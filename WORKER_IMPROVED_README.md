# Worker Improved - Documentação

## Visão Geral

O `Worker_improved` foi refatorado para usar **exclusivamente handlers específicos**, removendo o sistema de fallback e melhorando significativamente o sistema de logs e monitoramento.

## Principais Melhorias

### ✅ **Arquitetura Simplificada**
- Removido sistema de fallback (métodos tradicionais)
- Uso exclusivo de classes handlers
- Código mais limpo e manutenível

### ✅ **Sistema de Logs Aprimorado**
- Logs estruturados com timestamps
- Diferentes níveis de log (info, debug, error, warning)
- Contadores de sucesso/falha
- Resumo de execução detalhado
- Saída otimizada para cron jobs

### ✅ **Validação Robusta**
- Validação de handlers antes da execução
- Verificação de interfaces implementadas
- Tratamento de erros mais específico
- Mensagens de erro mais informativas

### ✅ **Monitoramento Melhorado**
- Contadores de jobs processados
- Tempo de execução rastreado
- Alertas para falhas
- Logs compatíveis com ferramentas de monitoramento

## Estrutura do Worker

```php
class Worker_improved extends CI_Controller
{
    // Mapeamento de tipos para handlers
    private $handler_classes = [
        'alteracao_status_massa' => 'AlteracaoStatusMassaHandler',
        'alteracao_status_by_ncm' => 'AlteracaoStatusMassaHandler',
        'homologacao_massa' => 'HomologacaoMassaHandler',
    ];
    
    // Contadores para monitoramento
    private $successful_jobs = 0;
    private $failed_jobs = 0;
    private $execution_start_time;
}
```

## Fluxo de Execução

1. **Inicialização**: Carrega modelos e configura logs
2. **Busca Jobs**: Obtém jobs pendentes da fila
3. **Processamento**: Para cada job:
   - Valida payload JSON
   - Identifica handler apropriado
   - Carrega e valida handler
   - Executa processamento
   - Registra resultado
4. **Finalização**: Exibe resumo e estatísticas

## Exemplo de Saída

```
[2025-01-11 10:30:00] Iniciando processamento da fila (limite: 10)
Encontrados 3 jobs para processar
Processando job #123
✓ Job #123 (homologacao_massa) concluído com sucesso
Processando job #124
✓ Job #124 (alteracao_status_massa) concluído com sucesso
Processando job #125
✗ Falha no job #125: Payload inválido para o tipo homologacao_massa
Execução finalizada em: 2025-01-11 10:30:15
Total de jobs processados: 3
Sucessos: 2
Falhas: 1
⚠ ATENÇÃO: 1 jobs falharam. Verifique os logs para detalhes.
```

## Como Usar

### Via CLI
```bash
cd /path/to/project
php index.php cli/worker_improved process_email_queue [limite]
```

### Via Cron
```bash
# Executar a cada 5 minutos
*/5 * * * * cd /path/to/project && php index.php cli/worker_improved process_email_queue 50 >> /var/log/worker.log 2>&1
```

### Com Script de Controle
```bash
# Usar o script exemplo_cron_worker.sh
*/5 * * * * /path/to/exemplo_cron_worker.sh
```

## Adicionando Novos Handlers

1. **Criar a classe handler**:
```php
// application/handlers/MeuNovoHandler.php
class MeuNovoHandler implements EmailHandlerInterface
{
    public function handle(array $payload) { /* ... */ }
    public function validatePayload(array $payload) { /* ... */ }
    public function getType() { return 'meu_novo_tipo'; }
}
```

2. **Registrar no worker**:
```php
private $handler_classes = [
    // ... handlers existentes
    'meu_novo_tipo' => 'MeuNovoHandler',
];
```

3. **Usar na aplicação**:
```php
$payload = [
    'type' => 'meu_novo_tipo',
    'dados' => $meus_dados
];
$this->email_queue_model->add_to_queue($payload);
```

## Logs e Monitoramento

### Níveis de Log
- **INFO**: Início/fim de execução, estatísticas
- **DEBUG**: Detalhes de processamento de jobs
- **ERROR**: Falhas em jobs e handlers
- **WARNING**: Situações que requerem atenção

### Arquivos de Log
- **CodeIgniter Logs**: `application/logs/log-YYYY-MM-DD.php`
- **Cron Logs**: Configurável no script de cron

### Monitoramento Recomendado
- Verificar logs de erro regularmente
- Monitorar taxa de falhas
- Alertar se worker não executar por muito tempo
- Verificar crescimento da fila

## Troubleshooting

### Job Falha Constantemente
1. Verificar logs detalhados
2. Validar payload do job
3. Testar handler isoladamente
4. Verificar dependências (models, services)

### Worker Não Processa Jobs
1. Verificar se cron está executando
2. Verificar permissões de arquivo
3. Verificar configuração do banco
4. Verificar logs do sistema

### Performance Lenta
1. Ajustar limite de jobs por execução
2. Otimizar handlers específicos
3. Verificar queries do banco
4. Considerar paralelização

## Migração do Worker Antigo

O worker antigo (`worker.php`) ainda existe para compatibilidade, mas recomenda-se migrar para o `worker_improved.php`:

1. Testar handlers existentes
2. Atualizar cron jobs
3. Monitorar execução
4. Remover worker antigo quando estável

## Configurações Recomendadas

### Cron Job
```bash
# A cada 5 minutos, processar até 50 jobs
*/5 * * * * cd /path/to/project && timeout 300 php index.php cli/worker_improved process_email_queue 50 >> /var/log/worker.log 2>&1
```

### Monitoramento
- Configurar alertas para falhas > 10%
- Verificar logs diariamente
- Monitorar tamanho da fila
- Backup regular dos logs importantes
