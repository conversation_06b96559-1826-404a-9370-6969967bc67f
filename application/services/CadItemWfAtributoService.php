<?php

namespace App\Services;

// application/services/CadItemWfAtributoService.php
defined('BASEPATH') || exit('No direct script access allowed');

class CadItemWfAtributoService
{
    /**
     * The CodeIgniter super-object.
     *
     * @var CI_Controller & object{load: CI_Loader, cad_item_wf_atributo_model: Cad_item_wf_atributo_model, email: CI_Email, atributo: Atributo}
     */
    protected $CI;

    public function __construct()
    {
        $this->CI = &get_instance();
        $this->CI->load->model('cad_item_wf_atributo_model');
        $this->CI->load->model('usuario_model');
        $this->CI->load->library("Item/Atributo");
    }

    /**
     * Envia e-mail de alteração de status em massa para itens.
     * @param array $item_ids
     * @param int $id_empresa
     * @param int $status_id
     * @throws Exception
     */
    public function enviarAlteracaoStatusMassa($item_ids, $id_empresa, $status_id)
    {
        $items = $this->CI->cad_item_wf_atributo_model
            ->get_itens_by_ids($item_ids);

        $part_numbers = array_column($items, 'part_number');
        $estabelecimentos = array_column($items, 'estabelecimento');

        // Buscar dados necessários para o email
        $usuarios = $this->CI->cad_item_wf_atributo_model
            ->get_usuarios_emails($part_numbers, $estabelecimentos, $id_empresa);

        $itens = $this->CI->cad_item_wf_atributo_model
            ->get_itens($part_numbers, $estabelecimentos, $id_empresa);

        $desc_status = $this->CI->atributo->get_status($status_id);

        // Enviar emails para cada usuário
        if (empty($usuarios)) {
            return;
        }
        
        foreach ($usuarios as $usuario) {
            $this->enviarEmailAlteracaoStatus($usuario, $itens, $desc_status);
        }
    }

    /**
     * Envia email individual de alteração de status.
     * @param object $usuario
     * @param array $itens
     * @param string $desc_status
     * @return bool
     */
    private function enviarEmailAlteracaoStatus($usuario, $itens, $desc_status)
    {
        $qtd_itens = count($itens);
        $url = config_item('online_url') . '/wf/atributos';

        // Construir tabela de itens
        $tabela_itens = $this->construirTabelaItens($itens, $usuario);

        $html_message = $this->construirMensagemEmail($usuario, $desc_status, $qtd_itens, $tabela_itens, $url);

        $temp_data = [
            'base_url' => config_item('online_url'),
            'html_message' => $html_message
        ];

        $titulo = '[Gestão Tarifária] - Alterações de Status dos atributos nos itens';
        $titulo = mb_convert_encoding($titulo, 'UTF-8', 'auto');

        $body = $this->CI->load->view('templates/basic_template', $temp_data, true);

        return $this->enviarEmail(
            $usuario->email,
            $titulo,
            $body
        );
    }

    /**
     * Constrói a tabela HTML dos itens para o email.
     * @param array $itens
     * @param object $usuario
     * @return string
     */
    private function construirTabelaItens($itens, $usuario)
    {
        $tabela_itens = '<table border="1" style="border-collapse: collapse; width: 100%;">';
        $tabela_itens .= '<tr><th>Part Number</th><th>Estabelecimento</th><th>Descrição</th><th>NCM</th></tr>';

        foreach ($itens as $item) {
            if ($item->id_resp_engenharia == $usuario->id_resp_engenharia) {
                $tabela_itens .= '<tr>';
                $tabela_itens .= '<td>' . $item->part_number . '</td>';
                $tabela_itens .= '<td>' . $item->estabelecimento . '</td>';
                $tabela_itens .= '<td>' . $item->descricao . '</td>';
                $tabela_itens .= '<td>' . $item->ncm_proposto . '</td>';
                $tabela_itens .= '</tr>';
            }
        }

        $tabela_itens .= '</table>';
        return $tabela_itens;
    }

    /**
     * Constrói a mensagem HTML do email.
     * @param object $usuario
     * @param string $desc_status
     * @param int $qtd_itens
     * @param string $tabela_itens
     * @param string $url
     * @return string
     */
    private function construirMensagemEmail($usuario, $desc_status, $qtd_itens, $tabela_itens, $url)
    {
        return '
            <h3>Itens com alteração de status para ' . $desc_status . ':</h3>
            <br>
            <p>Olá, ' . $usuario->nome . '!</p>
            <p>Os seguintes itens foram alterados no portal, verifique abaixo as informações: </p>
            <div class="panel panel-default">
                <div class="panel-body" style="padding: 20px;border-left: 3px solid #ccc;background: #FBFBFB;">
                    <p><strong>Quantidade de itens: </strong> ' . $qtd_itens . '</p>
                    ' . $tabela_itens . '
                </div>
            </div>
            <p style="text-align: right;"><a href="' . $url . '">Clique aqui para acompanhar no portal</a></p>
        ';
    }

    /**
     * Envia o email usando a biblioteca do CodeIgniter.
     * @param string $to
     * @param string $subject
     * @param string $body
     * @return bool
     */
    private function enviarEmail($to, $subject, $body)
    {
        $this->CI->load->library('email');

        $this->CI->email->from(config_item('mail_from_addr'), config_item('mail_from_name'));
        $this->CI->email->to($to);
        $this->CI->email->subject($subject);
        $this->CI->email->message($body);

        return $this->CI->email->send();
    }
}
