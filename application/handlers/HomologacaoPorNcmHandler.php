<?php

namespace App\Handlers;

// application/handlers/HomologacaoPorNcmHandler.php
defined('BASEPATH') || exit('No direct script access allowed');

use Exception;
use App\Handlers\EmailHandlerInterface;
use App\Exceptions\EmailQueueException;

/**
 * Handler específico para homologação por NCM (ajax_set_status).
 *
 * Esta classe implementa a lógica específica para processar jobs
 * de homologação de itens individuais ou por NCM de forma assíncrona.
 */
class HomologacaoPorNcmHandler implements EmailHandlerInterface
{
    /**
     * The CodeIgniter super-object.
     *
     * @var CI_Controller & object{load: CI_Loader}
     */
    protected $CI;

    public function __construct()
    {
        $this->CI = &get_instance();
        $this->CI->load->model([
            'log_wf_atributos_model',
            'cad_item_wf_atributo_model',
            'usuario_model',
            'empresa_model',
            'email_queue_model'
        ]);
        $this->CI->load->library('email');
    }

    /**
     * {@inheritdoc}
     */
    public function handle(array $payload)
    {
        if (!$this->validatePayload($payload)) {
            throw new EmailQueueException('Payload inválido para o tipo ' . $this->getType());
        }

        try {
            // Processa a homologação
            $this->processarHomologacaoPorNcm($payload);

            // Envia email de notificação de conclusão
            // Retirado o envio de email com aviso para o usuário logado, a pedido de Douglas.
            // $this->enviarEmailHomologacaoConcluida($payload);

            return true;
        } catch (Exception $e) {
            // Em caso de erro, envia email de falha
            $this->enviarEmailHomologacaoFalha($payload, $e->getMessage());
            throw new EmailQueueException('Erro ao processar homologação por NCM: ' . $e->getMessage());
        }
    }

    /**
     * {@inheritdoc}
     */
    public function validatePayload(array $payload)
    {
        $required_fields = [
            'status',
            'id_empresa',
            'id_usuario',
            'justificativa',
            'user_email'
        ];

        // Verifica se todos os campos obrigatórios existem
        foreach ($required_fields as $field) {
            if (!isset($payload[$field])) {
                return false;
            }
        }

        // Validações específicas
        $valid_status = is_array($payload['status'])
            && isset($payload['status']['slug'])
            && isset($payload['status']['id']);

        $valid_empresa = is_numeric($payload['id_empresa']);
        $valid_usuario = is_numeric($payload['id_usuario']);
        $valid_email = filter_var($payload['user_email'], FILTER_VALIDATE_EMAIL);

        // Deve ter pelo menos id_item ou ncm
        $has_items = !empty($payload['id_item']) || !empty($payload['ncm']);

        return $valid_status && $valid_empresa && $valid_usuario && $valid_email && $has_items;
    }

    /**
     * {@inheritdoc}
     */
    public function getType()
    {
        return 'homologacao_por_ncm';
    }

    /**
     * Processa a homologação por NCM.
     */
    private function processarHomologacaoPorNcm($payload)
    {
        $status = $payload['status'];
        $id_item = $payload['id_item'] ?? null;
        $part_numbers = $payload['part_numbers'] ?? null;
        $estabelecimentos = $payload['estabelecimentos'] ?? null;
        $id_empresa = $payload['id_empresa'];
        $ncm = $payload['ncm'] ?? null;
        $id_usuario = $payload['id_usuario'];
        $justificativa = $payload['justificativa'];
        $itens_existentes = $payload['itens_existentes'] ?? null;

        // Executar set_status
        $this->CI->cad_item_wf_atributo_model->set_status(
            $status['slug'],
            $id_item,
            $part_numbers,
            $estabelecimentos,
            $id_empresa,
            $ncm
        );

        // Processar itens por NCM se necessário
        if (!empty($ncm) && empty($id_item)) {
            $this->CI->cad_item_wf_atributo_model->set_state_store_session(true);
            $this->CI->cad_item_wf_atributo_model->restore_state_from_session('filter.', 'post');

            $empresa = $this->CI->empresa_model->get_entry($id_empresa);
            $itens = $this->CI->cad_item_wf_atributo_model->get_itens_validados_movimentacao($ncm, $empresa, $id_empresa);

            $part_numbers = [];
            $estabelecimentos = [];
            foreach ($itens as $item) {
                $part_numbers[] = $item->part_number;
                $estabelecimentos[] = $item->estabelecimento;
            }
        }

        // Registrar log
        $this->CI->log_wf_atributos_model->registrar_log(
            $id_item,
            $part_numbers,
            $estabelecimentos,
            $id_empresa,
            $status['id'],
            'movimentacao_manual',
            $id_usuario,
            $justificativa
        );

        if (!is_array($id_item)) {
            $id_item = [$id_item];
        }

        // Registrar email de alteração de status na fila
        $email_payload = [
            'type' => 'alteracao_status_by_ncm',
            'item_ids' => $id_item,
            'id_empresa' => $id_empresa,
            'status_id' => $status['id']
        ];
        $this->CI->email_queue_model->add_to_queue($email_payload);
    }

    /**
     * Envia email de notificação de homologação concluída.
     */
    private function enviarEmailHomologacaoConcluida($payload)
    {
        $usuario = $this->CI->usuario_model->get_by_id($payload['id_usuario']);
        $total_itens = $this->calcularTotalItens($payload);

        $email_data = [
            'base_url' => config_item('online_url'),
            'usuario_nome' => $payload['user_nome'] ?? ($usuario->nome ?? 'Usuário'),
            'total_itens' => $total_itens,
            'status_novo' => $payload['status']['desc'],
            'justificativa' => $payload['justificativa'],
            'data_processamento' => date('d/m/Y H:i:s'),
            'tipo_processamento' => 'individual'
        ];

        $body = $this->CI->load->view('templates/homologacao_individual_concluida', $email_data, TRUE);

        $this->CI->email->from(config_item('mail_from_addr'), config_item('mail_from_name'));
        $this->CI->email->to($payload['user_email']);
        $this->CI->email->subject('[Gestão Tarifária] - Alteração de status concluída');
        $this->CI->email->message($body);

        $this->CI->email->send();
    }

    /**
     * Envia email de notificação de falha na homologação.
     */
    private function enviarEmailHomologacaoFalha($payload, $erro)
    {
        $usuario = $this->CI->usuario_model->get_by_id($payload['id_usuario']);
        $total_itens = $this->calcularTotalItens($payload);

        $email_data = [
            'base_url' => config_item('online_url'),
            'usuario_nome' => $payload['user_nome'] ?? ($usuario->nome ?? 'Usuário'),
            'total_itens' => $total_itens,
            'erro' => $erro,
            'data_processamento' => date('d/m/Y H:i:s'),
            'tipo_processamento' => 'individual'
        ];

        $body = $this->CI->load->view('templates/homologacao_individual_falha', $email_data, TRUE);

        $this->CI->email->from(config_item('mail_from_addr'), config_item('mail_from_name'));
        $this->CI->email->to($payload['user_email']);
        $this->CI->email->subject('[Gestão Tarifária] - Falha na alteração de status');
        $this->CI->email->message($body);

        $this->CI->email->send();
    }

    /**
     * Calcula o total de itens processados
     */
    private function calcularTotalItens($payload)
    {
        if (!empty($payload['id_item'])) {
            return is_array($payload['id_item']) ? count($payload['id_item']) : 1;
        }

        if (!empty($payload['itens_existentes'])) {
            return count($payload['itens_existentes']);
        }

        // Se for por NCM, não temos como saber exatamente quantos itens foram afetados
        // sem fazer uma nova consulta, então retornamos uma estimativa
        return 1;
    }
}
