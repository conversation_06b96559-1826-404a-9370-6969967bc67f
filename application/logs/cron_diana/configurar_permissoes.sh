#!/bin/bash

# Script para configurar permissões do sistema de logs DIANA
# Execute este script como root ou com sudo se necessário

echo "=== CONFIGURANDO PERMISSÕES - SISTEMA DE LOGS DIANA ==="
echo ""

# Diretório base da aplicação (ajuste conforme necessário)
APP_DIR="/var/www/gestaotarifaria/production/current"
LOG_DIR="$APP_DIR/application/logs/cron_diana"

echo "1. Verificando diretório de logs..."
if [ ! -d "$LOG_DIR" ]; then
    echo "   Criando diretório de logs..."
    mkdir -p "$LOG_DIR"
fi

echo "2. Configurando permissões do diretório..."
chmod 777 "$LOG_DIR"
echo "   Permissões definidas: 777 (rwxrwxrwx)"

echo "3. Verificando proprietário..."
# Detectar usuário do servidor web
WEB_USER=""
if [ -f /etc/passwd ]; then
    WEB_USER=$(grep -E "^www-data|^apache|^nginx|^http" /etc/passwd | head -1 | cut -d: -f1)
fi

if [ -n "$WEB_USER" ]; then
    echo "   Usuário do servidor web detectado: $WEB_USER"
    chown "$WEB_USER" "$LOG_DIR"
    echo "   Proprietário alterado para: $WEB_USER"
else
    echo "   Usuário do servidor web não detectado automaticamente"
    echo "   Mantendo proprietário atual"
fi

echo "4. Verificando permissões finais..."
ls -la "$LOG_DIR"

echo ""
echo "5. Testando escrita no diretório..."
TEST_FILE="$LOG_DIR/test_permissions.log"
if echo "Teste de permissões - $(date)" > "$TEST_FILE" 2>/dev/null; then
    echo "   ✓ Teste de escrita bem-sucedido"
    rm -f "$TEST_FILE"
else
    echo "   ✗ Falha no teste de escrita"
    echo "   Verifique as permissões manualmente"
fi

echo ""
echo "=== CONFIGURAÇÃO CONCLUÍDA ==="
echo ""
echo "Para verificar se está funcionando, execute:"
echo "  php $APP_DIR/index.php cli/cron_analise_diana test_config"
echo ""
echo "Para visualizar logs:"
echo "  php $APP_DIR/index.php cli/cron_analise_diana logs"
echo ""
echo "Se ainda houver problemas de permissão:"
echo "1. Verifique se o usuário que executa a cron tem acesso ao diretório"
echo "2. Execute: sudo chown -R [usuario_cron]:[grupo_cron] $LOG_DIR"
echo "3. Execute: sudo chmod -R 777 $LOG_DIR" 