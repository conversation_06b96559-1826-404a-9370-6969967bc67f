# Sistema de Logs Estruturado - Cron DIANA

## Visão Geral

Este diretório contém os logs estruturados das execuções da cron de análise DIANA. Cada execução gera um arquivo de log único com informações detalhadas sobre o processamento.

## Estrutura dos Arquivos

Os arquivos de log seguem o padrão:

```
diana_execution_YYYY-MM-DD_HH-MM-SS_[execution_id].log
```

Exemplo:

```
diana_execution_2025-01-15_14-30-25_a1b2c3d4e5f6.log
```

## Formato do Log

Cada linha do log contém:

```
[YYYY-MM-DD HH:MM:SS] [LEVEL  ] [ELAPSEDms] MESSAGE | Context: {...}
```

### Campos:

- **Timestamp**: Data e hora da entrada
- **Level**: Nível do log (DEBUG, INFO, WARNING, ERROR)
- **Elapsed**: Tempo decorrido desde o início da execução (em milissegundos)
- **Message**: Mensagem principal
- **Context**: Contexto adicional em JSON (opcional)

### Exemplo:

```
[2025-01-15 14:30:25] [INFO   ] [    0.00ms] === SISTEMA DE LOG INICIALIZADO ===
[2025-01-15 14:30:25] [INFO   ] [    5.20ms] API DIANA configurada | Context: {"url":"https://api.diana.com"}
[2025-01-15 14:30:26] [INFO   ] [  125.50ms] Empresas encontradas para processamento | Context: {"total_empresas":3}
[2025-01-15 14:30:27] [DEBUG  ] [  250.30ms] Processando item pendente | Context: {"part_number":"ABC123","empresa_id":1}
[2025-01-15 14:30:28] [INFO   ] [  350.75ms] Item processado com sucesso | Context: {"part_number":"ABC123","status_triagem":"aprovada"}
[2025-01-15 14:30:29] [ERROR  ] [  450.20ms] Falha na chamada da API DIANA | Context: {"part_number":"XYZ789","empresa_id":2}
```

## Níveis de Log

- **DEBUG**: Informações detalhadas para desenvolvimento
- **INFO**: Informações gerais sobre o progresso
- **WARNING**: Avisos que não impedem a execução
- **ERROR**: Erros que afetam o processamento

## Comandos Disponíveis

### Visualizar logs mais recentes

```bash
php index.php cli/cron_analise_diana logs
```

### Visualizar log específico

```bash
php index.php cli/cron_analise_diana logs [execution_id]
```

### Visualizar últimas N linhas

```bash
php index.php cli/cron_analise_diana logs [execution_id] [lines]
```

### Listar todas as execuções

```bash
php index.php cli/cron_analise_diana list_logs
```

### Limpar logs antigos (automático)

```bash
php index.php cli/cron_analise_diana cleanup
```

## Rotação Automática

- Logs com mais de 30 dias são automaticamente removidos
- A limpeza é executada junto com o comando `cleanup`
- Cada execução gera um arquivo único para facilitar o rastreamento

## Contexto Estruturado

O sistema captura automaticamente informações contextuais como:

- IDs de execução
- IDs de empresas
- Part numbers dos itens
- Estatísticas de processamento
- Detalhes de erros
- Tempos de execução

## Vantagens do Sistema

1. **Rastreabilidade**: Cada execução tem seu próprio arquivo
2. **Estruturação**: Logs organizados com níveis e contexto
3. **Performance**: Tempo decorrido para análise de performance
4. **Debugging**: Informações detalhadas para troubleshooting
5. **Manutenção**: Rotação automática de logs antigos
6. **Compatibilidade**: Mantém compatibilidade com o sistema existente

## Monitoramento

Para monitorar as execuções em produção:

1. Configure alertas para logs com nível ERROR
2. Monitore o tamanho do diretório de logs
3. Verifique regularmente a taxa de sucesso
4. Analise logs de execuções com muitos erros

## Troubleshooting

### Erro de Permissão

Se você receber erro "Permission denied" ao tentar escrever logs:

```bash
# Configurar permissões automaticamente
./application/logs/cron_diana/configurar_permissoes.sh

# Ou manualmente:
chmod 777 application/logs/cron_diana/
chown www-data:www-data application/logs/cron_diana/  # Ajuste o usuário conforme necessário
```

### Log não encontrado

- Verifique se o diretório `logs/cron_diana/` existe
- Confirme se a execução foi iniciada corretamente
- Use `list_logs` para ver todas as execuções disponíveis

### Log muito grande

- Use o parâmetro `lines` para limitar a saída
- Considere aumentar a frequência de limpeza automática
- Monitore o espaço em disco

### Performance

- Logs DEBUG são desabilitados em produção por padrão
- O sistema usa lock de arquivo para evitar corrupção
- Logs são escritos de forma assíncrona
