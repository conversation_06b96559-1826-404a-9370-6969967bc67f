#!/bin/bash

# Script de exemplo para demonstrar o uso do sistema de logs estruturado
# da cron DIANA

echo "=== SISTEMA DE LOGS ESTRUTURADO - CRON DIANA ==="
echo ""

# Diretório base da aplicação (ajuste conforme necessário)
APP_DIR="/var/www/gestaotarifaria/production/current"

# 1. Executar a cron normalmente
echo "1. Executando cron DIANA..."
cd $APP_DIR
php index.php cli/cron_analise_diana

echo ""
echo "2. Listando execuções disponíveis..."
php index.php cli/cron_analise_diana list_logs

echo ""
echo "3. Visualizando logs da execução mais recente..."
php index.php cli/cron_analise_diana logs

echo ""
echo "4. Visualizando apenas as últimas 10 linhas..."
php index.php cli/cron_analise_diana logs "" 10

echo ""
echo "5. Verificando status das execuções..."
php index.php cli/cron_analise_diana status

echo ""
echo "=== COMANDOS ÚTEIS ==="
echo ""
echo "Para visualizar log específico:"
echo "  php index.php cli/cron_analise_diana logs [execution_id]"
echo ""
echo "Para visualizar últimas N linhas:"
echo "  php index.php cli/cron_analise_diana logs [execution_id] [lines]"
echo ""
echo "Para listar todas as execuções:"
echo "  php index.php cli/cron_analise_diana list_logs"
echo ""
echo "Para limpar logs antigos:"
echo "  php index.php cli/cron_analise_diana cleanup"
echo ""
echo "Para testar configuração:"
echo "  php index.php cli/cron_analise_diana test_config"
echo ""
echo "=== MONITORAMENTO ==="
echo ""
echo "Para monitorar logs em tempo real:"
echo "  tail -f application/logs/cron_diana/diana_execution_*.log"
echo ""
echo "Para buscar erros específicos:"
echo "  grep 'ERROR' application/logs/cron_diana/diana_execution_*.log"
echo ""
echo "Para analisar performance:"
echo "  grep 'ANÁLISE DIANA FINALIZADA' application/logs/cron_diana/diana_execution_*.log" 