# Solução para Erro de Permissão - Sistema de Logs DIANA

## Problema

```
A PHP Error was encountered
Severity: Warning
Message: file_put_contents(application/logs/cron_diana/diana_execution_*.log): failed to open stream: Permission denied
```

## Solução Rápida

### 1. Configuração Automática

```bash
# Execute o script de configuração
./application/logs/cron_diana/configurar_permissoes.sh
```

### 2. Configuração Manual

```bash
# Definir permissões corretas
chmod 777 application/logs/cron_diana/

# Definir proprietário correto (ajuste conforme necessário)
chown www-data:www-data application/logs/cron_diana/
# ou
chown apache:apache application/logs/cron_diana/
# ou
chown nginx:nginx application/logs/cron_diana/
```

## Verificação

### 1. Verificar Permissões Atuais

```bash
ls -la application/logs/cron_diana/
```

**Resultado esperado:**

```
drwxrwxrwx 1 <USER> <GROUP> 114 Jul 27 22:28 .
```

### 2. Testar Escrita

```bash
# Teste simples
echo "teste" > application/logs/cron_diana/teste.log
cat application/logs/cron_diana/teste.log
rm application/logs/cron_diana/teste.log
```

## Cenários Comuns

### Cenário 1: Desenvolvimento Local

```bash
# Se você está executando como usuário local
chown -R $USER:$USER application/logs/cron_diana/
chmod -R 755 application/logs/cron_diana/
```

### Cenário 2: Servidor Web (Apache/Nginx)

```bash
# Descobrir usuário do servidor web
ps aux | grep apache
ps aux | grep nginx
ps aux | grep httpd

# Configurar permissões
chown -R www-data:www-data application/logs/cron_diana/
chmod -R 755 application/logs/cron_diana/
```

### Cenário 3: Cron Job

```bash
# Se executado via cron, verificar usuário da cron
crontab -l

# Configurar permissões para o usuário da cron
chown -R [usuario_cron]:[grupo_cron] application/logs/cron_diana/
chmod -R 777 application/logs/cron_diana/
```

### Cenário 4: Docker/Container

```bash
# Se executado em container
chown -R 1000:1000 application/logs/cron_diana/
chmod -R 777 application/logs/cron_diana/
```

## Troubleshooting Avançado

### 1. Verificar Usuário Atual

```bash
# Verificar quem está executando
whoami
id

# Verificar usuário do PHP
php -r "echo 'PHP User: ' . get_current_user() . PHP_EOL;"
```

### 2. Verificar SELinux (se aplicável)

```bash
# Verificar status do SELinux
sestatus

# Se ativo, configurar contexto
semanage fcontext -a -t httpd_rw_content_t "application/logs/cron_diana(/.*)?"
restorecon -Rv application/logs/cron_diana/
```

### 3. Verificar ACLs

```bash
# Verificar ACLs
getfacl application/logs/cron_diana/

# Configurar ACL se necessário
setfacl -R -m u:www-data:rwx application/logs/cron_diana/
```

## Comandos de Diagnóstico

### 1. Verificar Estrutura Completa

```bash
# Verificar toda a estrutura de logs
ls -la application/logs/
ls -la application/logs/cron_diana/
```

### 2. Testar Criação de Arquivo

```bash
# Teste de criação
touch application/logs/cron_diana/teste_$(date +%s).log
ls -la application/logs/cron_diana/teste_*.log
rm application/logs/cron_diana/teste_*.log
```

### 3. Verificar Espaço em Disco

```bash
# Verificar espaço disponível
df -h application/logs/cron_diana/
```

## Prevenção

### 1. Script de Configuração Automática

O script `configurar_permissoes.sh` deve ser executado:

- Após deploy
- Após mudanças de usuário
- Após reinstalação do sistema

### 2. Monitoramento

```bash
# Adicionar ao crontab para verificação periódica
0 */6 * * * /path/to/application/logs/cron_diana/configurar_permissoes.sh
```

### 3. Logs de Sistema

```bash
# Verificar logs do sistema para problemas de permissão
tail -f /var/log/syslog | grep "Permission denied"
```

## Contato

Se o problema persistir, verifique:

1. Logs do sistema operacional
2. Configuração do servidor web
3. Políticas de segurança do sistema
4. Configuração do PHP (safe_mode, open_basedir)
