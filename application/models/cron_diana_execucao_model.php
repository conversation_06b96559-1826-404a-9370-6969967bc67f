<?php

class Cron_diana_execucao_model extends MY_Model
{
    public $_table = 'cron_diana_execucao';

    const STATUS_INICIADA = 'iniciada';
    const STATUS_FINALIZADA = 'finalizada';
    const STATUS_ERRO = 'erro';
    const STATUS_INTERROMPIDA = 'interrompida';

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Verifica se existe uma execução em andamento
     * @return bool|object False se não há execução em andamento, objeto da execução se há
     */
    public function verificar_execucao_em_andamento()
    {
        // Buscar a última execução em andamento
        $this->db->where('status_execucao', self::STATUS_INICIADA);
        $this->db->order_by('data_inicio', 'DESC');
        $this->db->limit(1);
        $query = $this->db->get($this->_table);

        if ($query->num_rows() > 0) {
            $execucao = $query->row();

            // Verificar se a execução ultrapassou o tempo limite
            $tempo_limite = strtotime('-2 hours');
            $data_inicio = strtotime($execucao->data_inicio);

            if ($data_inicio < $tempo_limite) {
                // Execução muito antiga, marcar como interrompida
                $this->finalizar_execucao($execucao->id_execucao, [
                    'status_execucao' => self::STATUS_INTERROMPIDA,
                    'observacoes' => 'Execução interrompida por timeout (mais de 2 horas)'
                ]);
                return false;
            }
            
            // Verificar se o processo ainda está rodando (se PID foi registrado)
            if (!empty($execucao->pid_processo)) {
                if ($this->processo_ainda_rodando($execucao->pid_processo)) {
                    return $execucao;
                } else {
                    // Processo não está mais rodando, marcar como interrompida
                    $this->finalizar_execucao($execucao->id_execucao, [
                        'status_execucao' => self::STATUS_INTERROMPIDA,
                        'observacoes' => 'Processo interrompido inesperadamente (PID não encontrado)'
                    ]);
                    return false;
                }
            }

            return $execucao;
        }

        return false;
    }

    /**
     * Verifica se um processo ainda está rodando pelo PID
     * @param int $pid Process ID
     * @return bool
     */
    private function processo_ainda_rodando($pid)
    {
        if (empty($pid) || !is_numeric($pid)) {
            return false;
        }

        // No Linux/Unix, verificar se o processo existe
        if (function_exists('posix_kill')) {
            return posix_kill($pid, 0);
        }

        // Fallback: tentar usar ps command
        $output = shell_exec("ps -p $pid");
        return !empty($output) && strpos($output, (string)$pid) !== false;
    }

    /**
     * Inicia uma nova execução
     * @param array $dados_iniciais Dados opcionais para a execução
     * @return int ID da execução criada
     */
    public function iniciar_execucao($dados_iniciais = [])
    {
        $dados = array_merge([
            'data_inicio' => date('Y-m-d H:i:s'),
            'status_execucao' => self::STATUS_INICIADA,
            'pid_processo' => function_exists('getmypid') ? getmypid() : null
        ], $dados_iniciais);

        $this->db->insert($this->_table, $dados);
        return $this->db->insert_id();
    }

    /**
     * Atualiza dados de uma execução em andamento
     * @param int $id_execucao
     * @param array $dados
     * @return bool
     */
    public function atualizar_execucao($id_execucao, $dados)
    {
        $this->db->where('id_execucao', $id_execucao);
        return $this->db->update($this->_table, $dados);
    }

    /**
     * Finaliza uma execução
     * @param int $id_execucao
     * @param array $dados_finais
     * @return bool
     */
    public function finalizar_execucao($id_execucao, $dados_finais = [])
    {
        $dados = array_merge([
            'data_fim' => date('Y-m-d H:i:s'),
            'status_execucao' => self::STATUS_FINALIZADA
        ], $dados_finais);

        $this->db->where('id_execucao', $id_execucao);
        return $this->db->update($this->_table, $dados);
    }

    /**
     * Busca execuções recentes
     * @param int $limit
     * @return array
     */
    public function get_execucoes_recentes($limit = 10)
    {
        $this->db->order_by('data_inicio', 'DESC');
        $this->db->limit($limit);
        $query = $this->db->get($this->_table);
        return $query->result();
    }

    /**
     * Busca estatísticas das execuções
     * @param int $dias_atras Quantos dias atrás buscar
     * @return object
     */
    public function get_estatisticas_execucoes($dias_atras = 7)
    {
        $data_limite = date('Y-m-d H:i:s', strtotime("-{$dias_atras} days"));
        
        $this->db->select('
            COUNT(*) as total_execucoes,
            SUM(CASE WHEN status_execucao = "finalizada" THEN 1 ELSE 0 END) as finalizadas,
            SUM(CASE WHEN status_execucao = "erro" THEN 1 ELSE 0 END) as com_erro,
            SUM(CASE WHEN status_execucao = "interrompida" THEN 1 ELSE 0 END) as interrompidas,
            AVG(tempo_execucao_segundos) as tempo_medio_segundos,
            SUM(total_itens_processados) as total_itens,
            SUM(total_sucessos) as total_sucessos,
            SUM(total_erros) as total_erros
        ', false);
        
        $this->db->where('data_inicio >=', $data_limite);
        $query = $this->db->get($this->_table);
        
        return $query->row();
    }

    /**
     * Limpa execuções antigas (mais de 30 dias)
     * @return int Número de registros removidos
     */
    public function limpar_execucoes_antigas()
    {
        $data_limite = date('Y-m-d H:i:s', strtotime('-30 days'));
        
        $this->db->where('data_inicio <', $data_limite);
        $this->db->where('status_execucao !=', self::STATUS_INICIADA);
        
        // Contar antes de deletar
        $count = $this->db->count_all_results($this->_table, false);
        
        // Deletar
        $this->db->delete($this->_table);
        
        return $count;
    }
}
