<?php

class Cad_item_wf_atributo_model extends MY_Model
{

    public $_table = 'cad_item_wf_atributo';
    public $_atributosFiltrados = [];
    public function __construct()
    {
        $this->db->query("set sql_mode='';");
        parent::__construct();
        $this->_user_id = sess_user_id();
    }
    private $_user_id = NULL;

    private $attrs = [];
    public $_attrs_new = [];
    private $alteracoes = [];

    // public function get_all_entries()
    // {
    // 	$this->db->order_by('part_number', 'ASC');
    // 	$query = $this->db->get($this->_table);
    // 	return $query->result();
    // }

    // public function get_total_entries()
    // {
    // 	$total = $this->db->count_all_results($this->_table);
    // 	return $total;
    // }

    public function save_attrs($arr_attr, $item = null)
    {
        if (empty($arr_attr)) {
            throw new \Exception("Parametros indefinidos ou vazios.");
        }


        foreach ($arr_attr as $attr) {
            if (\is_string($attr)) {
                $attr = \json_decode($attr, TRUE); // Força a conversão de string para o tipo array.
            }

            $this->save_attr($attr, $item);
        }

        $data = $this->organizeDataByItem($this->alteracoes);
        $data_result = $this->formatChangeLog($data);
        $this->log_alteracao_attr($data_result);
    }

    public function attr_already_exists($data)
    {
        $this->db->where('id_item', $data['id_item']);
        $this->db->where('atributo', $data['atributo']);

        $query = $this->db->get('cad_item_attr', 1);

        if ($query->num_rows()) {
            $row = $query->row();

            if (!empty($data['id_grupo_tarifario']) && !empty($data['id_item'])) {
                $grp_tarif = $data['id_grupo_tarifario'];

                $this->db->where('id_item', $data['id_item']);
                $query = $this->db->get('cad_item');
                $cad_item = $query->row();


                $this->db->where('id_grupo_tarifario', $cad_item->id_grupo_tarifario);
                $query = $this->db->get('grupo_tarifario');
                $result_grupo_tarifario = $query->row();
                $ncm_grupo_tarifario = $result_grupo_tarifario->ncm_recomendada;

                // grupo_tarifario na cad_item
                $this->db->where('id_grupo_tarifario', $grp_tarif);
                $query = $this->db->get('grupo_tarifario');
                $result_grupo_tarifario = $query->row();
                $ncm_novo_grupo_tarifario = $result_grupo_tarifario->ncm_recomendada;

                if (!empty($ncm_grupo_tarifario) && !empty($ncm_novo_grupo_tarifario) && $ncm_grupo_tarifario != $ncm_novo_grupo_tarifario) {
                    $this->db->where('id_item', $data['id_item']);
                    $this->db->where("id_grupo_tarifario <> '{$grp_tarif}'", NULL, FALSE);
                    $this->db->delete('cad_item_attr');

                    $logs_delete = [
                        'id' => $row->id,
                        'codigo' => $row->codigo,
                        'apresentacao' => $row->apresentacao,
                        'descricao' => $row->descricao,
                        'atributo_pai' => $row->atributo_pai,
                        'atributo' => $row->atributo,
                        'obrigatorio' => $row->obrigatorio,
                        'id_item' => $row->id_item,
                        'id_grupo_tarifario' => $row->id_grupo_tarifario,
                        'id_usuario' => $row->id_usuario,
                        'modalidade' => $row->modalidade,
                        'criado_em' => $row->criado_em,
                        'atualizado_em' => $row->atualizado_em,
                        'ativo' => $row->ativo,
                        'tenant_id' => $row->tenant_id
                    ];


                    $this->db->insert('logs_delete_cad_item_attr', $logs_delete);
                } else {
                    $this->db->set('id_grupo_tarifario', $grp_tarif);
                    $this->db->from('cad_item_attr');
                    $this->db->where('id_item', $data['id_item']);
                    $this->db->where("id_grupo_tarifario <> '{$grp_tarif}'", NULL, FALSE);
                    $this->db->update('cad_item_attr attr');
                }
            }

            return $row;
        }

        return NULL;
    }

    public function insert_attr($data)
    {
        $this->db->insert('cad_item_attr', $data);
        return $this->db->insert_id();
    }

    public function update_attr($data)
    {
        $id = $data["id"];

        if ($this->get_state('filter.vinculacao')) {
            $this->db->where('(codigo IS NULL OR codigo = "")', NULL, FALSE);
        }

        $this->db->update('cad_item_attr', $data, ["id" => $id]);
        return $id;
    }

    public function save_attr(&$attr, $itens, $parent_attr = array(), $check_update = false)
    {
        $this->load->model('cad_item_attr_model');
        $this->load->model('cad_item_model');

        $new_id = $updated_id = null;

        if (empty($attr) || empty($itens)) {
            throw new \Exception("Os parametros informados sao incorretos ou insuficientes.");
        }

        if (!is_array($attr) || !is_array($itens)) {
            throw new \Exception("Os parametros informados nao atendem aos requisitos necessarios");
        }

        $itens = (!array_key_exists(0, $itens)) ? [$itens] : $itens;


        foreach ($itens as $k => $item) {
            // Se item for um objeto, converter para array
            if (is_object($item)) {
                $item = (array) $item;
            }

            if (empty($item['id_item'])) {
                continue;
            }

            $id_item = $item['id_item'];
            $ncm = !empty($attr['ncm']) ? $attr['ncm'] : null;

            if (empty($ncm) && !empty($item['id_item'])) {
                $this->db->select('ncm_proposto');
                $this->db->from('cad_item');
                $this->db->where('id_item', $item['id_item']);
                $ncm = $this->db->get()->row()->ncm_proposto;
            }

            if (empty($attr['dbdata'][$id_item])) {
                continue;
            }


            $codigo = $attr['dbdata'][$id_item]['codigo'];

            if (\is_array($attr['dbdata'][$id_item]['codigo'])) {
                $codigo = implode(",", $attr['dbdata'][$id_item]['codigo']);
            }

            // $item = !is_array($item) ? (array) $item : $item;
            $data = [
                "codigo"             => $codigo,
                "apresentacao"       => !empty($attr["nomeApresentacao"]) ? $attr["nomeApresentacao"] : null,
                "atributo"           => $attr["codigo"],
                "id_grupo_tarifario" => $item["id_grupo_tarifario"],
                "id_item"            => $item["id_item"],
                "atualizado_em"      => date("Y-m-d H:i:s"),
                "obrigatorio"        => $attr["obrigatorio"] ? 1 : 0,
                "id_usuario"         => $this->_user_id,
                "ativo"              => 1
            ];

            $data["atributo_pai"] = null;

            if (!empty($parent_attr)) {
                $data["atributo_pai"] = $parent_attr["codigo"];
            }

            if (!empty($parent_attr['atributo']['codigo'])) {
                $data["atributo_pai"] = $parent_attr['atributo']["codigo"];
            }

            $do_not_update = false;
            $value_not_empty = false;
            $jump_attr = false;

            $chave_attr = $item["id_item"] . $attr["codigo"];

            if (!in_array($chave_attr, $this->attrs)) {
                $this->attrs[] = $item["id_item"] . $attr["codigo"];

                // if ($this->get_state('filter.vinculacao')) {
                //     $check_update = true;
                //     $value_not_empty = true;
                // }
                if ($value_not_empty == true && empty($data["codigo"])) {
                    $jump_attr = true;
                }
                if ($attr['formaPreenchimento'] == 'COMPOSTO') {
                    $data['codigo'] = 1;
                }
                if ($jump_attr == false) {
                    $attr_exists = $this->attr_already_exists($data);

                    if (empty($attr_exists)) {
                        $data["criado_em"] = \date("Y-m-d H:i:s");
                        $new_id = $this->insert_attr($data);
                        $attr_exists = $this->attr_already_exists($data);

                        $this->alteracoes[] = [
                            'attr_exists' => $attr_exists,
                            'data' => $data,
                            'dominio' => $attr['dominio'],
                            'formaPreenchimento' => $attr['formaPreenchimento'],
                            'new_id' => $new_id
                        ];
                    } else {
                        $data['id'] = $attr_exists->id;
                        $updated_id = $this->update_attr($data);

                        $this->alteracoes[] = [
                            'attr_exists' => $attr_exists,
                            'data' => $data,
                            'dominio' => $attr['dominio'],
                            'formaPreenchimento' => $attr['formaPreenchimento'],
                            'updated_id' => $updated_id
                        ];
                    }
                }
            }

            $data["id"]     = $new_id ?: $updated_id; // Captura o ID alvo e atribui aos dados enviados.

            $attr["dbdata"][$id_item] = $data;                  // Atribui os dados enviados para o atributo.      

            if ($attr["atributoCondicionante"] == TRUE && !empty($attr['condicionados'])) {
                foreach ($attr["condicionados"] as &$cond) {

                    if (!empty($cond["atributo"]["condicionados"])) {
                        foreach ($cond["atributo"]["condicionados"] as &$cond_attr) {
                            $this->save_attr($cond_attr["atributo"], $item, $cond, TRUE);
                        }
                    }
                    if ($this->has_attr_cond($attr, $cond, $id_item)) {
                        $this->save_attr($cond["atributo"], $item, $attr, TRUE);
                    } else {
                        if ($check_update == TRUE && !empty($cond["atributo"]["dbdata"]["codigo"])) {
                            $this->save_attr($cond["atributo"], $item, $attr, TRUE);
                        }
                        $cond["atributo"]["dbdata"][$id_item] = ["codigo" => ""];
                    }
                }
            }

            if (\strtoupper($attr["formaPreenchimento"]) == "COMPOSTO") {
                foreach ($attr["listaSubatributos"] as &$sub_attr) {
                    $this->save_attr($sub_attr, $item, $attr, true);
                }
            }

            if (!empty($id_item)) {
                $this->cad_item_model->save_status_attr($id_item, $ncm, true);
            }
        }
    }

    public function organizeDataByItem($data)
    {
        $result = [];

        foreach ($data as $item) {
            $attr_exists = $item['attr_exists'];
            $new_data = $item['data'];
            $dominio = $item['dominio'];
            $formaPreenchimento = $item['formaPreenchimento'];
            $id_item = $attr_exists->id_item;
            $new_id = isset($item['new_id']) ? $item['new_id'] : null;

            if (!isset($result[$id_item])) {
                $result[$id_item] = [];
            }
            if ($attr_exists->codigo != $new_data['codigo']) {
                $result[$id_item][] = [
                    'atributo_anterior' => $attr_exists->atributo,
                    'codigo_anterior' => $attr_exists->codigo,
                    'atributo_posterior' => $new_data['atributo'],
                    'codigo_posterior' => $new_data['codigo'],
                    'apresentacao' => $new_data['apresentacao'],
                    'dominio'      => $dominio,
                    'formaPreenchimento' => $formaPreenchimento
                ];
            } elseif (($attr_exists->codigo == $new_data['codigo']) && (!empty($new_id))) {
                $result[$id_item][] = [
                    'atributo_anterior' => $attr_exists->atributo,
                    'codigo_anterior' => $attr_exists->codigo,
                    'atributo_posterior' => $new_data['atributo'],
                    'codigo_posterior' => $new_data['codigo'],
                    'apresentacao' => $new_data['apresentacao'],
                    'dominio'      => $dominio,
                    'formaPreenchimento' => $formaPreenchimento,
                    'new_id' => $new_id
                ];
            }
        }

        return $result;
    }

    public function formatChangeLog($groupedData)
    {
        $result = [];


        define("CODE_EMPTY", "(vazio)");


        foreach ($groupedData as $id_item => $changes) {
            if (!empty($changes)) {
 
                foreach ($changes as $change) {
                    $attribute           = $change['atributo_posterior'];
                    $old_value           = $change['codigo_anterior'];
                    $new_value           = $change['codigo_posterior'];
                    $apresentacao        = $change['apresentacao'];
                    $formaPreenchimento  = $change['formaPreenchimento'];
                    $descricao_anterior  = '';
                    $descricao_posterior = '';
                    $new_id_attribute    = isset($change['new_id']) ? $change['new_id'] : null;

                    if (!empty($change['dominio']))
                    {
                        foreach ($change['dominio'] as $dominio) {
                            if ($dominio['codigo'] == $old_value) {
                                $descricao_anterior = $dominio['descricao'];
                            }
                        }

                        // Lógica específica para LISTA_ESTATICA com múltiplos valores
                        if ($formaPreenchimento == 'LISTA_ESTATICA') {
                            $new_values = explode(',', $new_value);
                            $descricoes_posteriores = [];
                            foreach ($new_values as $val) {
                                foreach ($change['dominio'] as $dominio) {
                                    if (trim($val) == $dominio['codigo']) {
                                        $descricoes_posteriores[] = $dominio['descricao'];
                                    }
                                }
                            }
                            $descricao_posterior = implode(',', $descricoes_posteriores);
                        } else {
                            foreach ($change['dominio'] as $dominio) {
                                if (trim($new_value) == $dominio['codigo']) {
                                    $descricao_posterior = $dominio['descricao'];
                                }
                            }
                        }
                    } else {
                        if ($formaPreenchimento == 'BOOLEANO') {
                            $descricao_anterior = $old_value == '' ? CODE_EMPTY : ($old_value == 1 ? 'Sim' : 'Não');
                            $descricao_posterior = $new_value == '' ? CODE_EMPTY : ($new_value == 1 ? 'Sim' : 'Não');
                        } else {
                            $descricao_anterior = $old_value;
                            $descricao_posterior = $new_value;
                        }
                    }

                    if ($old_value !== $new_value  && $descricao_anterior !== $descricao_posterior) {
                        if (empty($descricao_anterior)) {
                            $descricao_anterior = CODE_EMPTY;
                        }
                        if (empty($descricao_posterior)) {
                            $descricao_posterior = CODE_EMPTY;
                        }
                        $result[$id_item][] = "{$attribute} - <b>{$apresentacao}</b> alterado de <b>'{$descricao_anterior}'</b> para <b>'{$descricao_posterior}'</b>";
                    } elseif (!empty($new_id_attribute)) {
                        $descricao_anterior = CODE_EMPTY;

                        if ((empty($descricao_posterior) && $descricao_posterior !== CODE_EMPTY) || ($descricao_anterior === $descricao_posterior)) {
                            // Não gravar log
                            continue;
                        }
                        $result[$id_item][] = "{$attribute} - <b>{$apresentacao}</b> alterado de <b>'{$descricao_anterior}'</b> para <b>'{$descricao_posterior}'</b>";
                    }
                }
            }
        }

        return $result;
    }


    public function log_alteracao_attr($data)
    {
        $this->load->model('cad_item_model');
        $this->load->model('item_model');

        foreach ($data as $id_item => $msg) {
            $item = $this->cad_item_model->get_entry($id_item);
            $result = $this->item_model->get_entry($item->part_number, $item->id_empresa, $item->estabelecimento);

            $status = $this->get_status_wf_atributos_by_id($result->wf_status_atributos);

            $data = [
                "part_number" => $item->part_number,
                "estabelecimento" => $item->estabelecimento,
                "id_empresa" => $item->id_empresa,
                "status_atual" => !empty($status->status) ? $status->status : null,
                "detalhes" => implode(PHP_EOL, $msg),
                "id_usuario" => $this->_user_id,
                "tipo" => 'alteracao_atributos'
            ];

            $this->db->insert('log_wf_atributos', $data);
        }
    }
    private function has_attr_cond($attr, $cond, $id_item)
    {
        $cond_op  = $cond["condicao"]["operador"]; // O operador para comparação.
        $cond_val = $cond["condicao"]["valor"];    // O valor para ser comparado.
        $cond_res = FALSE;                         // O resultado final da comparação.

        if (!empty($attr['multivalorado'])) {
            $attr_vals = $attr["dbdata"]["codigo"]; // Multivalorado.

            if (!\is_array($attr_vals)) {
                $attr_vals = explode(",", $attr_vals);
            }

            foreach ($attr_vals as $attr_val) {

                $cond_res = eval("return \"$attr_val\" $cond_op \"$cond_val\";");

                if ($cond_res) {
                    break;
                }
            }
        } else {
            $attr_val = $attr["dbdata"][$id_item]["codigo"]; // Valor simples.

            if (strtoupper($attr["formaPreenchimento"]) == 'BOOLEANO' && $attr_val > 0) {
                $attr_val = "true";
            }

            $cond_res = eval("return \"$attr_val\" $cond_op \"$cond_val\";");
        }

        return $cond_res;
    }

    public function fake_data($id_empresa)
    {
        $this->db->query("set sql_mode='';");
        $data = $this->db->query("
			SELECT
				COUNT(subquery.itens) AS total_itens,
				subquery.ncm,
				subquery.atributos,
				subquery.total_atributos_obrigatorios,
				subquery.total_atributos_nao_obrigatorios
			FROM (
				SELECT 
					cad.ncm_proposto AS ncm,
					cad.id_item AS itens,
					COUNT(ncm.codigo) AS atributos,
					SUM(CASE
						WHEN ncm.obrigatorio = 1 THEN 1
						ELSE 0
					END) AS total_atributos_obrigatorios,
					SUM(CASE
						WHEN ncm.obrigatorio = 0 THEN 1
						ELSE 0
					END) AS total_atributos_nao_obrigatorios
				FROM
					ncm_atributo ncm
					INNER JOIN
					cad_item AS cad ON cad.ncm_proposto = ncm.ncm
				WHERE
					cad.id_empresa = '{$id_empresa}'
				GROUP BY cad.ncm_proposto, cad.id_item
			) AS subquery
			GROUP BY subquery.ncm;
		");
        return $data->result();
    }

    public function getHistoricoItem($partnumber, $estabelecimento)
    {
        $this->db->where('cpp.id_empresa', sess_user_company());


        $this->db->where('cpp.part_number', $partnumber);

        if (!empty($estabelecimento)) {
            $this->db->where('cpp.estabelecimento', $estabelecimento);
        } else {
            $this->db->where("(cpp.estabelecimento = NULL OR cpp.estabelecimento = '')", null, false);
        };

        $this->db->select('cr.*,
            IF(cpp.pendente = 0, "não", "sim") as pendente,
            cpp.pergunta,
            if(EXISTS(SELECT * FROM (`ctr_anexo_resposta` ar) where ar.id_resposta = cr.id), 1, 0) as has_file,
            cg_novo.nome as grupo_nome,
            IFNULL(cg_novo.nome, "N/A") as grupo_nome_novo,
            rpr.id_responsavel as id_responsavel_pergunta,
            u.nome as nome_responsavel_pergunta,
            u.email as email_responsavel_pergunta', false);

        $this->db->join('ctr_resposta cr', 'cpp.id = cr.id_pergunta', 'left');
        $this->db->join('ctr_pergunta cp', 'cpp.id_pergunta = cp.id', 'left');
        $this->db->join('ctr_grupo_pergunta cgp', 'cp.id = cgp.id_pergunta', 'left');
        $this->db->join('ctr_grupo cg_novo', 'cpp.id_ctr_grupo = cg_novo.id', 'left');
        $this->db->join('rel_perguntas_responsavel rpr', 'cpp.id = rpr.id_ctr_pendencias', 'left');
        $this->db->join('usuario u', 'rpr.id_responsavel = u.id_usuario', 'left');

        $this->db->where("cpp.part_number IS NOT NULL ", null, false);

        $this->db->group_by('cpp.id');

        $query = $this->db->get('ctr_pendencias_pergunta cpp');

        return $query->result();
    }


    public function get_selected_itens_ncm($ncm, $limit = NULL, $offset = NULL, $atribuidos = FALSE, $total = NULL)
    {
        $this->cad_item_wf_atributo_model->set_state_store_session(TRUE);
        $this->load->model('empresa_model');
        if ($tipo_item = $this->get_state('filter.tipo_item')) {
            $tipo_item = $tipo_item;
        } else {
            $tipo_item = 'importado';
        }


        $id_empresa = sess_user_company();
        $empresa = $this->empresa_model->get_entry($id_empresa);
        $data['campos_adicionais'] = explode("|", $empresa->campos_adicionais);
        $hasPrioridade =  in_array('prioridade', $data['campos_adicionais']);
        $hasPnPrimarioSecundario = in_array('pn_primario_secundario', $data['campos_adicionais']);

        $this->db->select('ci.id_item, ci.part_number, ci.estabelecimento, i.descricao as descricao_curta, i.descricao_proposta_completa as descricao_completa, ci.descricao_mercado_local,
        ci.id_grupo_tarifario');

        $this->db->select('i.part_number, i.marca, i.material_constitutivo ');
        $status_preenchimento = $this->get_state('filter.status_preenchimento');
        if ($status_preenchimento) {
            $this->db->where_in("i.status_attr", $status_preenchimento);
        }
        $this->db->select('sa.color, sa.slug as status_wf, sai.slug as status_wf_integracao, i.id_status as status_fiscal');
        $this->db->select('sa.status as status_wf_descricao');


        if ($tipo_item == 'importado') {
            $this->db->select('sa.slug as status_wf, sai.slug as status_wf_integracao');
            $this->db->join('item i', 'ci.part_number = i.part_number AND ci.id_empresa = i.id_empresa AND ci.estabelecimento = i.estabelecimento ', 'inner');

            if ($this->db->table_exists('comex')) {
                $this->db->join('comex', 'comex.part_number_original = i.part_number AND comex.id_empresa = i.id_empresa AND comex.unidade_negocio = i.estabelecimento', 'inner');
                $this->db->where("comex.ind_ecomex = 'EI'"); 

                if ($data_ini_imp = $this->get_state('filter.data_inicio_importado_modal')) {
                    $data_ini_imp = DateTime::createFromFormat('d/m/Y', $data_ini_imp)->format('Y-m-d');
                    $this->db->where('DATE(comex.data_criacao) >=', $data_ini_imp);
                }
                
                if ($data_fim_imp = $this->get_state('filter.data_fim_importado_modal')) {
                    $data_fim_imp = DateTime::createFromFormat('d/m/Y', $data_fim_imp)->format('Y-m-d');
                    $this->db->where('DATE(comex.data_criacao) <=', $data_fim_imp);
                }
            }

            $this->db->join('status_wf_atributos sa', 'sa.id = i.wf_status_atributos ', 'left');
            $this->db->join('status_wf_atributos_integracao sai', 'sai.id = i.wf_status_integracao ', 'left');
            $this->db->where('ci.id_empresa', $id_empresa);
            $this->db->where('ci.ncm_proposto', $ncm);
        } else {
            $this->db->join('item i', 'ci.part_number = i.part_number AND ci.id_empresa = i.id_empresa AND ci.estabelecimento = i.estabelecimento ', 'inner');
            $this->db->join('status_wf_atributos sa', 'sa.id = i.wf_status_atributos ', 'left');
            $this->db->join('status_wf_atributos_integracao sai', 'sai.id = i.wf_status_integracao ', 'left');
            $this->db->where("(i.wf_status_atributos IS NULL OR i.wf_status_atributos = '1')");
            $this->db->where('ci.id_empresa', $id_empresa);
            $this->db->where('ci.ncm_proposto', $ncm);
        }



        if ($status_classificacao_fiscal = $this->get_state('filter.status_classificacao_fiscal')) {
            $this->db->where_in("i.id_status", $status_classificacao_fiscal);
        }

        $where_in = "";
        if ($search = $this->get_state('filter.search')) {
            $keywords = array_filter(preg_split('/\r\n|[\r\n]/', $search));

            if (count($keywords) > 1) {
                $adwhereplus = "";
                foreach ($keywords as $keyword) {
                    if ($keyword !== '') {
                        $keyword = str_replace("*", "%",  $keyword);
                        if ($hasPnPrimarioSecundario) {
                            $adwhereplus .= ' i.descricao LIKE "' . $keyword . '" OR i.part_number LIKE "' . trim($keyword) . '" OR i.pn_primario_mpn LIKE "' . trim($keyword) . '" OR i.pn_secundario_ipn LIKE "' . trim($keyword) . '" OR ';
                        } else {
                            $adwhereplus .= ' i.descricao LIKE "' . $keyword . '" OR i.part_number LIKE "' . trim($keyword) . '" OR ';
                        }
                    }
                }

                $adwhereplus = rtrim($adwhereplus, 'OR ');

                $where_in .= ' (' . $adwhereplus . ')';

                if (!empty($adwhereplus)) {
                    $this->db->where($where_in, NULL, FALSE);
                }
            } else if (strpos($search, "\n") !== false) {

                $search = str_replace(array("\t", "\r\n", "\s", "\n"), '#', $search);

                $keywords = explode('#', $search);
                $adwhereplus = "";
                $adwhere_go = "";

                foreach ($keywords as $keyword) {
                    if (empty($keyword)) {
                        continue;
                    }
                    $keyword = str_replace("*", "%", $keyword);

                    $keyword = rtrim($keyword, " ");

                    if ($keyword !== '') {
                        if ($hasPnPrimarioSecundario) {
                            $adwhereplus .= ' i.descricao LIKE "' . $keyword . '" OR i.part_number LIKE "' . trim($keyword) . '" OR i.pn_primario_mpn LIKE "' . trim($keyword) . '" OR i.pn_secundario_ipn LIKE "' . trim($keyword) . '" OR ';
                        } else {
                            $adwhereplus .= ' i.descricao LIKE "' . $keyword . '" OR i.part_number LIKE "' . trim($keyword) . '" OR ';
                        }
                    }
                }

                $adwhere_go = rtrim($adwhereplus, 'OR ');

                $where_in .= ' (' . $adwhere_go . ')';
                if (!empty($adwhereplus)) {
                    $this->db->where($where_in, NULL, FALSE);
                }
            } else {
                if ($where_in == '') {
                    if (strpos($search, '*') !== false) {


                        if (is_array($search)) {
                            $search = $search[0];
                        }


                        if (substr_count($search, '*') == 1) {

                            if (substr($search, 0, 1) === '*') {
                                $search = str_replace("*", "",  $search);
                                if ($hasPnPrimarioSecundario) {
                                    $where_in  .= ' (i.descricao LIKE "%' . $search . '" OR i.part_number LIKE "%' . $search . '" OR i.pn_primario_mpn LIKE "%' . $search . '" OR i.pn_secundario_ipn LIKE "%' . $search . '")';
                                } else {
                                    $where_in  .= ' (i.descricao LIKE "%' . $search . '" OR i.part_number LIKE "%' . $search . '")';
                                }
                            } else if (substr($search, -1) === '*') {
                                $search = str_replace("*", "",  $search);
                                if ($hasPnPrimarioSecundario) {
                                    $where_in  .= ' (i.descricao LIKE "' . $search . '%" OR i.part_number LIKE "' . $search . '%" OR i.pn_primario_mpn LIKE "' . $search . '%" OR i.pn_secundario_ipn LIKE "' . $search . '%")';
                                } else {
                                    $where_in  .= ' (i.descricao LIKE "' . $search . '%" OR i.part_number LIKE "' . $search . '%")';
                                }
                            } else {

                                $search = str_replace("*", "%", $search);

                                if ($hasPnPrimarioSecundario) {
                                    $where_in  .= ' (i.descricao LIKE "' . $search . '" OR i.part_number LIKE "' . $search . '" OR i.pn_primario_mpn LIKE "' . $search . '" OR i.pn_secundario_ipn LIKE "' . $search . '")';
                                } else {
                                    $where_in  .= ' (i.descricao LIKE "' . $search . '" OR i.part_number LIKE "' . $search . '")';
                                }
                            }
                        }

                        if (substr($search, 0, 1) === '*' && substr($search, -1) === '*' && substr_count($search, '*') <= 2) {
                            $search = str_replace("*", "",  $search);
                            if ($hasPnPrimarioSecundario) {
                                $where_in  .= ' (i.descricao LIKE "%' . $search . '%" OR i.part_number LIKE "%' . $search . '%" OR i.pn_primario_mpn LIKE "%' . $search . '%" OR i.pn_secundario_ipn LIKE "%' . $search . '%" )';
                            } else {
                                $where_in  .= ' (i.descricao LIKE "%' . $search . '%" OR i.part_number LIKE "%' . $search . '%")';
                            }
                        } else {
                            if (substr_count($search, '*') >= 2) {

                                $adwhereplus = '';

                                $search = str_replace("*", "%", $search);

                                if ($search !== '') {

                                    if ($hasPnPrimarioSecundario) {
                                        $adwhereplus .= 'i.descricao LIKE "' . $search . '" OR i.part_number LIKE "' . $search . '" OR i.pn_primario_mpn LIKE "' . $search . '" OR i.pn_secundario_ipn LIKE "' . $search . '" OR ';
                                    } else {
                                        $adwhereplus .= 'i.descricao LIKE "' . $search . '" OR i.part_number LIKE "' . $search . '" OR ';
                                    }
                                }

                                $adwhereplus = rtrim($adwhereplus, 'OR ');

                                $where_in .= ' (' . $adwhereplus . ')';
                            }
                        }

                        $this->db->where($where_in, NULL, FALSE);
                    } else {
                        //$this->db->like("i.part_number", trim($search));
                        if (!empty($search)) {
                            if (strpos($search, ',') !== false) {
                                $keywords = explode(',', $search);
                                $adwhere_go = "";
                                foreach ($keywords as $keyword) {
                                    if ($keyword !== '') {
                                        if ($hasPnPrimarioSecundario) {
                                            $adwhere_go .= ' i.part_number LIKE "%' . $keyword  . '%" OR i.pn_primario_mpn LIKE "%' . $keyword . '%" OR i.pn_secundario_ipn LIKE "%' . $keyword . '%" OR';
                                        } else {
                                            $adwhere_go .= ' i.part_number LIKE "%' . $keyword  . '%" OR ';
                                        }
                                    }
                                }
                                $adwhere_go = rtrim($adwhere_go, 'OR ');
                                $where_in .= ' (' . $adwhere_go . ')';

                                if ($where_in != ' ()') {
                                    $this->db->where($where_in, NULL, FALSE);
                                }
                            } else {
                                if ($hasPnPrimarioSecundario) {
                                    $this->db->where("(i.descricao LIKE '{$search}' OR i.part_number LIKE '{$search}' OR i.pn_primario_mpn LIKE '{$search}' OR i.pn_secundario_ipn LIKE '{$search}')", NULL, FALSE);
                                } else {
                                    $this->db->where("(i.descricao LIKE '{$search}' OR i.part_number LIKE '{$search}')", NULL, FALSE);
                                }
                            }
                        }
                    }
                }
            }
        }

        if ($status_atributos = $this->get_state('filter.status_atributos')) {
            $this->db->where_in("i.wf_status_atributos", $status_atributos);
        }

        if ($status_integracao = $this->get_state('filter.status_integracao')) {
            if (!in_array('-1', $status_integracao)) {
                $this->db->where_in("i.wf_status_integracao", $status_integracao);
            }
        }

        if ($estabelecimento = $this->get_state('filter.estabelecimento')) {
            $this->db->where_in("i.estabelecimento", $estabelecimento);
        }

        if ($objetivos = $this->get_state('filter.objetivos')) {
            $this->db->join('ncm_atributo ncm', 'ncm.ncm = ci.ncm_proposto', 'inner');

            $likes = [];
            foreach ($objetivos as $index => $objetivo) {
                if (count($objetivos) == 1) {
                    $this->db->like('ncm.objetivos', $objetivo);
                } else {
                    $likes[] = "ncm.objetivos LIKE '%" . $this->db->escape_like_str($objetivo) . "%'";
                    //  $this->db->or_like('ncm.objetivos', $objetivo);
                }
            }

            if (!empty($likes)) {
                $this->db->where('(' . implode(' OR ', $likes) . ')');
            }
        }

        if ($ncm_proposta_modal = $this->get_state('filter.ncm_proposta_modal')) {
            $this->db->where_in("ci.ncm_proposto", $ncm_proposta_modal);
        }

        // if ($status_preenchimento = $this->get_state('filter.status_preenchimento')) {

        //     if(reset($status_preenchimento) == 1){
        // 		$this->db->where("((SELECT COUNT(*) FROM cad_item_attr WHERE id_item = ci.id_item AND obrigatorio = 1 AND ativo = 1  AND (codigo IS NULL OR codigo = '') > 0 ))", NULL, FALSE);
        // 	}else{
        // 		$this->db->where("(SELECT COUNT(*) FROM cad_item_attr WHERE id_item = ci.id_item AND obrigatorio = 0 AND ativo = 1  AND (codigo IS NULL OR codigo = '') > 0 )", NULL, FALSE);
        // 	}

        // }

        if ($responsavel = $this->get_state('filter.responsavel')) {
            if (!is_array($responsavel)) {
                $responsavel = [$responsavel];
            }

            $this->db->where_in("ci.id_resp_engenharia", $responsavel);
        }

        if ($evento = $this->get_state("filter.evento")) {
            $evento = (array)$evento;
            $newEvento = array_filter($evento, function ($item) {
                return $item != "sem_evento";
            });

            if (isset($newEvento[0]) && !empty($newEvento[0])) {

                $eventoConcatenado = "'" . implode("','", $newEvento) . "'";
                $whereClause = "(i.evento IN ({$eventoConcatenado}))";

                if (in_array("sem_evento", $evento)) {
                    $whereClause .= " OR (i.evento = '' OR i.evento IS NULL)";
                }
                $this->db->where($whereClause, null, false);
            } elseif (in_array("sem_evento", $evento)) {
                $this->db->where("(i.evento = '' OR i.evento IS NULL)", NULL, FALSE);
            }
        }

        if ($owner = $this->get_state('filter.owner')) {
            $this->db->where_in("i.cod_owner", $owner);
        }

        if ($hasPrioridade) {
            if ($prioridade = $this->get_state('filter.prioridade')) {
                if (!empty($prioridade)) {
                    $this->db->where_in('i.id_prioridade', $prioridade);
                }
            }
        }

        $status_preenchimento = $this->get_state('filter.status_preenchimento');
        if ($status_preenchimento) {
            $this->db->where_in("i.status_attr", $status_preenchimento);
        }
        $this->db->group_by('ci.id_item');

        if ($tipo_item == 'importado') {
            $query = $this->db->get('cad_item ci', $limit, $offset);
        } else {
            $query = $this->db->get('cad_item ci', $limit, $offset);
        }

        $result =  $query->result();
        $array_itens = [];
        $id_itens = [];
        foreach ($result as $k => $dbNcm) {
            $id_itens[] = $dbNcm->id_item;
            $array_itens[$k] = $dbNcm;
        }

        return ['id_itens' => $id_itens, 'array_itens' => $array_itens, 'permissao' => $tipo_item];
    }

    public function default_filters($id_empresa, $empresa)
    {

        $data['campos_adicionais'] = explode("|", $empresa->campos_adicionais);
        $hasPrioridade =  in_array('prioridade', $data['campos_adicionais']);
        $hasPnPrimarioSecundario = in_array('pn_primario_secundario', $data['campos_adicionais']);

        if ($status_classificacao_fiscal = $this->get_state('filter.status_classificacao_fiscal')) {
            $this->db->where_in("i.id_status", $status_classificacao_fiscal);
        }

        $where_in = "";
        if ($search = $this->get_state('filter.search')) {
            $keywords = array_filter(preg_split('/\r\n|[\r\n]/', $search));

            if (count($keywords) > 1) {
                $adwhereplus = "";
                foreach ($keywords as $keyword) {
                    if ($keyword !== '') {
                        $keyword = str_replace("*", "%",  $keyword);
                        if ($hasPnPrimarioSecundario) {
                            $adwhereplus .= ' i.descricao LIKE "' . $keyword . '" OR i.part_number LIKE "' . trim($keyword) . '" OR i.pn_primario_mpn LIKE "' . trim($keyword) . '" OR i.pn_secundario_ipn LIKE "' . trim($keyword) . '" OR ';
                        } else {
                            $adwhereplus .= ' i.descricao LIKE "' . $keyword . '" OR i.part_number LIKE "' . trim($keyword) . '" OR ';
                        }
                    }
                }

                $adwhereplus = rtrim($adwhereplus, 'OR ');

                $where_in .= ' (' . $adwhereplus . ')';
                if (!empty($adwhereplus)) {
                    $this->db->where($where_in, NULL, FALSE);
                }
            } else if (strpos($search, "\n") !== false) {

                $search = str_replace(array("\t", "\r\n", "\s", "\n"), '#', $search);

                $keywords = explode('#', $search);
                $adwhereplus = "";
                $adwhere_go = "";
                foreach ($keywords as $keyword) {
                    if (empty($keyword)) {
                        continue;
                    }
                    $keyword = str_replace("*", "%", $keyword);

                    $keyword = rtrim($keyword, " ");
                    if ($keyword !== '') {
                        if ($hasPnPrimarioSecundario) {
                            $adwhereplus .= ' i.descricao LIKE "' . $keyword . '" OR i.part_number LIKE "' . trim($keyword) . '" OR i.pn_primario_mpn LIKE "' . trim($keyword) . '" OR i.pn_secundario_ipn LIKE "' . trim($keyword) . '" OR ';
                        } else {
                            $adwhereplus .= ' i.descricao LIKE "' . $keyword . '" OR i.part_number LIKE "' . trim($keyword) . '" OR ';
                        }
                    }
                }

                $adwhereplus = rtrim($adwhereplus, 'OR ');

                $where_in .= ' (' . $adwhereplus . ')';
                if (!empty($adwhereplus)) {
                    $this->db->where($where_in, NULL, FALSE);
                }
            } else {
                if ($where_in == '') {
                    if (strpos($search, '*') !== false) {


                        if (is_array($search)) {
                            $search = $search[0];
                        }


                        if (substr_count($search, '*') == 1) {

                            if (substr($search, 0, 1) === '*') {
                                $search = str_replace("*", "",  $search);
                                if ($hasPnPrimarioSecundario) {
                                    $where_in  .= ' (i.descricao LIKE "%' . $search . '" OR i.part_number LIKE "%' . $search . '" OR i.pn_primario_mpn LIKE "%' . $search . '" OR i.pn_secundario_ipn LIKE "%' . $search . '")';
                                } else {
                                    $where_in  .= ' (i.descricao LIKE "%' . $search . '" OR i.part_number LIKE "%' . $search . '")';
                                }
                            } else if (substr($search, -1) === '*') {
                                $search = str_replace("*", "",  $search);
                                if ($hasPnPrimarioSecundario) {
                                    $where_in  .= ' (i.descricao LIKE "' . $search . '%" OR i.part_number LIKE "' . $search . '%" OR i.pn_primario_mpn LIKE "' . $search . '%" OR i.pn_secundario_ipn LIKE "' . $search . '%")';
                                } else {
                                    $where_in  .= ' (i.descricao LIKE "' . $search . '%" OR i.part_number LIKE "' . $search . '%")';
                                }
                            } else {

                                $search = str_replace("*", "%", $search);

                                if ($hasPnPrimarioSecundario) {
                                    $where_in  .= ' (i.descricao LIKE "' . $search . '" OR i.part_number LIKE "' . $search . '" OR i.pn_primario_mpn LIKE "' . $search . '" OR i.pn_secundario_ipn LIKE "' . $search . '")';
                                } else {
                                    $where_in  .= ' (i.descricao LIKE "' . $search . '" OR i.part_number LIKE "' . $search . '")';
                                }
                            }
                        }

                        if (substr($search, 0, 1) === '*' && substr($search, -1) === '*' && substr_count($search, '*') <= 2) {
                            $search = str_replace("*", "",  $search);
                            if ($hasPnPrimarioSecundario) {
                                $where_in  .= ' (i.descricao LIKE "%' . $search . '%" OR i.part_number LIKE "%' . $search . '%" OR i.pn_primario_mpn LIKE "%' . $search . '%" OR i.pn_secundario_ipn LIKE "%' . $search . '%" )';
                            } else {
                                $where_in  .= ' (i.descricao LIKE "%' . $search . '%" OR i.part_number LIKE "%' . $search . '%")';
                            }
                        } else {
                            if (substr_count($search, '*') >= 2) {

                                $adwhereplus = '';

                                $search = str_replace("*", "%", $search);

                                // if ($search !== '') {

                                if ($hasPnPrimarioSecundario) {
                                    $adwhereplus .= 'i.descricao LIKE "' . $search . '" OR i.part_number LIKE "' . $search . '" OR i.pn_primario_mpn LIKE "' . $search . '" OR i.pn_secundario_ipn LIKE "' . $search . '" OR ';
                                } else {
                                    $adwhereplus .= 'i.descricao LIKE "' . $search . '" OR i.part_number LIKE "' . $search . '" OR ';
                                }
                                // }

                                $adwhereplus = rtrim($adwhereplus, 'OR ');

                                $where_in .= ' (' . $adwhereplus . ')';
                                $this->db->where($where_in, NULL, FALSE);
                            }
                        }
                    } else {

                        //$this->db->like("i.part_number", trim($search));
                        if (!empty($search)) {
                            if (strpos($search, ',') !== false) {
                                $keywords = explode(',', $search);
                                $adwhere_go = "";
                                foreach ($keywords as $keyword) {
                                    if (empty($keyword)) {
                                        continue;
                                    }
                                    // if ( $keyword !== '') {
                                    if ($hasPnPrimarioSecundario) {
                                        $adwhere_go .= ' i.part_number LIKE "%' . $keyword  . '%" OR i.pn_primario_mpn LIKE "%' . $keyword . '%" OR i.pn_secundario_ipn LIKE "%' . $keyword . '%" OR';
                                    } else {
                                        $adwhere_go .= ' i.part_number LIKE "%' . $keyword  . '%" OR ';
                                    }

                                    // }
                                }
                                $adwhere_go = rtrim($adwhere_go, 'OR ');
                                $where_in .= ' (' . $adwhere_go . ')';

                                if ($where_in != ' ()') {
                                    $this->db->where($where_in, NULL, FALSE);
                                }
                            } else {
                                if ($hasPnPrimarioSecundario) {
                                    $this->db->where("(i.descricao LIKE '{$search}' OR i.part_number LIKE '{$search}' OR i.pn_primario_mpn LIKE '{$search}' OR i.pn_secundario_ipn LIKE '{$search}')", NULL, FALSE);
                                } else {
                                    $this->db->where("(i.descricao LIKE '{$search}' OR i.part_number LIKE '{$search}')", NULL, FALSE);
                                }
                            }
                        }
                    }
                }
            }
        }

        if ($status_atributos = $this->get_state('filter.status_atributos')) {
            $this->db->where_in("i.wf_status_atributos", $status_atributos);
        }

        if ($status_integracao = $this->get_state('filter.status_integracao')) {

            if (!in_array('-1', $status_integracao)) {
                $this->db->where_in("i.wf_status_integracao", $status_integracao);
            }
        }

        if ($estabelecimento = $this->get_state('filter.estabelecimento')) {
            $this->db->where_in("i.estabelecimento", $estabelecimento);
        }

        if ($objetivos = $this->get_state('filter.objetivos')) {
            $this->db->join('ncm_atributo ncm', 'ncm.ncm = ci.ncm_proposto', 'inner');
            $likes = [];
            foreach ($objetivos as $index => $objetivo) {
                if (count($objetivos) == 1) {
                    $this->db->like('ncm.objetivos', $objetivo);
                } else {
                    //  $this->db->or_like('ncm.objetivosss', $objetivo);
                    $likes[] = "ncm.objetivos LIKE '%" . $this->db->escape_like_str($objetivo) . "%'";
                }
            }
            if (!empty($likes)) {

                $this->db->where('(' . implode(' OR ', $likes) . ')');
            }
        }

        if ($ncm_proposta_modal = $this->get_state('filter.ncm_proposta_modal')) {
            $this->db->where_in("ci.ncm_proposto", $ncm_proposta_modal);
        }

        if ($ncm_proposta_modal = $this->get_state('filter.ncm_proposta')) {
            $this->db->where_in("ci.ncm_proposto", $ncm_proposta_modal);
        }

        if (!empty($exclui_status)) {
            $this->db->where_not_in("i.wf_status_atributos", $exclui_status);
        }

        $status_preenchimento = $this->get_state('filter.status_preenchimento');
        if ($status_preenchimento) {
            $this->db->where_in("i.status_attr", $status_preenchimento);
        }

        //AND (ci.id_resp_engenharia = '1' OR ci.id_resp_fiscal = '1') -- //Responsável
        if ($responsavel = $this->get_state('filter.responsavel')) {
            $this->db->where_in("ci.id_resp_engenharia", $responsavel);
        }

        if ($evento = $this->get_state("filter.evento")) {
            $evento = (array)$evento;
            $newEvento = array_filter($evento, function ($item) {
                return $item != "sem_evento";
            });

            if (isset($newEvento[0]) && !empty($newEvento[0])) {

                $eventoConcatenado = "'" . implode("','", $newEvento) . "'";
                $whereClause = "(i.evento IN ({$eventoConcatenado}))";

                if (in_array("sem_evento", $evento)) {
                    $whereClause .= " OR (i.evento = '' OR i.evento IS NULL)";
                }
                $this->db->where($whereClause, null, false);
            } elseif (in_array("sem_evento", $evento)) {
                $this->db->where("(i.evento = '' OR i.evento IS NULL)", NULL, FALSE);
            }
        }

        if ($owner = $this->get_state('filter.owner')) {
            $this->db->where_in("i.cod_owner", $owner);
        }

        if ($hasPrioridade) {
            if ($prioridade = $this->get_state('filter.prioridade')) {
                if (!empty($prioridade)) {
                    $this->db->where_in('i.id_prioridade', $prioridade);
                }
            }
        }
    }

    public function get_entries($limit = NULL, $offset = NULL, $atribuidos = FALSE, $total = NULL, $exclui_status = null)
    {
        $this->load->model('empresa_model');

        if ($tipo_item = $this->get_state('filter.tipo_item')) {
            $tipo_item = $tipo_item;
        } else {
            $tipo_item = 'importado';
        }

        $id_empresa = sess_user_company();
        $empresa = $this->empresa_model->get_entry($id_empresa);
        $data['campos_adicionais'] = explode("|", $empresa->campos_adicionais);
        $hasPrioridade =  in_array('prioridade', $data['campos_adicionais']);
        $hasPnPrimarioSecundario = in_array('pn_primario_secundario', $data['campos_adicionais']);

        // Trazer na consulta também os id_itens distintos que fazem parte da NCM
        $this->db->select("
            COUNT(DISTINCT ci.id_item) AS total_itens,
            ci.ncm_proposto,
            JSON_ARRAYAGG(ci.id_item) AS id_itens,
            (SELECT COUNT(*) FROM ncm_atributo ncm WHERE ncm = ci.ncm_proposto) as atributos,
            (SELECT COUNT(*) FROM ncm_atributo ncm WHERE ncm = ci.ncm_proposto AND obrigatorio = 1) AS total_atributos_obrigatorios,
            (SELECT COUNT(*) FROM ncm_atributo ncm WHERE ncm = ci.ncm_proposto AND obrigatorio = 0) AS total_atributos_nao_obrigatorios
        ");

        $status_preenchimento = $this->get_state('filter.status_preenchimento');
        if ($status_preenchimento) {
            $this->db->where_in("i.status_attr", $status_preenchimento);
        }


        if ($tipo_item == 'importado') {
            $this->db->join('item i', 'ci.part_number = i.part_number AND ci.id_empresa = i.id_empresa AND ci.estabelecimento = i.estabelecimento ', 'inner');
            if ($this->db->table_exists('comex')) {
                $this->db->join('comex', 'comex.part_number_original = i.part_number AND comex.id_empresa = i.id_empresa AND comex.unidade_negocio = i.estabelecimento', 'inner');
                $this->db->where("comex.ind_ecomex = 'EI'"); 

                if ($data_ini_imp = $this->get_state('filter.data_inicio_importado_modal')) {
                    $data_ini_imp = DateTime::createFromFormat('d/m/Y', $data_ini_imp)->format('Y-m-d');
                    $this->db->where('DATE(comex.data_criacao) >=', $data_ini_imp);
                }
                
                if ($data_fim_imp = $this->get_state('filter.data_fim_importado_modal')) {
                    $data_fim_imp = DateTime::createFromFormat('d/m/Y', $data_fim_imp)->format('Y-m-d');
                    $this->db->where('DATE(comex.data_criacao) <=', $data_fim_imp);
                }
            }
            $this->db->where('ci.id_empresa = ' . $id_empresa, NULL, FALSE);
            $this->db->where("(ci.ncm_proposto IS NOT NULL AND ci.ncm_proposto != '')");
            $this->db->where("i.id_status <> 4");

            if ($data_ini_imp = $this->get_state('filter.data_inicio_importado_modal')) {
                $data_ini_imp = DateTime::createFromFormat('d/m/Y', $data_ini_imp)->format('Y-m-d');
                $this->db->where('DATE(comex.data_criacao) >=', $data_ini_imp);
                $this->db->where('comex.ind_ecomex', 'EI');
            }
            
            if ($data_fim_imp = $this->get_state('filter.data_fim_importado_modal')) {
                $data_fim_imp = DateTime::createFromFormat('d/m/Y', $data_fim_imp)->format('Y-m-d');
                $this->db->where('DATE(comex.data_criacao) <=', $data_fim_imp);
                $this->db->where('comex.ind_ecomex', 'EI');
            }
    
            $this->default_filters($id_empresa,$empresa);
            $this->db->where('EXISTS (SELECT 1 FROM ncm_atributo ncm WHERE ci.ncm_proposto = ncm.ncm)', NULL, FALSE);

            $this->db->group_by('ci.ncm_proposto');
            $this->db->order_by('ci.ncm_proposto ASC');
            $query = $this->db->get('cad_item ci', $limit, $offset);
        } else {
            $this->db->join('item i', 'ci.part_number = i.part_number AND ci.id_empresa = i.id_empresa AND ci.estabelecimento = i.estabelecimento ', 'inner');
            $this->db->where('ci.id_empresa = ' . $id_empresa, NULL, FALSE);
            $this->db->where("(ci.ncm_proposto IS NOT NULL AND ci.ncm_proposto != '')");
            $this->db->where("(i.wf_status_atributos IS NULL OR i.wf_status_atributos = '1')");
            $this->db->where("(ci.ncm_proposto <> '00000000')");
            $this->db->where("i.id_status <> 4");

            $this->default_filters($id_empresa, $empresa);
            $this->db->where('EXISTS (SELECT 1 FROM ncm_atributo ncm WHERE ci.ncm_proposto = ncm.ncm)', NULL, FALSE);

            $this->db->group_by('ci.ncm_proposto');
            $this->db->order_by('ci.ncm_proposto ASC');
            $query = $this->db->get('cad_item ci', $limit, $offset);
        }

        if (!empty($total)) {
            $result = $query->result();
            $num_rows = $query->num_rows();
            $total_itens = 0;

            foreach ($result as $item) {
                $total_itens += intval($item->total_itens);
            }

            return [
                'num_rows' => $num_rows,
                'total_itens' => $total_itens,
            ];
        }

        return $query->result();
    }

    public function get_list_status_class_fiscais($id_empresa)
    {
        $this->db->select('s.*');
        $query = $this->db->get('status s');
        return  $query->result();
    }

    public function get_list_status_class_fiscais_nacionais($id_empresa)
    {

        $this->db->select('s.*');
        $query = $this->db->get('status s');
        return  $query->result();
    }

    public function get_list_eventos($id_empresa)
    {

        $this->db->select('DISTINCT(evento)');
        $this->db->where('i.id_empresa', $id_empresa);
        $this->db->where('evento IS NOT NULL', null, false);
        $this->db->where('evento !=', '');

        $this->db->order_by('evento', 'asc');

        $query = $this->db->get('item i');

        return $query->num_rows() > 0 ? $query->result() : array();
    }

    public function get_list_eventos_nacionais($id_empresa)
    {

        $this->db->select('i.evento');
        //$this->db->join('cad_item ci', 'i.part_number = ci.part_number AND i.id_empresa = ci.id_empresa AND i.estabelecimento = ci.estabelecimento ', 'inner');
        $this->db->where('i.id_empresa = ' . $id_empresa, NULL, FALSE);
        // $this->db->where("(ci.ncm_proposto IS NOT NULL AND ci.ncm_proposto != '')");
        $this->db->where("(i.evento IS NOT NULL AND i.evento != '')");
        $this->db->group_by('i.evento');

        $query = $this->db->get('item i');
        return  $query->result();
    }

    public function get_list_objetivos()
    {

        return ['Produto', 'Tratamento administrativo', 'LPCO'];
    }

    public function get_ncm($id_empresa)
    {
        $this->db->select('ci.ncm_proposto');

        // FROM com FORCE INDEX. O FALSE evita que o CI escape a string, preservando a sintaxe desejada.
        $this->db->from("cad_item ci ", false);

        $this->db->join(
            'ncm_atributo ncmattr',
            'ncmattr.ncm = ci.ncm_proposto',
            'inner'
        );
        $this->db->join('item i', 'ci.part_number = i.part_number AND ci.id_empresa = i.id_empresa AND ci.estabelecimento = i.estabelecimento ', 'inner');

        $this->db->where("ci.id_empresa = " . $id_empresa, null, false);
        $this->db->where("(ci.ncm_proposto IS NOT NULL AND ci.ncm_proposto != '')", null, false);

        $this->db->group_by('ci.ncm_proposto');
        $this->db->order_by('ci.ncm_proposto ASC');

        $query = $this->db->get();
        return  $query->result();
    }

    public function get_list_ncm_proposto($id_empresa)
    {
        $this->db->select('ci.ncm_proposto');

        // FROM com FORCE INDEX. O FALSE evita que o CI escape a string, preservando a sintaxe desejada.
        $this->db->from("cad_item ci", false);

        $this->db->join(
            'ncm_atributo ncmattr',
            'ncmattr.ncm = ci.ncm_proposto',
            'inner'
        );
        $this->db->join('item i', 'ci.part_number = i.part_number AND ci.id_empresa = i.id_empresa AND ci.estabelecimento = i.estabelecimento ', 'inner');

        $this->db->where("ci.id_empresa = " . $id_empresa, null, false);
        $this->db->where("(ci.ncm_proposto IS NOT NULL AND ci.ncm_proposto != '')", null, false);

        $this->db->group_by('ci.ncm_proposto');
        $this->db->order_by('ci.ncm_proposto ASC');

        $status_preenchimento = $this->get_state('filter.status_preenchimento');
        if ($status_preenchimento) {
            $this->db->where_in("i.status_attr", $status_preenchimento);
        }

        $query = $this->db->get();
        return  $query->result();
    }
    public function get_list_ncm_proposto_nacionais($id_empresa)
    {


        $this->db->select('ci.ncm_proposto');
        $this->db->join('item i', 'ci.part_number = i.part_number AND ci.id_empresa = i.id_empresa AND ci.estabelecimento = i.estabelecimento ', 'inner');
        $this->db->where('ci.id_empresa = ' . $id_empresa, NULL, FALSE);
        $this->db->where("(ci.ncm_proposto IS NOT NULL AND ci.ncm_proposto != '')");
        $this->db->join('ncm_atributo ncmattr', 'ncmattr.ncm = ci.ncm_proposto', 'inner');
        $this->db->group_by('ci.ncm_proposto');
        $this->db->order_by('ci.ncm_proposto ASC');

        $status_preenchimento = $this->get_state('filter.status_preenchimento');
        if ($status_preenchimento) {
            $this->db->where_in("i.status_attr", $status_preenchimento);
        }

        $query = $this->db->get('cad_item ci');

        return  $query->result();
    }

    public function get_list_empresa_prioridades($id_empresa)
    {
        $this->db->select('ep.*');
        $this->db->join('cad_item ci', 'i.part_number = ci.part_number AND i.id_empresa = ci.id_empresa AND i.estabelecimento = ci.estabelecimento ', 'inner');
        $this->db->join('empresa_prioridades ep', 'ep.id_prioridade = i.id_prioridade ', 'inner');
        $this->db->where('i.id_empresa = ' . $id_empresa, NULL, FALSE);
        $this->db->where("(ci.ncm_proposto IS NOT NULL AND ci.ncm_proposto != '')");
        $this->db->group_by('i.id_prioridade');
        $this->db->order_by('ep.ordem ASC');

        $query = $this->db->get('item i');
        return  $query->result();
    }

    public function get_list_empresa_prioridades_nacionais($id_empresa)
    {

        $this->db->select('ep.*');
        $this->db->join('cad_item ci', 'i.part_number = ci.part_number AND i.id_empresa = ci.id_empresa AND i.estabelecimento = ci.estabelecimento ', 'inner');
        $this->db->join('empresa_prioridades ep', 'ep.id_prioridade = i.id_prioridade ', 'inner');
        $this->db->where('i.id_empresa = ' . $id_empresa, NULL, FALSE);
        $this->db->where("(ci.ncm_proposto IS NOT NULL AND ci.ncm_proposto != '')");
        $this->db->group_by('i.id_prioridade');
        $this->db->order_by('ep.ordem ASC');

        $query = $this->db->get('item i');
        return  $query->result();
    }

    public function get_list_responsavel($id_empresa)
    {

        $this->db->select('ci.id_resp_engenharia, u2.nome, ci.id_resp_fiscal');
        $this->db->join('item i', 'ci.part_number = i.part_number AND ci.id_empresa = i.id_empresa AND ci.estabelecimento = i.estabelecimento ', 'inner');
        $this->db->join('usuario u2', 'u2.id_usuario = ci.id_resp_engenharia ', 'inner');
        $this->db->where('ci.id_empresa = ' . $id_empresa, NULL, FALSE);
        $this->db->where("(ci.ncm_proposto IS NOT NULL AND ci.ncm_proposto != '')");
        $this->db->group_by('ci.id_resp_engenharia');
        $this->db->order_by('u2.nome ASC');

        $query = $this->db->get('cad_item ci');
        return  $query->result();
    }


    public function get_list_responsavel_nacionais($id_empresa)
    {

        $this->db->select('ci.id_resp_engenharia, u2.nome, ci.id_resp_fiscal');
        $this->db->join('item i', 'ci.part_number = i.part_number AND ci.id_empresa = i.id_empresa AND ci.estabelecimento = i.estabelecimento ', 'inner');
        $this->db->join('usuario u2', 'u2.id_usuario = ci.id_resp_engenharia ', 'inner');
        $this->db->where('ci.id_empresa = ' . $id_empresa, NULL, FALSE);
        $this->db->where("(ci.ncm_proposto IS NOT NULL AND ci.ncm_proposto != '')");
        $this->db->group_by('ci.id_resp_engenharia');
        $this->db->order_by('u2.nome ASC');

        $query = $this->db->get('cad_item ci');
        return  $query->result();
    }

    public function get_list_status_wf_atributos($id_empresa)
    {

        $this->db->select('swa.*');
        $this->db->join('item i', 'ci.part_number = i.part_number AND ci.id_empresa = i.id_empresa AND ci.estabelecimento = i.estabelecimento ', 'inner');
        $this->db->join('status_wf_atributos swa', 'swa.id = i.wf_status_atributos ', 'inner');
        $this->db->where('ci.id_empresa = ' . $id_empresa, NULL, FALSE);
        $this->db->where("(ci.ncm_proposto IS NOT NULL AND ci.ncm_proposto != '')");
        $this->db->group_by('swa.id');
        $this->db->order_by('swa.ordem ASC');

        $query = $this->db->get('cad_item ci');
        return  $query->result();
    }

    public function get_list_status_wf_atributos_integracao($id_empresa)
    {

        $this->db->select('swai.*');
        $this->db->join('item i', 'ci.part_number = i.part_number AND ci.id_empresa = i.id_empresa AND ci.estabelecimento = i.estabelecimento ', 'inner');
        $this->db->join('status_wf_atributos_integracao swai', 'swai.id = i.wf_status_integracao ', 'inner');
        $this->db->where('ci.id_empresa = ' . $id_empresa, NULL, FALSE);
        $this->db->where("(ci.ncm_proposto IS NOT NULL AND ci.ncm_proposto != '')");
        $this->db->group_by('swai.id');
        $this->db->order_by('swai.ordem ASC');

        $query = $this->db->get('cad_item ci');
        return  $query->result();
    }

    public function get_historico($part_number, $estabelecimento, $id_empresa)
    {

        $this->db->select("lwa.*, u.nome as nome_usuario");
        $this->db->where("lwa.part_number = '{$part_number}'", NULL, FALSE);
        $this->db->where("lwa.estabelecimento = '{$estabelecimento}'", NULL, FALSE);
        $this->db->where("lwa.id_empresa = '{$id_empresa}'", NULL, FALSE);
        $this->db->join('usuario u', 'u.id_usuario = lwa.id_usuario ', 'inner');
        $this->db->order_by('lwa.criado_em DESC');

        $query = $this->db->get('log_wf_atributos lwa');
        $result =  $query->result();

        return $result;
    }


    public function get_status_wf_atributos($status)
    {
        $this->db->where('slug', $status);
        $query = $this->db->get('status_wf_atributos');
        $result =  $query->row();
        return $result;
    }

    public function get_status_wf_atributos_by_id($id)
    {
        $this->db->where('id', $id);
        $query = $this->db->get('status_wf_atributos');
        $result =  $query->row();
        return $result;
    }

    public function set_status($status_novo, $id_item = null, $part_numbers = null, $estabelecimentos = null, $id_empresa = null, $ncm = null)
    {
        $this->load->model('cad_item_model');
        $status = $this->get_status_wf_atributos($status_novo);

        if (is_array($id_item) && !empty($id_item) && !empty($id_item[0]['ncm_proposto'])) {
            $ids = array_map(function ($item) {
                return $item['id_item'];
            }, $id_item);
            $id_item = $ids;
        }

        if (!empty($id_item) && !empty($status)) {
            if (!empty($id_item)) {
                if (is_array($id_item)) {
                    $id_item_str = implode(',', $id_item);
                } else if (is_numeric($id_item)) {
                    $id_item_str = $id_item;
                } else {
                    return;
                }

                $sql = "
                    UPDATE item i
                    INNER JOIN cad_item ci ON ci.part_number = i.part_number
                        AND ci.estabelecimento = i.estabelecimento
                        AND ci.id_empresa = i.id_empresa

                    INNER JOIN comex cx ON cx.part_number_original = i.part_number
                        AND cx.unidade_negocio = i.estabelecimento
                        AND cx.id_empresa = i.id_empresa

                    SET i.wf_status_atributos = ?
                    WHERE ci.id_item IN ($id_item_str) AND 
                    cx.ind_ecomex = 'EI'
                ";

                $this->db->query($sql, array($status->id));
            } else if (!empty($id_item) && !is_array($id_item)) {

                $sql = "
                    UPDATE item i
                    INNER JOIN cad_item ci ON ci.part_number = i.part_number
                        AND ci.estabelecimento = i.estabelecimento
                        AND ci.id_empresa = i.id_empresa

                    INNER JOIN comex cx ON cx.part_number_original = i.part_number
                        AND cx.unidade_negocio = i.estabelecimento
                        AND cx.id_empresa = i.id_empresa

                    SET i.wf_status_atributos = ?
                    WHERE ci.id_item = '{$id_item}' AND 
                        cx.ind_ecomex = 'EI'
                ";

                $this->db->query($sql, array($status->id));
            }
        }

        if (!empty($ncm) && empty($id_item)) {
            $this->set_state_store_session(TRUE);
            $this->restore_state_from_session('filter.', 'post');

            $itens = $this->get_itens_validados_movimentacao($ncm);
            $id_itens = [];
            foreach ($itens as $item) {
                $id_itens[] = $item->id_item;
            }
            if (empty($id_itens))
                return;

            $this->db->set('item.wf_status_atributos', $status->id);
            $this->db->join('cad_item ci', 'ci.part_number = item.part_number AND ci.estabelecimento = item.estabelecimento AND ci.id_empresa = item.id_empresa', 'inner');


            if ($this->db->table_exists('comex')) {
                $this->db->join('comex', 'comex.part_number_original = ci.part_number AND comex.id_empresa = ci.id_empresa AND comex.unidade_negocio = i.estabelecimento', 'inner');
                $this->db->where("comex.ind_ecomex = 'EI'"); 

                if ($data_ini_imp = $this->get_state('filter.data_inicio_importado_modal')) {
                    $data_ini_imp = DateTime::createFromFormat('d/m/Y', $data_ini_imp)->format('Y-m-d');
                    $this->db->where('DATE(comex.data_criacao) >=', $data_ini_imp);
                }
                
                if ($data_fim_imp = $this->get_state('filter.data_fim_importado_modal')) {
                    $data_fim_imp = DateTime::createFromFormat('d/m/Y', $data_fim_imp)->format('Y-m-d');
                    $this->db->where('DATE(comex.data_criacao) <=', $data_fim_imp);
                }
            }
            $this->db->where_in('ci.id_item', $id_itens);
            $this->db->update('item');
        }
        return true;
    }


    public function get_usuarios_emails($part_numbers, $estabelecimentos, $id_empresa)
    {

        $where = '';

        if (is_array($part_numbers) && is_array($estabelecimentos)) {
            foreach ($part_numbers as $x => $part_number) {
                $where .= "OR (ci.part_number = '{$part_number}' AND ci.estabelecimento = '{$estabelecimentos[$x]}' AND ci.id_empresa = '{$id_empresa}')";
            }
        } else {
            $where .= "OR (ci.part_number = '{$part_numbers}' AND ci.estabelecimento = '{$estabelecimentos}' AND ci.id_empresa = '{$id_empresa}')";
        }

        // foreach($part_numbers as $x => $part_number){
        //     $where .= "OR (ci.part_number = '{$part_number}' AND ci.estabelecimento = '{$estabelecimentos[$x]}' AND ci.id_empresa = {$id_empresa})";
        // }

        $where = substr($where, 2);

        $this->db->select("ci.id_resp_engenharia,
                            ci.id_resp_fiscal,
                            u2.nome,
                            u2.email");
        $this->db->join('usuario u2', 'u2.id_usuario = ci.id_resp_engenharia ', 'inner');
        $this->db->where($where, NULL, FALSE);
        $this->db->group_by('ci.id_resp_engenharia');

        $query = $this->db->get('cad_item ci');
        $result =  $query->result();

        return $result;
    }

    public function get_itens($part_numbers, $estabelecimentos, $id_empresa)
    {

        $where = '';

        if (is_array($part_numbers) && is_array($estabelecimentos)) {
            foreach ($part_numbers as $x => $part_number) {
                $where .= "OR (ci.part_number = '{$part_number}' AND ci.estabelecimento = '{$estabelecimentos[$x]}' AND ci.id_empresa = '{$id_empresa}')";
            }
        } else {
            $where .= "OR (ci.part_number = '{$part_numbers}' AND ci.estabelecimento = '{$estabelecimentos}' AND ci.id_empresa = '{$id_empresa}')";
        }

        // foreach($part_numbers as $x => $part_number){
        //     $where .= "OR (ci.part_number = '{$part_number}' AND ci.estabelecimento = '{$estabelecimentos[$x]}' AND ci.id_empresa = {$id_empresa})";
        // }

        $where = substr($where, 2);

        $this->db->select("ci.id_item, 
                    i.part_number,
                    i.estabelecimento,
                    i.descricao,
                    ci.ncm_proposto,
                    ci.id_resp_engenharia,
                    i.wf_status_atributos");
        $this->db->join('cad_item ci', 'i.part_number = ci.part_number AND i.id_empresa = ci.id_empresa AND i.estabelecimento = ci.estabelecimento', 'inner');
        $this->db->where($where, NULL, FALSE);
        $this->db->where("(ci.ncm_proposto IS NOT NULL AND ci.ncm_proposto != '')");

        $query = $this->db->get('item i');
        $result =  $query->result();

        return $result;
    }

    public function get_itens_by_ids($item_ids)
    {
        if (empty($item_ids)) {
            return [];
        }

        $this->db->select('part_number, estabelecimento');
        $this->db->where_in('id_item', $item_ids);
        $query = $this->db->get('cad_item');
        return $query->result_array();
    }

    public function send_mail_alteracao_status_item_wf_atributos($part_numbers, $estabelecimentos, $id_empresa, $status_novo)
    {
        $this->load->model(array('usuario_model'));

        $usuarios = $this->get_usuarios_emails($part_numbers, $estabelecimentos, $id_empresa);

        $itens = $this->get_itens($part_numbers, $estabelecimentos, $id_empresa);

        $this->load->library("Item/Atributo");

        $desc_status = $this->atributo->get_status($status_novo);

        $url = config_item('online_url') . '/wf/atributos';

        foreach ($usuarios as $usuario) {

            $tabela_itens = '<table>';
            $tabela_itens .= '<tr>';
            $tabela_itens .= '<th><strong>Part-number</strong><th>';
            $tabela_itens .= '<th><strong>Estabelecimento</strong><th>';
            $tabela_itens .= '<th><strong>Descrição</strong><th>';
            $tabela_itens .= '<th><strong>NCM Proposto</strong><th>';
            $tabela_itens .= '</tr>';
            $qtd_itens = 0;
            foreach ($itens as $item) {
                if ($item->id_resp_engenharia ==  $usuario->id_resp_engenharia) {
                    $tabela_itens .= '<tr>';
                    $tabela_itens .= '<td>' . $item->part_number . '<td>';
                    $tabela_itens .= '<td>' . $item->estabelecimento . '<td>';
                    $tabela_itens .= '<td>' . $item->descricao . '<td>';
                    $tabela_itens .= '<td>' . $item->ncm_proposto . '<td>';
                    $tabela_itens .= '</tr>';
                    $qtd_itens++;
                }
            }
            $tabela_itens .= '</table>';

            $html_message = '
                <h3>Itens com alteração de status para ' . $desc_status . ':</h3>

                <br>

                <p>Olá, ' . $usuario->nome . '!</p>
                <p>Os seguintes itens foram alterados no portal, verifique abaixo as informações: </p>

                <div class="panel panel-default">
                    <div class="panel-body" style="padding: 20px;border-left: 3px solid #ccc;background: #FBFBFB;">
                        <p><strong>Quantidade de itens: </strong> ' . $qtd_itens . '</p>
                        ' . $tabela_itens . '
                    </div>
                </div>

                <p style="text-align: right;"><a href="' . $url . '">Clique aqui para acompanhar no portal</a></p>
            ';

            $temp_data = array(
                'base_url' => config_item('online_url'),
                'html_message' => $html_message
            );

            $body = $this->load->view('templates/basic_template', $temp_data, true);

            $this->load->library('email');

            $this->email->from(config_item('mail_from_addr'), config_item('mail_from_name'));
            $this->email->to($usuario->email);
            $this->email->subject('[Gestão Tarifária] - Alterações de Status dos atributos nos itens');
            $this->email->message($body);

            return $this->email->send();
        }
    }

    public function reset_query(&$db)
    {
        $db->ar_select = array();
        $db->ar_distinct = FALSE;
        $db->ar_from = array();
        $db->ar_join = array();
        $db->ar_where = array();
        $db->ar_like = array();
        $db->ar_groupby = array();
        $db->ar_having = array();
        $db->ar_orderby = array();
        $db->ar_wherein = array();
        $db->ar_aliased_tables = array();
        $db->ar_no_escape = array();
        $db->ar_set = array();
        $db->ar_where = array();
        $db->ar_wherein = array();
        $db->ar_like = array();
        $db->ar_limit = FALSE;
        $db->ar_offset = FALSE;
        $db->ar_order = FALSE;
        $db->ar_orderby = array();
        $db->ar_having = array();
        $db->ar_keys = array();
        $db->ar_cache_exists = array();
        $db->ar_cache_select = array();
        $db->ar_cache_from = array();
        $db->ar_cache_join = array();
        $db->ar_cache_where = array();
        $db->ar_cache_like = array();
        $db->ar_cache_groupby = array();
        $db->ar_cache_having = array();
        $db->ar_cache_orderby = array();
        $db->ar_cache_set = array();
    }

    public function validar_respostas_attr($itens, $bulk = false)
    {
        $empresa = $this->empresa_model->get_entry(sess_user_company());
        $funcoes_adicionais = explode('|', $empresa->funcoes_adicionais);

        $this->load->model('catalogo/produto_model');
        $atributos_validados = [];
        foreach ($itens as $item) {
            if (!empty($item->ncm_proposto)) {
                $item_valido = true; // Assumir que o item é válido inicialmente
                $ncm_item = $this->get_required_attrs($this->produto_model->get_attr_ncm($item->ncm_proposto));

                $array = $this->get_attr($item->id_item);

                $ncm_item["defaultAttrs"] = $this->filterUniqueAttributes($array);
                $ncm_item["assocAttrs"] = $this->create_assoc_attrs_structure($ncm_item);

                $attr_salvo_db = [];

                // Construindo os atributos salvos no banco de dados.
                foreach ($ncm_item["defaultAttrs"] as $i) {
                    if (!in_array('homologacao_atributos_incompletos', $funcoes_adicionais) && ($i->codigo == null || $i->codigo == '') && $i->obrigatorio == 1) {
                        if (!$bulk) {
                            return false; // Comportamento original: falha rápida
                        }
                        $item_valido = false; // Comportamento em massa: marca como inválido e continua
                        break; // Pula para o próximo item, não precisa checar outros atributos
                    }
                    $attr_salvo_db[$i->atributo] = $i->codigo;
                }

                if (!$item_valido) {
                    continue; // Pula para o próximo item do loop principal
                }

                $this->_atributosFiltrados = [];
                $tamanho_valido = $this->filtrarAtributosPorTamanho($ncm_item["assocAttrs"], $attr_salvo_db);

                if ($tamanho_valido) {
                    $atributos_validados[] = $item; // Adiciona apenas itens válidos
                }
            }
        }
        return $atributos_validados;
    }

    function contarDigitosNumero($numero)
    {
        $digitos = preg_replace('/[^0-9]/', '', $numero);
        return strlen($digitos);
    }

    public function filtrarAtributosPorTamanho($atributos, $valores)
    {

        foreach ($atributos as $atributo) {
            $atributoValido = true;

            $casasDecimais = (int)$atributo['casasDecimais'];
            $tamanhoMaximo = (int)$atributo['tamanhoMaximo'];

            if (isset($atributo['dbdata']) && !empty($atributo['dbdata']) && isset($atributo['dbdata']['codigo'])) {
                $codigo = $atributo['dbdata']['codigo'];
                
                // Não valida atributos sem preenchimento
                if ($codigo == '' || $codigo == null) {
                    continue;
                }
                
                // Lógica específica para tipo NUMERO_INTEIRO
                if (isset($atributo['formaPreenchimento']) && $atributo['formaPreenchimento'] === 'NUMERO_INTEIRO' && !empty($tamanhoMaximo)) {
                    if ($this->contarDigitosNumero($codigo) > $tamanhoMaximo) {
                        $this->_atributosFiltrados[] = 0;
                    }

                    if (!preg_match('/^[0-9.,]+$/', $codigo)) {
                        $this->_atributosFiltrados[] = 0;
                    }
                } else if (isset($atributo['formaPreenchimento']) && $atributo['formaPreenchimento'] != 'NUMERO_REAL' && $atributo['formaPreenchimento'] != 'NUMERO_INTEIRO' && !empty($tamanhoMaximo)) {
                    $trimmed_codigo = trim($codigo); // Remove espaços no início e fim
                    $cleaned_codigo = trim(preg_replace('/\s+/', ' ', $trimmed_codigo));
                    if (mb_strlen($cleaned_codigo, 'UTF-8') > $tamanhoMaximo) {
                        $this->_atributosFiltrados[] = 0;
                    }
                }

            } elseif (isset($atributo['obrigatorio']) && $atributo['obrigatorio'] == 1) {
                $this->_atributosFiltrados[] = 0;
            }

            if (isset($atributo['formaPreenchimento']) && $atributo['formaPreenchimento'] === 'NUMERO_REAL' && $atributoValido && !empty($tamanhoMaximo)) {
                if (!preg_match('/^[0-9.,]+$/', $codigo)) {
                    $this->_atributosFiltrados[] = 0;
                }
                if (isset($atributo['casasDecimais']) && !empty($atributo['casasDecimais'])) {

                    if (isset($atributo['dbdata']) && !empty($atributo['dbdata']) && isset($atributo['dbdata']['codigo'])) {
                        $codigo = $atributo['dbdata']['codigo'];

                        if (is_numeric($codigo)) {

                            $partes = explode('.', $codigo);
                            if (isset($partes[1]) && strlen($partes[1]) > $casasDecimais) {
                                $this->_atributosFiltrados[] = 0;
                            }
                        } else {
                            $this->_atributosFiltrados[] = 0;
                        }

                        if ($this->contarDigitosNumero($codigo) > $tamanhoMaximo) {
                            $this->_atributosFiltrados[] = 0;
                        }
                    } else {
                        $this->_atributosFiltrados[] = 0;
                    }
                }
            }
            //Processa Subatributos Recursivamente
            // Verifica níveis adicionais de condicionantes
            if (!empty($atributo['listaSubatributos'])) {
                $this->filtrarAtributosPorTamanho($atributo['listaSubatributos'], $valores);
            }
        }

        return !in_array('0', $this->_atributosFiltrados) ? true : false;
    }

    public function get_itens_validados_movimentacao($ncm = null, $empresa = null, $id_empresa = null, $idItens = null, $validar_attr = true, $bulk = false)
    {

        if (empty($ncm) && empty($idItens)) {
            return []; // Retorna array vazio para evitar erros
        }

        if (empty($empresa)) {
            $id_empresa = sess_user_company();
            $this->load->model('empresa_model');
            $empresa = $this->empresa_model->get_entry($id_empresa);
        }

        $this->reset_query($this->db);

        $this->db->trans_start();

        $this->db->select("ci.id_item,
        i.part_number,
        i.estabelecimento, 
        i.descricao, 
        ci.ncm_proposto");
        $this->db->where('ci.id_empresa', $id_empresa);

        $this->db->join('item i', 'ci.part_number = i.part_number AND ci.id_empresa = i.id_empresa AND ci.estabelecimento = i.estabelecimento ', 'inner');

        if (!empty($idItens)) {
            $this->db->where_in('ci.id_item', $idItens);
        }

        if (!empty($ncm)) {
            $this->db->where("ci.ncm_proposto = '{$ncm}' AND (ci.ncm_proposto IS NOT NULL AND ci.ncm_proposto != '')", NULL, FALSE);
        }

        $this->default_filters($id_empresa, $empresa);
        $this->db->group_by('ci.id_item');
        $query = $this->db->get('cad_item ci');
        $item =  $query->result();
        $this->db->trans_complete();

        if ($validar_attr) {
            $result = $this->validar_respostas_attr($item, $bulk);
            return $result;
        } else {
            return $item;
        }
    }

    public function get_itens_validados($status_novo, $id_item = null, $ncm = null, $part_numbers = null, $estabelecimentos = null, $id_empresa = null)
    {

        $status = $this->get_status_wf_atributos($status_novo);

        $where = '';

        if (isset($part_numbers) && isset($estabelecimentos) && isset($id_empresa)) {
            foreach ($part_numbers as $x => $part_number) {
                $where .= "OR (ci.part_number = '{$part_number}' AND ci.estabelecimento = '{$estabelecimentos[$x]}' AND ci.id_empresa = '{$id_empresa}')";
                // $where .= "OR (ci.part_number = '{$r->part_number}' AND ci.estabelecimento = '{$r->estabelecimentos}' AND ci.id_empresa = {$r->id_empresa})";
            }
            $where = substr($where, 2);
            $this->db->where("({$where})", NULL, FALSE);
        }
        if (!empty($id_item)) {
            $this->db->where("ci.id_item = '{$id_item}'", NULL, FALSE);
        }

        // Verificação dos status
        switch ($status->id) {
            case 1: // Item nacional
                // Não é necessário considerar status anterior específico para este caso
                break;

            case 2: // Análise de atributos - Fiscal
                // Status inicial: Para itens importados
                break;

            case 3: // Preenchimento/Validação Engenharia
                // Status anterior: Análise de atributos - Fiscal (2)
                // Atributos obrigatórios preenchidos
                $this->db->where("(SELECT COUNT(*) FROM cad_item_attr WHERE id_item = ci.id_item AND obrigatorio = 1 AND (codigo IS NULL OR codigo = '')) = 0 
                            OR 
                            (SELECT 
                        COUNT(*)
                    FROM
                        cad_item_attr
                    WHERE
                    cad_item_attr.id_item = ci.id_item AND obrigatorio = 0
                            AND (codigo IS NOT NULL )) > 1");
                break;

            case 4: // Homologação da Classificação Fiscal
                $this->db->where("(SELECT COUNT(*) FROM cad_item_attr WHERE id_item = ci.id_item AND obrigatorio = 1 AND (codigo IS NULL OR codigo = '')) = 0 ");
                // Aguardando homologação da classificação fiscal (5)
                // $this->db->where("i.wf_status_atributos = 3");
                break;

            case 5: // Em revisão
                // Em revisão: Se houver alterações na descrição completa ou curta do item
                // $this->db->where("i.wf_status_atributos = 4");
                break;

            case 6: // Em revisão por alteração no PUCOMEX
                // Em revisão por atributos PUCOMEX: Se houver alterações na NCM do item
                //  $this->db->where("i.wf_status_atributos = 5");
                break;

            case 7: // Atributos homologados
                // Atributos homologados
                //  $this->db->where("i.wf_status_atributos = 6");
                break;
        }

        $this->db->select("ci.id_item,
                            i.part_number,
                            i.estabelecimento, 
                            i.descricao, 
                            ci.ncm_proposto");
        $this->db->join('item i', 'ci.part_number = i.part_number AND ci.id_empresa = i.id_empresa AND ci.estabelecimento = i.estabelecimento ', 'inner');

        if (!empty($ncm)) {
            $this->db->where("ci.ncm_proposto = '{$ncm}'", NULL, FALSE);
        }

        $query = $this->db->get('cad_item ci');
        $item =  $query->result();

        // Verificar esta validação pois não estão retornando os itens validados
        // $result = $this->validar_respostas_attr($item);

        return $item;
    }

    public function get_count_attrs($export = null)
    {
        $this->load->model('cockpit_model');
        $this->load->model('empresa_model');
        set_time_limit(0);
        ini_set('memory_limit', -1);
        $id_empresa = sess_user_company();
        $empresa = $this->empresa_model->get_entry($id_empresa);
        $campos_adicionais = explode("|", $empresa->campos_adicionais);
        $hasPrioridade =  in_array('prioridade', $campos_adicionais);
        $hasPnPrimarioSecundario = in_array('pn_primario_secundario', $campos_adicionais);
        $cnpj_raiz_logado = $this->empresa_model->get_entry_cnpj_raiz($id_empresa);

        $hasOwner =  in_array('owner', $campos_adicionais);

        if (empty($export)) {
            $subquery = "
                    SELECT 
   
                SUM(CASE
                    WHEN EXISTS (
                        SELECT 1
                        FROM ncm_atributo n
                        WHERE n.ncm = sub.ncm_proposto
                            AND ( n.codigo = 'null')
                    ) THEN 1
                    ELSE 0
                END) AS ncm_sem_atributos,

                SUM(CASE
                    WHEN sub.status_attr = 1 THEN 1
                    ELSE 0
                END) AS sem_preenchimento,
                SUM(CASE
                    WHEN sub.status_attr = 2 THEN 1
                    ELSE 0
                END) AS obrigatorio_nao_preenchido,
                    SUM(CASE
                    WHEN sub.status_attr = 3 THEN 1
                    ELSE 0
                END) AS opcional_nao_preenchido,
                    SUM(CASE
                    WHEN sub.status_attr = 4 THEN 1
                    ELSE 0
                END) AS totalmente_preenchido
            FROM
                (SELECT 
                    i.status_attr,
                    ci.ncm_proposto
                FROM
                    cad_item ci
                INNER JOIN item i ON ci.part_number = i.part_number
                    AND ci.estabelecimento = i.estabelecimento
                    AND ci.id_empresa = i.id_empresa
                LEFT JOIN usuario usu_fiscal ON usu_fiscal.id_usuario = ci.id_resp_fiscal
                LEFT JOIN usuario usu_engenharia ON usu_engenharia.id_usuario = ci.id_resp_engenharia
                INNER JOIN ncm_atributo ncmattr ON ncmattr.ncm = ci.ncm_proposto
                WHERE
                    ci.id_empresa = '{$id_empresa}'
                    
                        AND (ci.ncm_proposto IS NOT NULL
                        AND ci.ncm_proposto <> '')
                        AND (ci.ncm_proposto <> '00000000')
                ";
        } else if ($export == 'Sintetico') {

            $subquery = "
                SELECT
                ci.part_number,
                ci.descricao_mercado_local,
                usu_fiscal.nome as resp_fiscal,
                usu_engenharia.nome as resp_engenharia,
                i.evento,
                i.id_prioridade,
                i.estabelecimento,
                i.dat_criacao,
                i.data_modificacao,
                i.status_attr,
                ci.ncm_proposto,
                i.id_status as status_classificacao_fiscal,
                i.wf_status_atributos as status_atributo,
                i.wf_status_integracao as status_integracao";
            if ($hasOwner) {
                $subquery .= ",i.cod_owner, o.codigo as owner_codigo, o.descricao as owner_descricao";
            }
            $subquery .= "   
            FROM
                cad_item ci
                INNER JOIN item i ON ci.part_number = i.part_number and ci.estabelecimento = i.estabelecimento and ci.id_empresa = i.id_empresa
                LEFT JOIN usuario usu_fiscal ON usu_fiscal.id_usuario = ci.id_resp_fiscal
                LEFT JOIN usuario usu_engenharia ON usu_engenharia.id_usuario = ci.id_resp_engenharia";

            if ($hasOwner) {
                $subquery .= " LEFT JOIN owner o ON i.cod_owner = o.codigo AND (o.cnpj_raiz = '{$cnpj_raiz_logado}' OR o.cnpj_raiz IS NULL)";
            }

            $subquery .= "
                INNER JOIN ncm_atributo ncmattr ON ncmattr.ncm = ci.ncm_proposto
        
            WHERE
                ci.id_empresa = '{$id_empresa}'
                AND (
                    ci.ncm_proposto IS NOT NULL
                    AND ci.ncm_proposto <> ''
                )
                AND (ci.ncm_proposto <> '00000000')  
                ";
        } else if ($export == 'Detalhado') {

            $subquery = "
                SELECT
                ci.part_number,
                ci.descricao_mercado_local,
                usu_fiscal.nome as resp_fiscal,
                usu_engenharia.nome as resp_engenharia,
                i.evento,
                ep.nome as id_prioridade,
                i.estabelecimento,
                i.dat_criacao,
                i.data_modificacao,
                i.id_status as status_fiscal,
                ci.ncm_proposto,
                i.id_status as status_classificacao_fiscal,
                i.wf_status_atributos as status_atributo,
                i.wf_status_integracao as status_integracao";
            if ($hasOwner) {
                $subquery .= ",i.cod_owner, o.codigo as owner_codigo, o.descricao as owner_descricao ";
            }
            $subquery .= "   
                , i.status_attr,
                (SELECT MAX(criado_em) FROM cad_item_homologacao  where id_item = ci.id_item) as data_homologacao,
 
                (SELECT GROUP_CONCAT(
                    DISTINCT CONCAT(
                        ' |:| ', COALESCE(cia.atributo, ''),
                        ' || ', COALESCE(cia.apresentacao, ''),
                        ' - ', COALESCE(
                            NULLIF(cia.descricao, ''),
                            CASE
                                WHEN cia.codigo = '0' THEN 'Não'
                                WHEN cia.codigo = '1' THEN 'Sim'
                                ELSE COALESCE(cia.codigo, '')
                            END,
                            ''
                        )
                    ) SEPARATOR '||')
                    FROM cad_item_attr cia
                    WHERE cia.id_item = ci.id_item
                ) AS atributos
                  
            FROM
                cad_item ci
                INNER JOIN item i ON ci.part_number = i.part_number and ci.estabelecimento = i.estabelecimento and ci.id_empresa = i.id_empresa
                LEFT JOIN usuario usu_fiscal ON usu_fiscal.id_usuario = ci.id_resp_fiscal
                LEFT JOIN usuario usu_engenharia ON usu_engenharia.id_usuario = ci.id_resp_engenharia
                INNER JOIN ncm_atributo ncmattr ON ncmattr.ncm = ci.ncm_proposto
                LEFT JOIN empresa_prioridades ep ON ep.id_prioridade = i.id_prioridade";
            if ($hasOwner) {
                $subquery .= " LEFT JOIN owner o ON i.cod_owner = o.codigo AND (o.cnpj_raiz = '{$cnpj_raiz_logado}' OR o.cnpj_raiz IS NULL)";
            }

            $subquery .= "
            WHERE
                ci.id_empresa = '{$id_empresa}'
                AND (
                    ci.ncm_proposto IS NOT NULL
                    AND ci.ncm_proposto <> ''
                )
                AND (ci.ncm_proposto <> '00000000')  
                ";
        }

        $this->db->join('cad_item_homologacao ih', 'ih.id_item = i.id_item', 'inner');
        $evento = $this->cockpit_model->get_state('filter.evento');

        if ($evento && count($evento) > 0) {
            if (is_array($evento)) {
                if (empty($evento[0])) {
                    unset($evento[0]);
                }
                if (!is_array($evento)) {
                    $evento = array($evento);
                }

                $newEvento = array_filter($evento, function ($item) {
                    return $item != "sem_evento";
                });

                $eventoConcatenado = implode("','", array_map(function ($item) {
                    return $this->db->escape_str($item);
                }, $newEvento));

                if (!empty($newEvento)) {
                    if (in_array("sem_evento", $evento)) {
                        $subquery .= " AND (i.evento IN ('{$eventoConcatenado}') OR (i.evento = '' OR i.evento IS NULL))";
                    } else {
                        $subquery .= " AND (i.evento IN ('{$eventoConcatenado}'))";
                    }
                } elseif (in_array("sem_evento", $evento)) {
                    $subquery .= " AND (i.evento = '' OR i.evento IS NULL)";
                }
            }
        }

        $estabelecimento = $this->cockpit_model->get_state('filter.estabelecimento');

        if ($estabelecimento) {
            if (is_array($estabelecimento)) {
                $estabelecimentosConcatenados = implode("','", array_map(function ($item) {
                    return $this->db->escape_str($item);
                }, $estabelecimento));
                $subquery .= " AND (i.estabelecimento IN ('{$estabelecimentosConcatenados}'))";
            } elseif ($estabelecimento != "") {
                $subquery .= " AND i.estabelecimento = '" . $this->db->escape_str($estabelecimento) . "'";
            }
        }


        $objetivos = $this->cockpit_model->get_state('filter.objetivos');

        if ($objetivos) {

            $likes = [];
            foreach ($objetivos as $index => $objetivo) {

                $likes[] = "ncmattr.objetivos LIKE '%" . $this->db->escape_like_str($objetivo) . "%'";
            }
            if (!empty($likes)) {
                $subquery .= 'AND (' . implode(' OR ', $likes) . ')';
            }
        }

        $responsavel = $this->cockpit_model->get_state('filter.responsavel');

        if ($responsavel) {
            if (is_array($responsavel)) {
                $responsavelConcatenados = implode("','", array_map(function ($item) {
                    return $this->db->escape_str($item);
                }, $responsavel));
                $subquery .= " AND (ci.id_resp_engenharia IN ('{$responsavelConcatenados}'))";
            } elseif ($responsavel != "") {
                $subquery .= " AND ci.id_resp_engenharia = '" . $this->db->escape_str($responsavel) . "'";
            }
        }

        $status_atributos = $this->cockpit_model->get_state('filter.status_attr');

        if ($status_atributos) {
            if (is_array($status_atributos)) {
                $status_atributosConcatenados = implode("','", array_map(function ($item) {
                    return $this->db->escape_str($item);
                }, $status_atributos));
                $add_sub = '';
                if (in_array("1", $status_atributos)) {
                    $add_sub = " OR i.wf_status_atributos IS NULL";
                }
                $subquery .= " AND (i.wf_status_atributos IN ('{$status_atributosConcatenados}') {$add_sub})";
            } elseif ($status_atributos != "") {
                $subquery .= " AND i.wf_status_atributos = '" . $this->db->escape_str($status_atributos) . "'";
            }
        }

        $status_classificacao = $this->cockpit_model->get_state('filter.status_classificacao_fiscal');

        if ($status_classificacao) {
            if (is_array($status_classificacao)) {
                $statusClassificacaoConcatenados = implode("','", array_map(function ($item) {
                    return $this->db->escape_str($item);
                }, $status_classificacao));
                $subquery .= " AND (i.id_status IN ('{$statusClassificacaoConcatenados}'))";
            } elseif ($status_classificacao != "") {
                $subquery .= " AND i.id_status = '" . $this->db->escape_str($status_classificacao) . "'";
            }
        }

        $status_integracao = $this->cockpit_model->get_state('filter.status_integracao');

        if ($status_integracao) {
            if (is_array($status_integracao)) {
                $status_integracaoConcatenados = implode("','", array_map(function ($item) {
                    return $this->db->escape_str($item);
                }, $status_integracao));
                $add_sub = '';
                if (in_array("1", $status_integracao)) {
                    $add_sub = " OR i.wf_status_integracao IS NULL";
                }
                $subquery .= " AND (i.wf_status_integracao IN ('{$status_integracaoConcatenados}') {$add_sub})";
            } elseif ($status_integracao != "") {
                $subquery .= " AND i.wf_status_integracao = '" . $this->db->escape_str($status_integracao) . "'";
            }
        }

        $status_fiscal = $this->cockpit_model->get_state('filter.status_classificacao');

        if ($status_fiscal) {
            if (is_array($status_fiscal)) {
                $statusClassificacaoConcatenados = implode("','", array_map(function ($item) {
                    return $this->db->escape_str($item);
                }, $status_fiscal));
                $subquery .= " AND (i.id_status IN ('{$statusClassificacaoConcatenados}'))";
            } elseif ($status_fiscal != "") {
                $subquery .= " AND i.id_status = '" . $this->db->escape_str($status_fiscal) . "'";
            }
        }

        $status_preenchimento = $this->cockpit_model->get_state('filter.status_preenchimento');

        if ($status_preenchimento) {
            if (is_array($status_preenchimento)) {
                $statusPreenchimentoConcatenados = implode("','", array_map(function ($item) {
                    return $this->db->escape_str($item);
                }, $status_preenchimento));
                $subquery .= " AND (i.status_attr IN ('{$statusPreenchimentoConcatenados}'))";
            } elseif ($status_preenchimento != "") {
                $subquery .= " AND i.status_attr = '" . $this->db->escape_str($status_preenchimento) . "'";
            }
        }

        $ncm_proposto = $this->cockpit_model->get_state('filter.ncm_proposto');

        if ($ncm_proposto) {
            if (is_array($ncm_proposto)) {
                $statusNcmPropostoConcatenados = implode("','", array_map(function ($item) {
                    return $this->db->escape_str($item);
                }, $ncm_proposto));
                $subquery .= " AND (ci.ncm_proposto IN ('{$statusNcmPropostoConcatenados}'))";
            } elseif ($ncm_proposto != "") {
                $subquery .= " AND ci.ncm_proposto = '" . $this->db->escape_str($ncm_proposto) . "'";
            }
        }

        $prioridade = $this->cockpit_model->get_state('filter.prioridade');

        if ($prioridade) {
            if (is_array($prioridade)) {
                $statusPrioridadeConcatenados = implode("','", array_map(function ($item) {
                    return $this->db->escape_str($item);
                }, $prioridade));
                $subquery .= " AND (i.id_prioridade IN ('{$statusPrioridadeConcatenados}'))";
            } elseif ($prioridade != "") {
                $subquery .= " AND i.id_prioridade = '" . $this->db->escape_str($prioridade) . "'";
            }
        }

        $where_in = "";

        if ($search = $this->cockpit_model->get_state('filter.search')) {
            $keywords = array_filter(preg_split('/\r\n|[\r\n]/', $search));
            $adwhereplus = "";

            // Mais de um termo de pesquisa (multilinhas)
            if (count($keywords) > 1) {
                foreach ($keywords as $keyword) {
                    if ($keyword !== '') {
                        $keyword = str_replace("*", "%",  $keyword);
                        if ($hasPnPrimarioSecundario) {
                            $adwhereplus .= ' i.descricao LIKE "' . $keyword . '" OR i.part_number LIKE "' . trim($keyword) . '" OR i.pn_primario_mpn LIKE "' . trim($keyword) . '" OR i.pn_secundario_ipn LIKE "' . trim($keyword) . '" OR ';
                        } else {
                            $adwhereplus .= ' i.descricao LIKE "' . $keyword . '" OR i.part_number LIKE "' . trim($keyword) . '" OR ';
                        }
                    }
                }
                $subquery .= " AND (" . rtrim($adwhereplus, 'OR ') . ")";
            } elseif (strpos($search, "\n") !== false) {

                $search = str_replace(array("\t", "\r\n", "\s", "\n"), '#', $search);

                $keywords = explode('#', $search);
                $adwhereplus = "";
                $adwhere_go = "";
                foreach ($keywords as $keyword) {
                    if (empty($keyword)) {
                        continue;
                    }
                    $keyword = str_replace("*", "%", $keyword);

                    $keyword = rtrim($keyword, " ");
                    if ($keyword !== '') {
                        if ($hasPnPrimarioSecundario) {
                            $adwhereplus .= ' i.descricao LIKE "' . $keyword . '" OR i.part_number LIKE "' . trim($keyword) . '" OR i.pn_primario_mpn LIKE "' . trim($keyword) . '" OR i.pn_secundario_ipn LIKE "' . trim($keyword) . '" OR ';
                        } else {
                            $adwhereplus .= ' i.descricao LIKE "' . $keyword . '" OR i.part_number LIKE "' . trim($keyword) . '" OR ';
                        }
                    }
                }

                $adwhereplus = rtrim($adwhereplus, 'OR ');

                $where_in .= ' (' . $adwhereplus . ')';
                if (!empty($adwhereplus)) {
                    $this->db->where($where_in, NULL, FALSE);
                }
            } else {
                // Busca única ou com asteriscos
                if (strpos($search, '*') !== false) {
                    $search = str_replace("*", "%", $this->db->escape_like_str($search));
                    if ($hasPnPrimarioSecundario) {
                        $subquery .= " AND (i.descricao LIKE '%$search%' OR i.part_number LIKE '%$search%' OR i.pn_primario_mpn LIKE '%$search%' OR i.pn_secundario_ipn LIKE '%$search%')";
                    } else {
                        $subquery .= " AND (i.descricao LIKE '%$search%' OR i.part_number LIKE '%$search%')";
                    }
                } elseif (strpos($search, ',') !== false) {
                    // Busca com vírgulas
                    $keywords = explode(',', $search);
                    $adwhere_go = "";

                    foreach ($keywords as $keyword) {
                        $keyword = $this->db->escape_like_str(trim($keyword));
                        if ($hasPnPrimarioSecundario) {
                            $adwhere_go .= "i.part_number LIKE '%$keyword%' OR i.pn_primario_mpn LIKE '%$keyword%' OR i.pn_secundario_ipn LIKE '%$keyword%' OR ";
                        } else {
                            $adwhere_go .= "i.part_number LIKE '%$keyword%' OR ";
                        }
                    }
                    $subquery .= " AND (" . rtrim($adwhere_go, 'OR ') . ")";
                } else {
                    // Busca direta sem caracteres especiais
                    $search = $this->db->escape_like_str($search);
                    if ($hasPnPrimarioSecundario) {
                        $subquery .= " AND (i.descricao LIKE '$search' OR i.part_number LIKE '$search' OR i.pn_primario_mpn LIKE '$search' OR i.pn_secundario_ipn LIKE '$search')";
                    } else {
                        $subquery .= " AND (i.descricao LIKE '$search' OR i.part_number LIKE '$search')";
                    }
                }
            }
        }

        if (empty($export)) {
            $subquery .= " GROUP BY ci.id_item ) as sub";
            $result = $this->db->query("$subquery", false);
        } else if ($export == 'Sintetico' || $export == 'Detalhado') {
            $subquery .= " GROUP BY ci.id_item ";
            $result = $this->db->query("$subquery", false);
        }
        return $result;
    }


    // public function cron_insert_cad_item_wf_atributo()
    // {
    // 	$data = $this->db->query("INSERT INTO cad_item_wf_atributo (
    //         part_number,
    //         estabelecimento,
    //         id_empresa,
    //         criado_em
    //       )
    //       SELECT
    //         i.part_number,
    //         i.estabelecimento,
    //         i.id_empresa,
    //         NOW()
    //       FROM
    //         item i
    //       INNER JOIN comex c
    //         ON i.part_number = c.part_number_original
    //         AND i.id_empresa = c.id_empresa
    //         AND i.estabelecimento = c.unidade_negocio
    //       INNER JOIN cad_item cad
    //         ON i.part_number = cad.part_number
    //         AND i.id_empresa = cad.id_empresa
    //         AND i.estabelecimento = cad.estabelecimento
    //       LEFT JOIN cad_item_wf_atributo ciwa
    //         ON i.part_number = ciwa.part_number
    //         AND i.id_empresa = ciwa.id_empresa
    //         AND i.estabelecimento = ciwa.estabelecimento
    //       WHERE c.ind_ecomex = 'EI'
    //       AND (
    //         (cad.id_grupo_tarifario IS NOT NULL AND cad.id_grupo_tarifario <> '')
    //         OR (cad.ncm_proposto IS NOT NULL AND cad.ncm_proposto <> '')
    //       )
    //       AND ciwa.part_number IS NULL
    //       AND ciwa.estabelecimento IS NULL
    //       AND ciwa.id_empresa IS NULL;");

    //     $inseridos = $this->db->affected_rows();


    //     return array('inseridos' => $inseridos);
    // }

    public function get_usuarios_emails_cron()
    {

        $this->db->select("ci.id_resp_engenharia,
                            ci.id_resp_fiscal,
                            u2.nome,
                            u2.email");
        $this->db->join('item i ', 'i.part_number = ci.part_number AND i.estabelecimento = ci.estabelecimento AND i.id_empresa = ci.id_empresa ', 'inner');
        $this->db->join('usuario u2', 'u2.id_usuario = ci.id_resp_engenharia ', 'inner');
        $this->db->where("i.wf_status_atributos IS NULL", NULL, FALSE);
        $this->db->where("i.wf_status_integracao IS NULL", NULL, FALSE);
        $this->db->group_by(['ci.id_resp_engenharia', 'ci.id_resp_fiscal', 'u2.nome', 'u2.email']);

        $query = $this->db->get('cad_item ci');
        $result =  $query->result();

        return $result;
    }

    public function get_itens_cron_email()
    {
        $this->db->query("set sql_mode='';");
        $this->db->select("i.part_number,
                            i.id_empresa,
                            i.estabelecimento,
                            i.descricao,
                            ci.ncm_proposto, 
                            ci.id_resp_engenharia");
        $this->db->join('item i ', 'i.part_number = ci.part_number AND i.estabelecimento = ci.estabelecimento AND i.id_empresa = ci.id_empresa ', 'inner');
        $this->db->join('usuario u2', 'u2.id_usuario = ci.id_resp_engenharia ', 'inner');
        $this->db->where("i.wf_status_atributos IS NULL", NULL, FALSE);
        $this->db->where("i.wf_status_integracao IS NULL", NULL, FALSE);
        $this->db->group_by('ci.id_resp_engenharia');

        $query = $this->db->get('cad_item ci');
        $result =  $query->result();


        return $result;
    }

    // public function cron_atualizacao_itens_novos(){
    //     echo 'fim';
    //     exit;
    //     // $data = $this->db->query("UPDATE item i
    //     //                             INNER JOIN cad_item_wf_atributo ciwa ON
    //     //                                 i.part_number = ciwa.part_number
    //     //                                 AND i.id_empresa = ciwa.id_empresa
    //     //                                 AND i.estabelecimento = ciwa.estabelecimento
    //     //                             SET i.wf_status_atributos = 2,
    //     //                                 i.wf_status_integracao = 1
    //     //                             WHERE i.wf_status_atributos IS NULL
    //     //                                 AND i.wf_status_integracao IS NULL;");

    //         $data = $this->db->query("SELECT i.part_number, i.estabelecimento, i.id_empresa   
    //         FROM item i
    //         WHERE i.wf_status_atributos IS NULL
    //             AND i.wf_status_integracao IS NULL;");

    //             if (!empty($data->result()))
    //             {

    //                 foreach ($data->result() as $item)
    //                 {
    //                     if (empty($item->part_number) || 
    //                     empty($item->estabelecimento) ||
    //                     empty($item->id_empresa))
    //                     {
    //                         continue;
    //                     }

    //                     $this->db->query("UPDATE item i
    //                     SET i.wf_status_atributos = 2,
    //                         i.wf_status_integracao = 1
    //                     WHERE i.part_number = '{$item->part_number}'
    //                         AND i.estabelecimento = '{$item->estabelecimento}'
    //                         AND i.id_empresa = '{$item->id_empresa}';");
    //                         echo $item->part_number.PHP_EOL;
    //                 }
    //             }

    //     $atualizados = $this->db->affected_rows();

    //     return array('atualizados' => $atualizados);
    // }

    public function cron_atualizacao_status_itens_enviados_integracao($loteItens)
    {
        $itens = '';
        $enviados = 0;
        foreach ($loteItens as $item) {
            $itens .= $item->id_item . ',';
        }

        $itens = rtrim($itens, ',');

        $sql = "
            UPDATE item i
                INNER JOIN cad_item ci ON
                    ci.part_number = i.part_number
                    AND ci.id_empresa = i.id_empresa
                    AND ci.estabelecimento = i.estabelecimento
                SET i.wf_status_integracao = 2
                WHERE ci.id_item IN ({$itens})";

        $this->db->query($sql);

        $enviados = $this->db->affected_rows();

        return $enviados;
    }



    private function get_required_attrs($listaAtributos)
    {
        if (empty($listaAtributos)) {
            return;
        }

        $listaAtributos = \json_decode(\json_encode($listaAtributos), TRUE); // Força a conversão para array.

        $ncm_item = [];
        $ncm_item['listaAtributos'] = $listaAtributos;


        \array_multisort($ncm_item["listaAtributos"]);

        return $ncm_item;
    }

    public function get_attr($id_item, $empty = FALSE)
    {
        if (empty($id_item)) {
            return FALSE;
        }

        if ($empty) {
            $this->db->where("codigo", "");
        }

        $this->db->where("id_item", $id_item);

        $query = $this->db->get('cad_item_attr');

        if (!$query->num_rows()) {
            return FALSE;
        }

        return $query->result();
    }

    private function filterUniqueAttributes($inputArray)
    {
        if (!is_array($inputArray) || empty($inputArray)) {
            return [];
        }

        $attributeMap = [];

        foreach ($inputArray as $item) {
            $attribute = $item->atributo;
            $attributeMap[$attribute] = $item;
        }

        $resultArray = array_values($attributeMap);

        usort($resultArray, function ($a, $b) {
            return strtotime($b->atualizado_em) - strtotime($a->atualizado_em);
        });

        return $resultArray;
    }

    private function create_assoc_attrs_structure($ncm_item)
    {
        if (empty($ncm_item)) {
            return;
        }

        $ncm_item = \json_decode(\json_encode($ncm_item), TRUE);

        $arr_dbdata = $ncm_item["defaultAttrs"];
        $arr_attr   = $ncm_item["listaAtributos"];

        $this->assoc_recursively($arr_dbdata, $arr_attr);

        return $arr_attr;
    }

    public function has_attr_cond_new($attr, $cond)
    {
        $cond_op  = $cond["condicao"]["operador"]; // O operador para comparação.
        $cond_val = $cond["condicao"]["valor"];    // O valor para ser comparado.
        $cond_res = FALSE;                         // O resultado final da comparação.

        if (!empty($attr['multivalorado'])) {
            $attr_vals = $attr["dbdata"]["codigo"]; // Multivalorado.

            if (!\is_array($attr_vals)) {
                $attr_vals = explode(",", $attr_vals);
            }

            foreach ($attr_vals as $attr_val) {

                $cond_res = eval("return \"$attr_val\" $cond_op \"$cond_val\";");

                if ($cond_res) {
                    break;
                }
            }
        } else {
            $attr_val = $attr["dbdata"]["codigo"]; // Valor simples.

            if (strtoupper($attr["formaPreenchimento"]) == 'BOOLEANO' && $attr_val > 0) {
                $attr_val = "true";
            }

            $cond_res = eval("return \"$attr_val\" $cond_op \"$cond_val\";");
        }

        return $cond_res;
    }

    private function assoc_recursively($arr_dbdata, &$arr_attr, $parent_attr = NULL)
    {
        if (empty($arr_attr)) {
            return;
        }

        if (empty($arr_dbdata)) {
            $arr_dbdata = [];
        }

        foreach ($arr_attr as &$attr) {
            $attr_template = !empty($attr["atributo"]) ? $attr["atributo"] : $attr;

            $attr_template["dbdata"] = ["codigo" => ""];

            foreach ($arr_dbdata as $dbdata) {
                if (
                    !empty($parent_attr)
                    && !empty($dbdata["atributo_pai"])
                    && $dbdata["atributo_pai"] == $parent_attr["codigo"]
                    && $dbdata["atributo"] == $attr_template["codigo"]
                ) {
                    $attr_template["dbdata"] = $dbdata;
                } else if ($dbdata["atributo"] == $attr_template["codigo"]) {
                    $attr_template["dbdata"] = $dbdata;
                }
            }

            if ($attr_template["atributoCondicionante"] && !empty($attr_template["condicionados"])) {
                foreach ($attr_template["condicionados"] as &$cond) {
                    if ($this->has_attr_cond_new($attr_template, $cond)) {
                        if (!empty($attr_template["dbdata"]['atributo'])) {
                            $this->_attrs_new['sim'][] = $attr_template["dbdata"]['atributo'];
                        }
                    }
                }

                if (!empty($this->_attrs_new['sim']) && in_array($attr_template['codigo'], $this->_attrs_new['sim'])) {
                    $this->assoc_recursively($arr_dbdata, $attr_template["condicionados"], $attr_template);
                }
            } else if (\strtoupper($attr_template["formaPreenchimento"]) == "COMPOSTO" && !empty($attr_template["listaSubatributos"])) {
                $this->assoc_recursively($arr_dbdata, $attr_template["listaSubatributos"], $attr_template);
            }
            if (!empty($attr_template["dbdata"]["id_item"])) {
                if (!empty($attr_template["dbdata"]['atributo'])) {
                    $this->_attrs_new[] = $attr_template["dbdata"]['atributo'];
                }
            }

            if (!empty($attr["atributo"])) {
                $attr["atributo"] = $attr_template;
            } else {
                $attr = $attr_template;
            }
        }
    }

    public function cron_get_itens_prontos_para_envio()
    {
        $this->load->model('catalogo/produto_model');
        $this->load->model('cad_item_attr_model');


        $data = $this->db->query("
                                SELECT 
                                    e2.cnpj AS cpfCnpjRaizRaiz, 
                                    i.descricao_proposta_completa as descricao,
                                    ci.descricao_mercado_local as denominacao, 
                                    ('IMPORTAÇÃO')as modalidade,
                                    ci.ncm_proposto as ncm,
                                    i.part_number as codigosInterno,
                                    ci.id_item 
                                FROM cad_item ci
                                INNER JOIN item i ON
                                    i.part_number = ci.part_number
                                    AND ci.id_empresa = i.id_empresa
                                    AND ci.estabelecimento = i.estabelecimento
                                INNER JOIN empresa e2 ON 
                                    i.id_empresa = e2.id_empresa 
                                WHERE i.wf_status_atributos = 7
                                    AND i.wf_status_integracao = 1");

        $items = $data->result();

        $atributos = [];
        $atributosCompostos = [];
        $atributosMultivalorados = [];
        $atributosCompostosMultivalorados = [];
        foreach ($items as $item) {

            $this->_attrs_new = [];
            $ncm_item = $this->get_required_attrs($this->produto_model->get_attr_ncm($item->ncm));
            $array = $this->get_attr($item->id_item);
            $ncm_item["defaultAttrs"] = $this->filterUniqueAttributes($array);
            $ncm_item["assocAttrs"] = $this->create_assoc_attrs_structure($ncm_item);

            $atributos_ncm = $this->_attrs_new;

            if (isset($atributos_ncm['sim'])) {
                unset($atributos_ncm['sim']);
            }

            if (!empty($atributos_ncm)) {
                $atributos = [];
                $atributosCompostos = [];
                $atributosMultivalorados = [];
                $atributosCompostosMultivalorados = [];
                foreach ($atributos_ncm as $attr) {
                    $data = $this->db->query("SELECT ncm.nome_apresentacao as apresentacao, 
                        ncm.codigo_pai, ncm.codigo as atributo, attr.codigo as valor, 
                        ncm.forma_preenchimento as forma_preenchimento, 
                        ncm.multivalorado as multivalorado 
                        FROM 
                            ncm_atributo  ncm
                        inner join cad_item_attr attr on ncm.codigo = attr.atributo

                        where ncm.codigo = '{$attr}'
                        AND ncm.ncm = '{$item->ncm}'");

                    $result = $data->row();
                    if (!empty($result)) {
                        if ($result->forma_preenchimento == 'COMPOSTO' && $result->multivalorado == 0) {
                            $codigo_pai = $result->atributo;

                            $buscaComposto = $this->db->query("SELECT ncm.nome_apresentacao as apresentacao, 
                                ncm.codigo_pai, ncm.codigo as atributo, attr.codigo as valor, 
                                ncm.forma_preenchimento as forma_preenchimento, 
                                ncm.multivalorado as multivalorado 
                                FROM 
                                    ncm_atributo  ncm
                                inner join cad_item_attr attr on ncm.codigo = attr.atributo
                                where ncm.codigo_pai = '{$attr}'
                                AND ncm.ncm = '{$item->ncm}' group by attr.atributo");

                            $resultComposto = $buscaComposto->result();
                            $valoresCompostos = [];
                            if (!empty($resultComposto)) {
                                foreach ($resultComposto as $r) {
                                    $valoresCompostos[] = ["atributo" => $r->atributo, "valor" => $r->valor];
                                }
                            }

                            $atributosCompostos[] = [
                                "atributo" => $codigo_pai,
                                "valores" => $valoresCompostos
                            ];
                        } else if ($result->multivalorado == 1 && $result->forma_preenchimento != 'COMPOSTO') {
                            $atributosMultivalorados[] = [
                                "atributo" => $result->atributo,
                                "valores" => [$result->valor]
                            ];
                        } else if ($result->forma_preenchimento == 'COMPOSTO' && $result->multivalorado == 1) {
                            $atributosCompostosMultivalorados[] = [
                                "atributo" => $result->atributo,
                                "valores" => [
                                    [
                                        ["atributo" => $result->atributo, "valor" => $result->valor]
                                    ]
                                ],
                            ];
                        } else {
                            $atributos[] =
                                ["atributo" => $result->atributo, "valor" => $result->valor];
                        }
                    }
                }
            }

            $item->atributos = $atributos;
            $item->atributosCompostos = $atributosCompostos;
            $item->atributosMultivalorados = $atributosMultivalorados;
            $item->atributosCompostosMultivalorados = $atributosCompostosMultivalorados;
            $item->codigosInterno = [$item->codigosInterno];
        }

        return $items;
    }

    public function cron_get_atributos_itens_prontos_para_envio($id_item, $ncm)
    {

        $data = $this->db->query("
            SELECT attr.codigo, attr.atributo, ncm.modalidade, ncm.forma_preenchimento, ncm.multivalorado FROM gestaotarifaria_cnh.cad_item_attr attr
            inner join ncm_atributo ncm on    ncm.codigo = attr.atributo
            where ncm.ncm = '{$ncm}'
            AND ncm.forma_preenchimento <> 'COMPOSTO'
            AND ncm.multivalorado IS NULL
            AND attr.id_item = '{$id_item}'");

        $result = $data->result();

        $atributos = [];
        foreach ($result as $row) {
            $atributos[] = [
                'atributo' => $row->atributo,
                'valores' => $row->codigo
            ];
        }

        return array_values($atributos);
    }

    public function cron_get_atributos_compostos_itens_prontos_para_envio($id_item, $ncm)
    {
        // Obter atributos compostos
        $data = $this->db->query("
            SELECT attr.codigo, attr.atributo, ncm.modalidade, ncm.forma_preenchimento, ncm.multivalorado FROM gestaotarifaria_cnh.cad_item_attr attr
            inner join ncm_atributo ncm on    ncm.codigo = attr.atributo
            where ncm.ncm = '{$ncm}'
            AND ncm.forma_preenchimento = 'COMPOSTO'
            AND ncm.multivalorado IS NULL
            AND attr.id_item = '{$id_item}'");


        $result = $data->result();

        $atributosCompostos = [];
        foreach ($result as $row) {
            if (!isset($atributosCompostos[$row->atributo_pai])) {
                $atributosCompostos[$row->atributo_pai] = [
                    'atributo' => $row->atributo_pai,
                    'valores' => []
                ];
            }
            $atributosCompostos[$row->atributo_pai]['valores'][] = $row;
        }

        return array_values($atributosCompostos);
    }

    public function cron_get_atributos_multivalorados_itens_prontos_para_envio($id_item, $ncm)
    {

        $data = $this->db->query("
            SELECT attr.codigo, attr.atributo, ncm.modalidade, ncm.forma_preenchimento, ncm.multivalorado FROM gestaotarifaria_cnh.cad_item_attr attr
            inner join ncm_atributo ncm on    ncm.codigo = attr.atributo
            where ncm.ncm = '{$ncm}'
            AND ncm.multivalorado IS NOT NULL
            AND attr.id_item = '{$id_item}'");



        $result = $data->result();

        $atributosMultivalorados = [];
        foreach ($result as $row) {
            if (!isset($atributosMultivalorados[$row->atributo])) {
                $atributosMultivalorados[$row->atributo] = [
                    'atributo' => $row->atributo,
                    'valores' => []
                ];
            }
            $atributosMultivalorados[$row->atributo]['valores'][] = $row->valor;
        }

        return array_values($atributosMultivalorados);
    }

    /**
     * Busca todos os itens de uma NCM específica (para seleção em massa)
     * @param string $ncm NCM a ser consultada
     * @param int $id_empresa ID da empresa
     * @return array Lista de todos os itens da NCM
     */
    public function get_all_items_from_ncm($ncm, $id_empresa)
    {
        $this->load->model('empresa_model');
        $empresa = $this->empresa_model->get_entry($id_empresa);
        $data['campos_adicionais'] = explode("|", $empresa->campos_adicionais);

        // Determinar tipo de item (importado ou nacional)
        $tipo_item = 'importado'; // Default
        if ($this->get_state('filter.tipo_item')) {
            $tipo_item = $this->get_state('filter.tipo_item');
        }

        // Selecionar apenas os campos necessários para seleção em massa
        $this->db->select('ci.id_item, ci.part_number, ci.estabelecimento, i.descricao as descricao_curta');

        if ($tipo_item == 'importado') {
            $this->db->join('item i', 'ci.part_number = i.part_number AND ci.id_empresa = i.id_empresa AND ci.estabelecimento = i.estabelecimento', 'inner');

            if ($this->db->table_exists('comex')) {
                $this->db->join('comex', 'comex.part_number_original = i.part_number AND comex.id_empresa = i.id_empresa AND comex.unidade_negocio = i.estabelecimento', 'inner');
                $this->db->where("comex.ind_ecomex = 'EI'");
            }

            $this->db->where('ci.id_empresa', $id_empresa);
            $this->db->where('ci.ncm_proposto', $ncm);
        } else {
            $this->db->join('item i', 'ci.part_number = i.part_number AND ci.id_empresa = i.id_empresa AND ci.estabelecimento = i.estabelecimento', 'inner');
            $this->db->where("(i.wf_status_atributos IS NULL OR i.wf_status_atributos = '1')");
            $this->db->where('ci.id_empresa', $id_empresa);
            $this->db->where('ci.ncm_proposto', $ncm);
        }

        $this->db->group_by('ci.id_item');
        $this->db->order_by('ci.part_number', 'ASC');

        $query = $this->db->get('cad_item ci');
        return $query->result();
    }
}
