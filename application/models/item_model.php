<?php

class Item_model extends MY_Model
{

    public $_table = 'item';
    public $_status = '';
    private $separator = null;

    const DIANA_STATUS_PENDENTE = 'pendente_triagem';
    const DIANA_STATUS_APROVADA = 'triagem_aprovada';
    const DIANA_STATUS_REPROVADA = 'triagem_reprovada';
    const DIANA_STATUS_FALHA = 'falha_triagem';

    public function __construct()
    {
        parent::__construct();

        $this->separator = get_company_separator(null);
    }

    public function set_search($search = NULL)
    {
        $search = addslashes($search);
        $this->set_state('filter.part_numbers', $search);
    }

    public function set_status($status)
    {
        $this->_status = $status;
    }

    public function get_list_consultor()
    {
        $this->db->select("u.nome, u.id_usuario");
        $this->db->from("item_log_status_sla s");
        $this->db->join("usuario u", "u.id_usuario = s.id_usuario_responsavel");
        $this->db->group_by(array("u.nome"));

        $query = $this->db->get();
        return $query->result();
    }

    public function get_status_consult_report()
    {

        $this->db->select("DAY(s.data_novo_status) AS dia", false);
        $this->db->select("s.id_empresa, ci.descricao_mercado_local, ci.dat_disp_homologacao", false);
        $this->db->select("s.estabelecimento", false);
        $this->db->select("s.part_number", false);
        $this->db->select("emp.nome_fantasia as nome_empresa", false);

        $this->db->select("u.email, u.nome");

        $this->db->from("item_log_status_sla s");
        $this->db->join("usuario u", "u.id_usuario = s.id_usuario_responsavel");
        $this->db->join("cad_item ci", "ci.part_number = s.part_number AND ci.estabelecimento = s.estabelecimento AND ci.id_empresa = s.id_empresa");
        $this->db->join("empresa emp", "emp.id_empresa = s.id_empresa");

        $this->db->where_in("s.novo_status_descricao", array('Pendente de Informações', 'Em Revisão', 'Pendente de Homologação'));
        $this->db->where("s.status_anterior_descricao <>", "Pendente de Homologação");

        if ($cockpit_status_mes = $this->get_state('filter.cockpit_status_mes')) {
            $this->db->where("MONTH(s.data_novo_status)", $cockpit_status_mes);
        }

        if ($cockpit_status_ano = $this->get_state('filter.cockpit_status_ano')) {
            $this->db->where("YEAR(s.data_novo_status)", $cockpit_status_ano);
        }

        if ($cockpit_status_consultor = $this->get_state('filter.cockpit_status_consultor')) {
            $this->db->where_in("u.id_usuario", $cockpit_status_consultor);
        }

        if ($cockpit_status_empresa = $this->get_state('filter.cockpit_status_empresa')) {
            $this->db->where_in("s.id_empresa", $cockpit_status_empresa);
        }

        $this->db->order_by("u.nome");
        $query = $this->db->get();
        return $query->result();
    }

    public function get_status_consult()
    {

        $this->db->select("DAY(s.data_novo_status) AS dia", false);
        $this->db->select("COUNT(DISTINCT CONCAT(s.id_empresa, s.estabelecimento, s.part_number)) AS qtd_itens", false);
        $this->db->select("u.email, u.nome");

        $this->db->from("item_log_status_sla s");
        $this->db->join("usuario u", "u.id_usuario = s.id_usuario_responsavel");

        $this->db->where_in("s.novo_status_descricao", array('Pendente de Informações', 'Em Revisão', 'Pendente de Homologação'));
        $this->db->where("s.status_anterior_descricao <>", "Pendente de Homologação");

        if ($cockpit_status_mes = $this->get_state('filter.cockpit_status_mes')) {
            $this->db->where("MONTH(s.data_novo_status)", $cockpit_status_mes);
        }

        if ($cockpit_status_ano = $this->get_state('filter.cockpit_status_ano')) {
            $this->db->where("YEAR(s.data_novo_status)", $cockpit_status_ano);
        }

        if ($cockpit_status_consultor = $this->get_state('filter.cockpit_status_consultor')) {
            $this->db->where_in("u.id_usuario", $cockpit_status_consultor);
        }

        if ($cockpit_status_empresa = $this->get_state('filter.cockpit_status_empresa')) {
            $this->db->where_in("s.id_empresa", $cockpit_status_empresa);
        }

        $this->db->group_by(array("u.email"));

        $query = $this->db->get();
        return $query->result();
    }

    public function get_saldo_mes($data_inicial = null, $data_final = null, $id_empresa = null)
    {
        $this->load->model('empresa_model');
        $this->empresa_model->mail_settings_finance();
        $dados_empresa = $this->empresa_model->get_settings_finance(['habilitar_uso_franquia' => 1], $id_empresa);

        if (empty($dados_empresa)) {
            return [
                'habilitar_uso_franquia' => 0,
                'franquia_disponivel' => 0,
                'habilitar_cobrancas_adicionais' => 0,
                'valor_padrao_default' => 0,
                'valor_quimico_default' => 0,
                'habilitar_notificacoes' => 0,
                'destinatarios_excedente' => '',
                'percentual_primeira_notificacao' => 0,
                'percentual_segunda_notificacao' => 0,
                'habilitar_bloqueio' => 0,
                'tipo_bloqueio' => 0,
                'percentual_excedente' => 0,
                'total_sem_excedente' => 0,
                'total_franquia' => 0,
                'total_franquia_com_excedente' => 0
            ];
        };

        $total_contratado = !empty($dados_empresa->quantidade_franquia_mensal) ? $dados_empresa->quantidade_franquia_mensal : 0;
        $total_sem_excedente = $total_contratado;
        if ($dados_empresa->tipo_bloqueio == 0) {
            $total_contratado += $total_contratado * ($dados_empresa->percentual_excedente / 100);
        }

        if (empty($data_inicial)) {
            $data_inicial = date('Y-m-01');
        }

        if (empty($data_final)) {
            $data_final = date('Y-m-t');
        }

        $totais = $this->get_totais_franquia($data_inicial, $data_final);

        $totais_criados = 0;
        $totais_trabalhados = 0;
        $totais_processados = 0;
        if (!empty($totais)) {
            $totais_criados = $totais->Total_Itens_Criados + $totais->Total_Itens_Quimicos_Criados;
            $totais_trabalhados = $totais->Total_Itens_Reclassificados + $totais->Total_Itens_Quimicos_Reclassificados;

            $totais_processados = $totais_trabalhados + $totais_criados;
        }

        if (isset($totais_processados) && isset($total_contratado)) {

            return [
                'habilitar_uso_franquia' => 1,
                'franquia_disponivel' => $total_contratado - $totais_processados,
                'habilitar_cobrancas_adicionais' => $dados_empresa->habilitar_cobrancas_adicionais,
                'valor_padrao_default' => $dados_empresa->valor_padrao_default,
                'valor_quimico_default' => $dados_empresa->valor_quimico_default,
                'habilitar_notificacoes' => $dados_empresa->habilitar_notificacoes,
                'destinatarios_excedente' => $dados_empresa->destinatarios_excedente,
                'percentual_primeira_notificacao' => $dados_empresa->percentual_primeira_notificacao,
                'percentual_segunda_notificacao' => $dados_empresa->percentual_segunda_notificacao,
                'habilitar_bloqueio' => $dados_empresa->habilitar_bloqueio,
                'tipo_bloqueio' => $dados_empresa->tipo_bloqueio,
                'percentual_excedente' => $dados_empresa->percentual_excedente,
                'total_sem_excedente' => $total_sem_excedente - $totais_processados,
                'total_franquia' => $dados_empresa->quantidade_franquia_mensal,
                'total_franquia_com_excedente' => round($dados_empresa->quantidade_franquia_mensal + (($dados_empresa->percentual_excedente / 100) * $dados_empresa->quantidade_franquia_mensal))

            ];
        } else {

            return [
                'habilitar_uso_franquia' => 1,
                'franquia_disponivel' => $dados_empresa->quantidade_franquia_mensal,
                'habilitar_cobrancas_adicionais' => $dados_empresa->habilitar_cobrancas_adicionais,
                'valor_padrao_default' => $dados_empresa->valor_padrao_default,
                'valor_quimico_default' => $dados_empresa->valor_quimico_default,
                'habilitar_notificacoes' => $dados_empresa->habilitar_notificacoes,
                'destinatarios_excedente' => $dados_empresa->destinatarios_excedente,
                'percentual_primeira_notificacao' => $dados_empresa->percentual_primeira_notificacao,
                'percentual_segunda_notificacao' => $dados_empresa->percentual_segunda_notificacao,
                'habilitar_bloqueio' => $dados_empresa->habilitar_bloqueio,
                'tipo_bloqueio' => $dados_empresa->tipo_bloqueio,
                'percentual_excedente' => $dados_empresa->percentual_excedente,
                'total_sem_excedente' => $total_sem_excedente - $totais_processados,
                'total_franquia' => $dados_empresa->quantidade_franquia_mensal,
                'total_franquia_com_excedente' => round($dados_empresa->quantidade_franquia_mensal + (($dados_empresa->percentual_excedente / 100) * $dados_empresa->quantidade_franquia_mensal))


            ];
        }
    }

    public function get_totais_franquia($data_inicial, $data_final)
    {

        $id_empresa = sess_user_company();
        // $data_inicial = date('Y-m-01');  
        // $data_final = date('Y-m-t');      

        $query = $this->db->query("
        
        SELECT
            t.id_empresa,
            t.nome_fantasia,
            t.mes,  
            SUM(t.itens_criados) AS Total_Itens_Criados,
            SUM(t.itens_reclassificados) AS Total_Itens_Reclassificados,
            SUM(t.itens_quimicos_criados) AS Total_Itens_Quimicos_Criados,
            SUM(t.itens_quimicos_reclassificados) AS Total_Itens_Quimicos_Reclassificados
        FROM (
            SELECT
                i.id_empresa,
                e.nome_fantasia,
                DATE_FORMAT(ilss.data_status_anterior, '%Y-%m') AS mes,
                0 AS itens_criados,
                COUNT(*) AS itens_reclassificados,
                0 AS itens_quimicos_criados,
                0 AS itens_quimicos_reclassificados
            FROM item_log_status_sla ilss 
            JOIN empresa e ON e.id_empresa = ilss.id_empresa 
            JOIN item i ON ilss.id_empresa = i.id_empresa 
                AND ilss.estabelecimento = i.estabelecimento 
                AND ilss.part_number = i.part_number
            JOIN cad_item c ON ilss.id_empresa = c.id_empresa 
                AND ilss.estabelecimento = c.estabelecimento 
                AND ilss.part_number = c.part_number
            WHERE 
                (ilss.status_anterior_descricao = 'Homologado' AND ilss.novo_status_descricao <> 'Homologado')
                AND ilss.data_status_anterior BETWEEN '$data_inicial' AND '$data_final'
                AND i.id_empresa = '$id_empresa'
                AND i.gestao_mensal = 1
                AND i.dat_criacao < '$data_inicial'
                AND SUBSTR(c.ncm_proposto,1,2) NOT IN
                   ('25','26','27','28','29','30','31','32','33','34','35','36','37','38','50','51','52','53','54','55','56','57','58','59','60','61','62','63')
            GROUP BY i.id_empresa, e.nome_fantasia, mes
        
            UNION

            SELECT
                i.id_empresa,
                e.nome_fantasia,
                DATE_FORMAT(i.dat_criacao, '%Y-%m') AS mes,
                COUNT(*) AS itens_criados,
                0 AS itens_reclassificados,
                0 AS itens_quimicos_criados,
                0 AS itens_quimicos_reclassificados
            FROM item i
            JOIN empresa e ON e.id_empresa = i.id_empresa 
            LEFT JOIN cad_item c ON i.id_empresa = c.id_empresa 
                AND i.estabelecimento = c.estabelecimento 
                AND i.part_number = c.part_number
                AND SUBSTR(c.ncm_proposto,1,2) NOT IN 
                    ('25','26','27','28','29','30','31','32','33','34','35','36','37','38','50','51','52','53','54','55','56','57','58','59','60','61','62','63')
            WHERE i.dat_criacao BETWEEN '$data_inicial' AND '$data_final'
                AND i.id_empresa = '$id_empresa'
                AND i.gestao_mensal = 1
            GROUP BY i.id_empresa, e.nome_fantasia, mes
        
            UNION
        
            SELECT
                i.id_empresa,
                e.nome_fantasia,
                DATE_FORMAT(ilss.data_status_anterior, '%Y-%m') AS mes,
                0 AS itens_criados,
                0 AS itens_reclassificados,
                0 AS itens_quimicos_criados,
                COUNT(*) AS itens_quimicos_reclassificados
            FROM item_log_status_sla ilss 
            JOIN empresa e ON e.id_empresa = ilss.id_empresa 
            JOIN item i ON ilss.id_empresa = i.id_empresa 
                AND ilss.estabelecimento = i.estabelecimento 
                AND ilss.part_number = i.part_number
            JOIN cad_item c ON ilss.id_empresa = c.id_empresa 
                AND ilss.estabelecimento = c.estabelecimento 
                AND ilss.part_number = c.part_number
                AND SUBSTR(c.ncm_proposto,1,2) IN ('25','26','27','28','29','30','31','32','33','34','35','36','37','38','50','51','52','53','54','55','56','57','58','59','60','61','62','63')
            WHERE 
                (ilss.status_anterior_descricao = 'Homologado' AND ilss.novo_status_descricao <> 'Homologado')
                AND ilss.data_status_anterior BETWEEN '$data_inicial' AND '$data_final'
                AND i.id_empresa = '$id_empresa'
                AND i.gestao_mensal = 1
              
                AND ifnull(c.ncm_proposto,'') <> ''
                AND i.dat_criacao < '$data_inicial'
            GROUP BY i.id_empresa, e.nome_fantasia, mes
        
            UNION

            SELECT
                i.id_empresa,
                e.nome_fantasia,
                DATE_FORMAT(i.dat_criacao, '%Y-%m') AS mes,
                0 AS itens_criados,
                0 AS itens_reclassificados,
                count(*) AS itens_quimicos_criados,
                0 AS itens_quimicos_reclassificados
            FROM item i
            JOIN empresa e ON e.id_empresa = i.id_empresa 
            LEFT JOIN cad_item c ON i.id_empresa = c.id_empresa 
                AND i.estabelecimento = c.estabelecimento 
                AND i.part_number = c.part_number
                AND SUBSTR(c.ncm_proposto,1,2) IN ('25','26','27','28','29','30','31','32','33','34','35','36','37','38','50','51','52','53','54','55','56','57','58','59','60','61','62','63')
            WHERE i.dat_criacao BETWEEN '$data_inicial' AND '$data_final'
                AND i.id_empresa = '$id_empresa'
                AND i.gestao_mensal = 1

                AND ifnull(c.ncm_proposto,'') <> ''
            GROUP BY i.id_empresa, e.nome_fantasia, mes
            
        ) t
   
        GROUP BY t.id_empresa, t.nome_fantasia, t.mes;");

        return $query->row();
    }
    public function get_part_numbers_franquia($ano, $mes, $data_inicio, $data_fim, $limite, $id_empresa = null)
    {
        // $id_empresa = sess_user_company();

        // Construir a SQL dinamicamente
        $sql = "
        SELECT 
            resultado.*,
            dados.qtd_homologados,
            dados.data_homologado,
            dados.part_number as pn_homologado,
            '$mes' as mes,
            '$ano' as ano
        FROM (
            -- PART NUMBERS RECLASSIFICADOS - NÃO QUÍMICOS
            SELECT
            i.dat_criacao as data_criacao,
            c.ncm_proposto,
            c.dat_disp_homologacao as data_envio_cliente,
            ep.nome as prioridade,
            'NÃO' as quimico,
            ep.valor_padrao as custo,
                i.id_empresa,
                e.nome_fantasia,
                i.part_number,
                i.estabelecimento,
                DATE_FORMAT(ilss.data_status_anterior, '%Y-%m-%d') AS data_trabalho,
                'Reclassificado' AS tipo_operacao,
                'Não Químico' AS tipo_item,
                ilss.data_status_anterior
            FROM item_log_status_sla ilss 
            JOIN empresa e ON e.id_empresa = ilss.id_empresa 
       
            JOIN item i ON ilss.id_empresa = i.id_empresa 
                AND ilss.estabelecimento = i.estabelecimento 
                AND ilss.part_number = i.part_number
            LEFT JOIN empresa_prioridades ep ON i.id_empresa = ep.id_empresa  and i.id_prioridade = ep.id_prioridade
            JOIN cad_item c ON ilss.id_empresa = c.id_empresa 
                AND ilss.estabelecimento = c.estabelecimento 
                AND ilss.part_number = c.part_number
            WHERE 
                (ilss.status_anterior_descricao = 'Homologado' AND ilss.novo_status_descricao <> 'Homologado')
                AND ilss.data_status_anterior BETWEEN ? AND ?
                AND i.gestao_mensal = 1
                AND i.dat_criacao < ?
                AND SUBSTR(c.ncm_proposto,1,2) NOT IN
                ('25','26','27','28','29','30','31','32','33','34','35','36','37','38','50','51','52','53','54','55','56','57','58','59','60','61','62','63')
                AND i.id_empresa = ?
    
            UNION ALL
    
            -- PART NUMBERS CRIADOS - NÃO QUÍMICOS
            SELECT
            i.dat_criacao as data_criacao,
            c.ncm_proposto,
            c.dat_disp_homologacao as data_envio_cliente,
            ep.nome as prioridade,
            'NÃO' as quimico,
            ep.valor_padrao as custo,
                i.id_empresa,
                e.nome_fantasia,
                i.part_number,
                i.estabelecimento,
                DATE_FORMAT(i.dat_criacao, '%Y-%m-%d') AS data_trabalho,
                'Criado' AS tipo_operacao,
                'Não Químico' AS tipo_item,
                i.dat_criacao AS data_status_anterior
            FROM item i
            JOIN empresa e ON e.id_empresa = i.id_empresa 
            LEFT JOIN empresa_prioridades ep ON i.id_empresa = ep.id_empresa  and i.id_prioridade = ep.id_prioridade

            LEFT JOIN cad_item c ON i.id_empresa = c.id_empresa 
                AND i.estabelecimento = c.estabelecimento 
                AND i.part_number = c.part_number
                AND SUBSTR(c.ncm_proposto,1,2) NOT IN 
                    ('25','26','27','28','29','30','31','32','33','34','35','36','37','38','50','51','52','53','54','55','56','57','58','59','60','61','62','63')
            WHERE i.dat_criacao BETWEEN ? AND ?
                AND i.gestao_mensal = 1
                AND i.id_empresa = ?
    
            UNION ALL
    
            -- PART NUMBERS RECLASSIFICADOS - QUÍMICOS
            SELECT
            i.dat_criacao as data_criacao,
            c.ncm_proposto,
            c.dat_disp_homologacao as data_envio_cliente,
            ep.nome as prioridade,
            'SIM' as quimico,
            ep.valor_quimico as custo,
                i.id_empresa,
                e.nome_fantasia,
                i.part_number,
                i.estabelecimento,
                DATE_FORMAT(ilss.data_status_anterior, '%Y-%m-%d') AS data_trabalho,
                'Reclassificado' AS tipo_operacao,
                'Químico' AS tipo_item,
                ilss.data_status_anterior
            FROM item_log_status_sla ilss 
            JOIN empresa e ON e.id_empresa = ilss.id_empresa 
           

            JOIN item i ON ilss.id_empresa = i.id_empresa 
                AND ilss.estabelecimento = i.estabelecimento 
                AND ilss.part_number = i.part_number
            LEFT JOIN empresa_prioridades ep ON i.id_empresa = ep.id_empresa  and i.id_prioridade = ep.id_prioridade
            JOIN cad_item c ON ilss.id_empresa = c.id_empresa 
                AND ilss.estabelecimento = c.estabelecimento 
                AND ilss.part_number = c.part_number
                AND SUBSTR(c.ncm_proposto,1,2) IN ('25','26','27','28','29','30','31','32','33','34','35','36','37','38','50','51','52','53','54','55','56','57','58','59','60','61','62','63')
            WHERE 
                (ilss.status_anterior_descricao = 'Homologado' AND ilss.novo_status_descricao <> 'Homologado')
                AND ilss.data_status_anterior BETWEEN ? AND ?
                AND i.gestao_mensal = 1
                AND ifnull(c.ncm_proposto,'') <> ''
                AND i.dat_criacao < ?
                AND i.id_empresa = ?
    
            UNION ALL
    
            -- PART NUMBERS CRIADOS - QUÍMICOS
            SELECT
            i.dat_criacao as data_criacao,
            c.ncm_proposto,
            c.dat_disp_homologacao as data_envio_cliente,
            ep.nome as prioridade,
            'SIM' as quimico,
            ep.valor_quimico as custo,
                i.id_empresa,
                e.nome_fantasia,
                i.part_number,
                i.estabelecimento,
                DATE_FORMAT(i.dat_criacao, '%Y-%m-%d') AS data_trabalho,
                'Criado' AS tipo_operacao,
                'Químico' AS tipo_item,
                i.dat_criacao AS data_status_anterior
            FROM item i
            JOIN empresa e ON e.id_empresa = i.id_empresa 
            LEFT JOIN empresa_prioridades ep ON i.id_empresa = ep.id_empresa  and i.id_prioridade = ep.id_prioridade

            LEFT JOIN cad_item c ON i.id_empresa = c.id_empresa 
                AND i.estabelecimento = c.estabelecimento 
                AND i.part_number = c.part_number
                AND SUBSTR(c.ncm_proposto,1,2) IN ('25','26','27','28','29','30','31','32','33','34','35','36','37','38','50','51','52','53','54','55','56','57','58','59','60','61','62','63')
            WHERE i.dat_criacao BETWEEN ? AND ?
                AND i.gestao_mensal = 1
                AND ifnull(c.ncm_proposto,'') <> ''
                AND i.id_empresa = ?
        ) resultado
        LEFT JOIN (
            SELECT  
                COUNT(*) AS qtd_homologados, 
                MAX(data_novo_status) AS data_homologado, 
                part_number
            FROM 
                item_log_status_sla
            WHERE 
                novo_status_descricao = 'Homologado'
            GROUP BY part_number
        ) dados ON dados.part_number = resultado.part_number
        ORDER BY resultado.data_status_anterior DESC";

        // Primeiro, contar o total de registros
        $count_sql = "
        SELECT COUNT(*) as total FROM (
            SELECT 1
            FROM item_log_status_sla ilss 
            JOIN empresa e ON e.id_empresa = ilss.id_empresa 
            JOIN item i ON ilss.id_empresa = i.id_empresa 
                AND ilss.estabelecimento = i.estabelecimento 
                AND ilss.part_number = i.part_number
            JOIN cad_item c ON ilss.id_empresa = c.id_empresa 
                AND ilss.estabelecimento = c.estabelecimento 
                AND ilss.part_number = c.part_number
            WHERE 
                (ilss.status_anterior_descricao = 'Homologado' AND ilss.novo_status_descricao <> 'Homologado')
                AND ilss.data_status_anterior BETWEEN ? AND ?
                AND i.gestao_mensal = 1
                AND i.dat_criacao < ?
                AND SUBSTR(c.ncm_proposto,1,2) NOT IN
                ('25','26','27','28','29','30','31','32','33','34','35','36','37','38','50','51','52','53','54','55','56','57','58','59','60','61','62','63')
                AND i.id_empresa = ?
            UNION ALL
            SELECT 1
            FROM item i
            JOIN empresa e ON e.id_empresa = i.id_empresa 
            LEFT JOIN cad_item c ON i.id_empresa = c.id_empresa 
                AND i.estabelecimento = c.estabelecimento 
                AND i.part_number = c.part_number
                AND SUBSTR(c.ncm_proposto,1,2) NOT IN 
                    ('25','26','27','28','29','30','31','32','33','34','35','36','37','38','50','51','52','53','54','55','56','57','58','59','60','61','62','63')
            WHERE i.dat_criacao BETWEEN ? AND ?
                AND i.gestao_mensal = 1
                AND i.id_empresa = ?
            UNION ALL
            SELECT 1
            FROM item_log_status_sla ilss 
            JOIN empresa e ON e.id_empresa = ilss.id_empresa 
            JOIN item i ON ilss.id_empresa = i.id_empresa 
                AND ilss.estabelecimento = i.estabelecimento 
                AND ilss.part_number = i.part_number
            JOIN cad_item c ON ilss.id_empresa = c.id_empresa 
                AND ilss.estabelecimento = c.estabelecimento 
                AND ilss.part_number = c.part_number
                AND SUBSTR(c.ncm_proposto,1,2) IN ('25','26','27','28','29','30','31','32','33','34','35','36','37','38','50','51','52','53','54','55','56','57','58','59','60','61','62','63')
            WHERE 
                (ilss.status_anterior_descricao = 'Homologado' AND ilss.novo_status_descricao <> 'Homologado')
                AND ilss.data_status_anterior BETWEEN ? AND ?
                AND i.gestao_mensal = 1
                AND ifnull(c.ncm_proposto,'') <> ''
                AND i.dat_criacao < ?
                AND i.id_empresa = ?
            UNION ALL
            SELECT 1
            FROM item i
            JOIN empresa e ON e.id_empresa = i.id_empresa 
            LEFT JOIN cad_item c ON i.id_empresa = c.id_empresa 
                AND i.estabelecimento = c.estabelecimento 
                AND i.part_number = c.part_number
                AND SUBSTR(c.ncm_proposto,1,2) IN ('25','26','27','28','29','30','31','32','33','34','35','36','37','38','50','51','52','53','54','55','56','57','58','59','60','61','62','63')
            WHERE i.dat_criacao BETWEEN ? AND ?
                AND i.gestao_mensal = 1
                AND ifnull(c.ncm_proposto,'') <> ''
                AND i.id_empresa = ?
        ) contagem";

        // Executar contagem
        $count_query = $this->db->query($count_sql, [
            $data_inicio,
            $data_fim,
            $data_inicio,
            $id_empresa,  // Reclassificados não químicos
            $data_inicio,
            $data_fim,
            $id_empresa,                // Criados não químicos
            $data_inicio,
            $data_fim,
            $data_inicio,
            $id_empresa,  // Reclassificados químicos
            $data_inicio,
            $data_fim,
            $id_empresa                 // Criados químicos
        ]);

        $total = $count_query->row()->total;

        // Se o total for menor ou igual ao limite, retorna vazio
        if ($total <= $limite) {
            return [];
        }

        // Calcular o LIMIT
        $limit_value = $total - $limite;
        $sql .= " LIMIT " . $limit_value;

        // Executar query principal
        $query = $this->db->query($sql, [
            $data_inicio,
            $data_fim,
            $data_inicio,
            $id_empresa,  // Reclassificados não químicos
            $data_inicio,
            $data_fim,
            $id_empresa,                // Criados não químicos
            $data_inicio,
            $data_fim,
            $data_inicio,
            $id_empresa,  // Reclassificados químicos
            $data_inicio,
            $data_fim,
            $id_empresa                 // Criados químicos
        ]);

        return $query->result();
    }

    public function get_entries_export_mestre_itens($exportar_meste = false)
    {
        $id_empresa = sess_user_company();
        $this->load->model('empresa_model');
        $empresa = $this->empresa_model->get_entry(sess_user_company());
        $data['campos_adicionais'] = explode("|", $empresa->campos_adicionais);
        $hasPrioridade =  in_array('prioridade', $data['campos_adicionais']);
        $hasPnPrimarioSecundario = in_array('pn_primario_secundario', $data['campos_adicionais']);
        $cnpj_raiz_logado = $this->empresa_model->get_entry_cnpj_raiz($id_empresa);

        $this->db->where('i.id_empresa', sess_user_company());

        if ($exportar_meste) {
            $this->db->select('
                i.id_empresa,
                i.part_number,
                i.status_attr,
                i.descricao as descricao_curta,
                i.descricao_proposta_completa as descricao_completa,
                i.descricao_global,
                i.estabelecimento,
                ci.ncm_proposto,
                ci.num_ex_ii,
                ci.num_ex_ipi,
                gt.descricao,
                i.cod_owner, o.codigo as owner_codigo, o.descricao as owner_descricao,
                ep.nome as empresa_prioridade,
                i.integracao_novo_material,
                s.status as status_formatado,
                i.wf_status_atributos as status_atributos,
                i.wf_status_integracao as status_integracao,
                i.status_attr as status_preenchimento,
                i.evento,
                comex.data_invoice,
                i.sistema_origem,
                ci.status_exportacao,
                i.dat_criacao,
                i.data_modificacao,
                GROUP_CONCAT(DISTINCT rg.nome) as responsaveis_gestores_nomes
            ');

            $this->db->select(
                '
                CASE WHEN comex.ind_ecomex = "EI" THEN 1 ELSE 0 END as importado',
                false
            );
        } else {
            $this->db->select("
                i.part_number,
                i.status_attr,
                i.pn_primario_mpn,
                i.pn_secundario_ipn,
                i.descricao,
                ci.descricao_mercado_local,
                i.inf_adicionais,
                i.info_adicional,
                i.tag,
                i.descricao_proposta_completa,
                ci.ncm_proposto,
                u.nome,
                ci.id_item,
                i.peso,
                i.prioridade,
                ci.num_ex_ipi,
                i.item_ja_homologado,
                (SELECT MAX(cihf_sub.criado_em)
                FROM cad_item_homologacao cihf_sub
                WHERE cihf_sub.id_item = ci.id_item AND cihf_sub.tipo_homologacao = 'Fiscal' AND cihf_sub.homologado = 1) as homolog_fiscal_data,
                (SELECT MAX(cihe_sub.criado_em)
                FROM cad_item_homologacao cihe_sub
                WHERE cihe_sub.id_item = ci.id_item AND cihe_sub.tipo_homologacao = 'Engenharia' AND cihe_sub.homologado = 1) as homolog_engenharia_data,
                i.estabelecimento,
                i.wf_status_atributos as status_atributos,
                i.wf_status_integracao as status_integracao,
                i.status_attr as status_preenchimento,
                ep.nome as empresa_prioridade,
                i.maquina,
                i.origem,
                i.evento");
        }

        if ($data_ini = $this->get_state('filter.data_ini')) {
            $this->db->where('date(i.dat_criacao) >=', $data_ini);
        }

        if ($data_fim = $this->get_state('filter.data_fim')) {
            $this->db->where('date(i.dat_criacao) <=', $data_fim);
        }

        if ($evento = $this->get_state("filter.evento")) {
            $evento = (array)$evento;

            // Separar 'sem_evento' dos outros eventos
            $semEvento = in_array("sem_evento", $evento);
            $outrosEventos = array_diff($evento, ["sem_evento"]);

            $whereClause = [];

            // Adicionar condição para outros eventos, se existirem
            if (!empty($outrosEventos)) {
                $eventoConcatenado = "'" . implode("','", $outrosEventos) . "'";
                $whereClause[] = "i.evento IN ({$eventoConcatenado})";
            }

            // Adicionar condição para 'sem_evento', se presente
            if ($semEvento) {
                $whereClause[] = "(i.evento = '' OR i.evento IS NULL)";
            }

            // Combinar as condições com OR, se houver mais de uma
            if (!empty($whereClause)) {
                $finalWhereClause = '(' . implode(' OR ', $whereClause) . ')';
                $this->db->where($finalWhereClause, null, false);
            }
        }

        if ($owner = $this->get_state('filter.owner')) {
            $this->db->where_in("i.cod_owner", $owner);
        }

        if ($hasPrioridade) {
            if ($prioridade = $this->get_state('filter.prioridade')) {
                if (!empty($prioridade)) {
                    $this->db->where_in('i.id_prioridade', $prioridade);
                }
            }
        }

        $where_in = "";
        if ($search = $this->get_state('filter.search')) {
            $keywords = array_filter(preg_split('/\r\n|[\r\n]/', $search));

            if (count($keywords) > 1) {
                $adwhereplus = "";
                foreach ($keywords as $keyword) {
                    if ($keyword !== '') {
                        $keyword = str_replace("*", "%",  $keyword);
                        if ($hasPnPrimarioSecundario) {
                            $adwhereplus .= ' i.part_number LIKE "' . trim($keyword) . '" OR i.pn_primario_mpn LIKE "' . trim($keyword) . '" OR i.pn_secundario_ipn LIKE "' . trim($keyword) . '" OR ';
                        } else {
                            $adwhereplus .= ' i.part_number LIKE "' . trim($keyword) . '" OR ';
                        }
                    }
                }

                $adwhereplus = rtrim($adwhereplus, 'OR ');

                $where_in .= ' (' . $adwhereplus . ')';

                $this->db->where($where_in, NULL, FALSE);
            } else if (strpos($search, "\n") !== false) {

                $search = str_replace(array("\t", "\r\n", "\s", "\n"), '#', $search);

                $keywords = explode('#', $search);
                $adwhereplus = "";
                $adwhere_go = "";
                foreach ($keywords as $keyword) {

                    $keyword = str_replace("*", "%", $keyword);

                    $keyword = rtrim($keyword, " ");
                    if ($keyword !== '') {
                        if ($hasPnPrimarioSecundario) {
                            $adwhereplus .= ' i.part_number LIKE "' . trim($keyword) . '" OR i.pn_primario_mpn LIKE "' . trim($keyword) . '" OR i.pn_secundario_ipn LIKE "' . trim($keyword) . '" OR ';
                        } else {
                            $adwhereplus .= ' i.part_number LIKE "' . trim($keyword) . '" OR ';
                        }
                    }
                }
                $adwhere_go = rtrim($adwhere_go, 'OR ');

                $where_in .= ' (' . $adwhere_go . ')';
            } else {
                if ($where_in == '') {
                    if (strpos($search, '*') !== false) {


                        if (is_array($search)) {
                            $search = $search[0];
                        }


                        if (substr_count($search, '*') == 1) {

                            if (substr($search, 0, 1) === '*') {
                                $search = str_replace("*", "",  $search);
                                if ($hasPnPrimarioSecundario) {
                                    $where_in  .= ' (i.descricao LIKE "%' . $search . '" OR i.part_number LIKE "%' . $search . '" OR i.pn_primario_mpn LIKE "%' . $search . '" OR i.pn_secundario_ipn LIKE "%' . $search . '")';
                                } else {
                                    $where_in  .= ' (i.descricao LIKE "%' . $search . '" OR i.part_number LIKE "%' . $search . '")';
                                }
                            } else if (substr($search, -1) === '*') {
                                $search = str_replace("*", "",  $search);
                                if ($hasPnPrimarioSecundario) {
                                    $where_in  .= ' (i.descricao LIKE "' . $search . '%" OR i.part_number LIKE "' . $search . '%" OR i.pn_primario_mpn LIKE "' . $search . '%" OR i.pn_secundario_ipn LIKE "' . $search . '%")';
                                } else {
                                    $where_in  .= ' (i.descricao LIKE "' . $search . '%" OR i.part_number LIKE "' . $search . '%")';
                                }
                            } else {

                                $search = str_replace("*", "%", $search);

                                if ($hasPnPrimarioSecundario) {
                                    $where_in  .= ' (i.descricao LIKE "' . $search . '" OR i.part_number LIKE "' . $search . '" OR i.pn_primario_mpn LIKE "' . $search . '" OR i.pn_secundario_ipn LIKE "' . $search . '")';
                                } else {
                                    $where_in  .= ' (i.descricao LIKE "' . $search . '" OR i.part_number LIKE "' . $search . '")';
                                }
                            }
                        }

                        if (substr($search, 0, 1) === '*' && substr($search, -1) === '*' && substr_count($search, '*') <= 2) {
                            $search = str_replace("*", "",  $search);
                            if ($hasPnPrimarioSecundario) {
                                $where_in  .= ' (i.descricao LIKE "%' . $search . '%" OR i.part_number LIKE "%' . $search . '%" OR i.pn_primario_mpn LIKE "%' . $search . '%" OR i.pn_secundario_ipn LIKE "%' . $search . '%" )';
                            } else {
                                $where_in  .= ' (i.descricao LIKE "%' . $search . '%" OR i.part_number LIKE "%' . $search . '%")';
                            }
                        } else {
                            if (substr_count($search, '*') >= 2) {

                                $adwhereplus = '';

                                $search = str_replace("*", "%", $search);

                                if ($search !== '') {

                                    if ($hasPnPrimarioSecundario) {
                                        $adwhereplus .= 'i.descricao LIKE "' . $search . '" OR i.part_number LIKE "' . $search . '" OR i.pn_primario_mpn LIKE "' . $search . '" OR i.pn_secundario_ipn LIKE "' . $search . '" OR ';
                                    } else {
                                        $adwhereplus .= 'i.descricao LIKE "' . $search . '" OR i.part_number LIKE "' . $search . '" OR ';
                                    }
                                }

                                $adwhereplus = rtrim($adwhereplus, 'OR ');

                                $where_in .= ' (' . $adwhereplus . ')';
                            }
                        }

                        $this->db->where($where_in, NULL, FALSE);
                    } else {
                        //$this->db->like("i.part_number", trim($search));
                        if (!empty($search)) {
                            if (strpos($search, ',') !== false) {
                                $keywords = explode(',', $search);
                                $adwhere_go = "";
                                foreach ($keywords as $keyword) {
                                    if ($keyword !== '') {
                                        if ($hasPnPrimarioSecundario) {
                                            $adwhere_go .= ' i.part_number LIKE "%' . $keyword  . '%" OR i.pn_primario_mpn LIKE "%' . $keyword . '%" OR i.pn_secundario_ipn LIKE "%' . $keyword . '%" OR';
                                        } else {
                                            $adwhere_go .= ' i.part_number LIKE "%' . $keyword  . '%" OR ';
                                        }
                                    }
                                }
                                $adwhere_go = rtrim($adwhere_go, 'OR ');
                                $where_in .= ' (' . $adwhere_go . ')';

                                if ($where_in != ' ()') {
                                    $this->db->where($where_in, NULL, FALSE);
                                }
                            } else {
                                if ($hasPnPrimarioSecundario) {
                                    $this->db->where("(i.descricao LIKE '{$search}' OR i.part_number LIKE '{$search}' OR i.pn_primario_mpn LIKE '{$search}' OR i.pn_secundario_ipn LIKE '{$search}')", NULL, FALSE);
                                } else {
                                    $this->db->where("(i.descricao LIKE '{$search}' OR i.part_number LIKE '{$search}')", NULL, FALSE);
                                }
                            }
                        }
                    }
                }
            }
        }

        if ($novo_material_modal = $this->get_state('filter.novo_material_modal')) {
            $this->db->where("i.integracao_novo_material", $novo_material_modal);
        }

        if ($estabelecimento_modal = $this->get_state('filter.estabelecimento_modal')) {
            $this->db->where_in("i.estabelecimento", $estabelecimento_modal);
        }


        if ($ncm_proposta_modal = $this->get_state('filter.ncm_proposta_modal')) {
            $this->db->where_in("ci.ncm_proposto", $ncm_proposta_modal);
        }
        if ($ex_ipi_modal = $this->get_state('filter.ex_ipi_modal')) {
            $ex_ipi_modal = $this->getFirstPart($ex_ipi_modal);
            $this->db->where_in("ci.num_ex_ipi", $ex_ipi_modal);
        }
        if ($ex_ii_modal = $this->get_state('filter.ex_ii_modal')) {
            $ex_ii_modal = $this->getFirstPart($ex_ii_modal);
            $this->db->where_in("ci.num_ex_ii", $ex_ii_modal);
        }

        if ($sistema_origem_modal = $this->get_state('filter.sistema_origem_modal')) {

            $this->db->where_in("i.sistema_origem", $sistema_origem_modal);
        }

        if ($descricao_completa_modal = $this->get_state('filter.descricao_completa_modal')) {

            $search = $descricao_completa_modal;

            if (strpos($search, '*') !== false) {

                if (is_array($search)) {
                    $search = $search[0];
                }

                if (substr_count($search, '*') == 1) {

                    if (substr($search, 0, 1) === '*') {
                        $search = str_replace("*", "",  $search);

                        $where_in  = ' i.descricao_proposta_completa LIKE "%' . $search . '"';
                    } else if (substr($search, -1) === '*') {
                        $search = str_replace("*", "",  $search);

                        $where_in  = ' i.descricao_proposta_completa LIKE "' . $search . '%"';
                    } else {
                        $search = str_replace("*", "%",  $search);
                        $where_in  = ' i.descricao_proposta_completa LIKE "' . $search . '"';
                    }
                }

                if (substr($search, 0, 1) === '*' && substr($search, -1) === '*' && substr_count($search, '*') <= 2) {
                    $search = str_replace("*", "",  $search);

                    $where_in  = ' i.descricao_proposta_completa LIKE "%' . $search . '%"';
                } else {
                    if (substr_count($search, '*') >= 2) {

                        $adwhereplus = '';

                        $search = str_replace("*", "%", $search);

                        if ($search !== '') {

                            $adwhereplus .= 'i.descricao_proposta_completa LIKE "' . $search . '" OR';
                        }

                        $adwhereplus = rtrim($adwhereplus, 'OR ');

                        $where_in .= ' (' . $adwhereplus . ')';
                    }
                }

                $this->db->where($where_in, NULL, FALSE);
            } else {
                if (!empty($search)) {
                    $this->db->where("i.descricao_proposta_completa LIKE '{$search}' ", NULL, FALSE);
                }
            }
        }
        if ($descricao_global_modal = $this->get_state('filter.descricao_global_modal')) {

            $search = $descricao_global_modal;

            if (strpos($search, '*') !== false) {

                if (is_array($search)) {
                    $search = $search[0];
                }

                if (substr_count($search, '*') == 1) {

                    if (substr($search, 0, 1) === '*') {
                        $search = str_replace("*", "",  $search);

                        $where_in  = ' i.descricao_global LIKE "%' . $search . '"';
                    } else if (substr($search, -1) === '*') {
                        $search = str_replace("*", "",  $search);

                        $where_in  = ' i.descricao_global LIKE "' . $search . '%"';
                    } else {
                        $search = str_replace("*", "%",  $search);
                        $where_in  = ' i.descricao_global LIKE "' . $search . '"';
                    }
                }

                if (substr($search, 0, 1) === '*' && substr($search, -1) === '*' && substr_count($search, '*') <= 2) {
                    $search = str_replace("*", "",  $search);

                    $where_in  = ' i.descricao_global LIKE "%' . $search . '%"';
                } else {
                    if (substr_count($search, '*') >= 2) {

                        $adwhereplus = '';

                        $search = str_replace("*", "%", $search);

                        if ($search !== '') {

                            $adwhereplus .= 'i.descricao_global LIKE "' . $search . '" OR';
                        }

                        $adwhereplus = rtrim($adwhereplus, 'OR ');

                        $where_in .= ' (' . $adwhereplus . ')';
                    }
                }

                $this->db->where($where_in, NULL, FALSE);
            } else {
                if (!empty($search)) {
                    $this->db->where(" i.descricao_global LIKE '{$search}' ", NULL, FALSE);
                }
            }
        }

        if ($data_inicio_modificacao_modal = $this->get_state('filter.data_inicio_modificacao_modal')) {
            $this->db->where('date(i.data_modificacao) >=', $data_inicio_modificacao_modal);
        }

        if ($data_fim_modificacao_modal = $this->get_state('filter.data_fim_modificacao_modal')) {
            $this->db->where('date(i.data_modificacao) <=', $data_fim_modificacao_modal);
        }

        if ($data_inicio_homologacao_modal = $this->get_state('filter.data_inicio_homologacao_modal')) {
            $this->db->where('date(cih.criado_em) >=', $data_inicio_homologacao_modal);
        }

        if ($data_fim_homologacao_modal = $this->get_state('filter.data_fim_homologacao_modal')) {
            $this->db->where('date(cih.criado_em) <=', $data_fim_homologacao_modal);
        }


        if ($status = $this->get_state('filter.status')) {
            $this->db->where_in('s.slug', $status);
        }

        if ($statusExportacao = $this->get_state('filter.statusExportacao')) {
            $this->db->where('ci.status_exportacao', $statusExportacao == 'pendente' ? 0 : 1);
        }

        $this->db->join('status s', 's.id = i.id_status', 'inner');
        $this->db->join('cad_item ci', 'ci.part_number=i.part_number AND ci.id_empresa=i.id_empresa AND ci.estabelecimento = i.estabelecimento', 'left');
        $this->db->join('cad_item_homologacao cih', 'cih.id_item=ci.id_item', 'left');
        $this->db->join('usuario u', 'cih.id_usuario=u.id_usuario', 'left');
        $this->db->join('owner o', "i.cod_owner = o.codigo AND (o.cnpj_raiz = '{$cnpj_raiz_logado}' OR o.cnpj_raiz IS NULL) ", 'left');
        $this->db->join('owner_usuario ou', 'o.id_owner = ou.id_owner AND ou.responsavel_gestor = 1', 'left');
        $this->db->join('usuario rg', 'ou.id_usuario = rg.id_usuario', 'left');
        $this->db->join('empresa e', 'i.id_empresa = e.id_empresa', 'left');
        $this->db->join('empresa_prioridades ep', 'ep.id_prioridade = i.id_prioridade', 'left');
        $this->db->join('grupo_tarifario gt', 'gt.id_grupo_tarifario = ci.id_grupo_tarifario ', 'left');
        $this->db->join('comex', 'comex.part_number_original = i.part_number AND comex.id_empresa = i.id_empresa AND comex.unidade_negocio = i.estabelecimento', 'left');

        $this->db->group_by('i.part_number');
        $this->db->order_by('i.part_number ASC');

        return $this->db->get($this->_table . ' i');
        // return $query->result();
    }

    private function apply_default_filter()
    {
        if ($this->get_state('filter.id_empresa')) {
            $this->db->where('i.id_empresa', $this->get_state('filter.id_empresa'));
        }

        if ($estabelecimento = $this->get_state('filter.estabelecimento')) {
            $this->db->where('i.estabelecimento', $estabelecimento);
        }

        if ($data_ini = $this->get_state('filter.data_ini')) {
            $this->db->where('date(i.dat_criacao) >=', $data_ini);
        }

        if ($data_fim = $this->get_state('filter.data_fim')) {
            $this->db->where('date(i.dat_criacao) <=', $data_fim);
        }

        if ($pn = $this->get_state('filter.exclude_part_number')) {
            $this->db->where_not_in('i.part_number', $pn);
        }

        if ($search = $this->get_state('filter.search')) {
            if ($this->get_state('filter.ignore_like')) {

                $separator = $this->separator;

                $listPn = explode($separator, $search);

                if (!empty($listPn) && count($listPn) > 1) {
                    $this->db->where_in("i.part_number", $listPn);
                } else {
                    $listPn = array_filter(preg_split('/\r\n|[\r\n]/', $search));

                    if (count($listPn) > 1) {
                        $this->db->where_in("i.part_number", $listPn);
                    } else {
                        $this->db->like("i.part_number", trim($search));
                    }
                }
            } else {
                $this->db->like("i.part_number", trim($search));
            }
        }

        if ($evento = $this->get_state('filter.evento')) {
            if (!is_array($evento)) {
                $evento = array($evento);
            }

            $this->db->where_in('i.evento', $evento);
        }

        if ($statusExportacao = $this->get_state('filter.statusExportacao')) {
            $this->db->where('ci.status_exportacao', $statusExportacao == 'pendente' ? 0 : 1);
        }

        if (!empty($this->get_state("filter.search") && !$this->get_state('filter.ignore_like'))) {
            $search = $this->get_state("filter.search");
            if (!empty($search)) {
                $this->db->where("(i.part_number LIKE '%{$search}%'", NULL, FALSE);
                $this->db->or_where("i.part_number_similar LIKE '%{$search}%'", NULL, FALSE);
                $this->db->or_where("i.ncm LIKE '%{$search}%'", NULL, FALSE);
                $this->db->or_where("i.estabelecimento LIKE '%{$search}%'", NULL, FALSE);
                $this->db->or_where("i.descricao LIKE '%{$search}%'", NULL, FALSE);
                $this->db->or_where("e.nome_fantasia LIKE '%{$search}%'", NULL, FALSE);
                $this->db->or_where("e.cnpj LIKE '%{$search}%')", NULL, FALSE);
            }
        }

        if ($this->get_state('filter.part_number')) {
            $this->db->like('i.pn_primario_mpn', $this->get_state('filter.part_number'));
        }
    }

    public function get_entries($limit = NULL, $offset = NULL, $atribuidos = FALSE, $total = NULL, $owner_user = NULL, $part_numbers_created_for_user = NULL)
    {
        $id_empresa = sess_user_company();
        $this->load->model('empresa_model');
        $empresa = $this->empresa_model->get_entry(sess_user_company());
        $data['campos_adicionais'] = explode("|", $empresa->campos_adicionais);
        $data['funcoes_adicionais'] = explode("|", $empresa->funcoes_adicionais);
        $hasOwner =  in_array('owner', $data['campos_adicionais']);
        $hasPrioridade =  in_array('prioridade', $data['campos_adicionais']);
        $hasPnPrimarioSecundario = in_array('pn_primario_secundario', $data['campos_adicionais']);

        if ($hasOwner) {
            $cnpj_raiz_logado = $this->empresa_model->get_entry_cnpj_raiz($id_empresa);
        }

        $this->separator = get_company_separator(sess_user_company());

        if ($hasOwner) {
            $this->db->select('item.part_number, item.id_empresa, item.estabelecimento, i.status_exportacao, item.prioridade, item.part_number_similar, item.descricao, item.descricao_global, item.ncm, item.evento, s.status as status_formatado , item.data_envio, item.item_ja_homologado, item.cod_owner, o.codigo as owner_codigo, o.descricao as owner_descricao, item.pn_primario_mpn,item.pn_secundario_ipn, ep.nome as empresa_prioridade,item.dat_criacao,item.data_modificacao,item.integracao_novo_material, item.usuario_bloqueador, b.nome as nome_usuario_bloqueador,item.sistema_origem,
                item.status_triagem_diana,
                item.id_status,
                wf_status.status as wf_status_atributos,
                wf_status.color as wf_color,
                wf_status.id as wf_id,
                GROUP_CONCAT(DISTINCT rg.nome) as responsaveis_gestores_nomes,
                GROUP_CONCAT(DISTINCT rg.email) as responsaveis_gestores_emails');
        } else {
            $this->db->select('item.part_number, 
                wf_status.status as wf_status_atributos,
                wf_status.color as wf_color,
                wf_status.id as wf_id,
                item.id_empresa, item.estabelecimento, i.status_exportacao, item.prioridade, item.part_number_similar, item.descricao, item.descricao_global, item.ncm, item.evento, s.status as status_formatado , item.data_envio, item.item_ja_homologado, item.cod_owner, item.pn_primario_mpn, item.pn_secundario_ipn, ep.nome as empresa_prioridade,item.dat_criacao,item.data_modificacao, item.usuario_bloqueador,item.integracao_novo_material, b.nome as nome_usuario_bloqueador,item.sistema_origem,
                item.status_triagem_diana, item.id_status');
        }

        $this->db->join('cad_item i', 'i.part_number = item.part_number AND i.id_empresa = item.id_empresa AND i.estabelecimento = item.estabelecimento', 'left');
        // Join com cad_item_homologacao para buscar a maior criado_em que será a data de homologação e pode ser usado no filtro
        $this->db->join('cad_item_homologacao cih', 'cih.id_item = i.id_item', 'left');
        $this->db->join('status s', 's.id = item.id_status', 'inner');
        $this->db->join('empresa_prioridades ep', 'ep.id_prioridade = item.id_prioridade', 'left');
        if ($this->db->table_exists('comex')) {
            $this->db->join('comex', 'comex.part_number_original = i.part_number AND comex.id_empresa = i.id_empresa AND comex.unidade_negocio = i.estabelecimento', 'left');
        }
        $this->db->join('usuario b', 'b.id_usuario = item.usuario_bloqueador', 'left');
        $this->db->join('status_wf_atributos wf_status', 'wf_status.id = item.wf_status_atributos', 'left');

        if ($hasOwner) {
            // JOIN TABELA OWNER
            $this->db->join('owner o', "item.cod_owner = o.codigo AND (o.cnpj_raiz = '{$cnpj_raiz_logado}' OR o.cnpj_raiz IS NULL) ", 'left');
            // Faça JOIN com as tabelas owner_usuario e usuario para buscar os responsáveis gestores
            $this->db->join('owner_usuario ou', 'o.id_owner = ou.id_owner AND ou.responsavel_gestor = 1', 'left');
            $this->db->join('usuario rg', 'ou.id_usuario = rg.id_usuario', 'left');
        }

        if ($status = $this->get_state('filter.status')) {
            $this->db->where_in('s.slug', $status);
        }

        if ($statusExportacao = $this->get_state('filter.statusExportacao')) {
            $statusExportacao = $statusExportacao == 'pendente' ? 0 : 1;
            $this->db->where('i.status_exportacao', $statusExportacao);
        }

        $this->db->where('item.id_empresa', $id_empresa);

        if ($evento = $this->get_state("filter.evento")) {
            $evento = (array)$evento;

            // Separar 'sem_evento' dos outros eventos
            $semEvento = in_array("sem_evento", $evento);
            $outrosEventos = array_diff($evento, ["sem_evento"]);

            $whereClause = [];

            // Adicionar condição para outros eventos, se existirem
            if (!empty($outrosEventos)) {
                $eventoConcatenado = "'" . implode("','", $outrosEventos) . "'";
                $whereClause[] = "item.evento IN ({$eventoConcatenado})";
            }

            // Adicionar condição para 'sem_evento', se presente
            if ($semEvento) {
                $whereClause[] = "(item.evento = '' OR item.evento IS NULL)";
            }

            // Combinar as condições com OR, se houver mais de uma
            if (!empty($whereClause)) {
                $finalWhereClause = '(' . implode(' OR ', $whereClause) . ')';
                $this->db->where($finalWhereClause, null, false);
            }
        }

        if ($data_ini = $this->get_state('filter.data_ini')) {
            $this->db->where('date(item.dat_criacao) >=', $data_ini);
        }

        if ($data_fim = $this->get_state('filter.data_fim')) {
            $this->db->where('date(item.dat_criacao) <=', $data_fim);
        }

        if ($part_numbers = $this->get_state('filter.part_numbers')) {
            $or_flag = TRUE;
        } else {
            $or_flag = FALSE;
        }

        if ($hasOwner) {
            $owner = $this->get_state('filter.owner');
            if ($owner) {
                $this->db->where_in('item.cod_owner', $owner);
            }
        }

        if ($part_numbers_created_for_user) {
            $this->db->where_in('item.part_number', $part_numbers_created_for_user);
        }

        if ($hasOwner) {
            if ($owner_user) {
                $this->db->or_where('item.cod_owner', $owner_user);
            }
        }

        if ($hasPrioridade) {
            $prioridades = $this->get_state('filter.prioridade');
            $this->db->where_in('item.id_prioridade', $prioridades);
        }

        $like = "";

        $where_in = "";

        if ($search = $this->get_state('filter.search')) {

            $keywords = array_filter(preg_split('/\r\n|[\r\n]/', $search));

            if (count($keywords) > 1) {

                $adwhereplus = "";
                foreach ($keywords as $keyword) {
                    if ($keyword !== '') {
                        $keyword = str_replace("*", "%",  $keyword);
                        if ($hasPnPrimarioSecundario) {
                            $adwhereplus .= ' item.part_number LIKE "' . trim($keyword) . '" OR item.pn_primario_mpn LIKE "' . trim($keyword) . '" OR item.pn_secundario_ipn LIKE "' . trim($keyword) . '" OR ';
                        } else {
                            $adwhereplus .= ' item.part_number LIKE "' . trim($keyword) . '" OR ';
                        }
                    }
                }

                $adwhereplus = rtrim($adwhereplus, 'OR ');

                $where_in .= ' (' . $adwhereplus . ')';

                $this->db->where($where_in, NULL, FALSE);
            } else if (strpos($search, "\n") !== false) {

                $search = str_replace(array("\t", "\r\n", "\s", "\n"), '#', $search);

                $keywords = explode('#', $search);
                $adwhereplus = "";
                $adwhere_go = "";

                foreach ($keywords as $keyword) {

                    $keyword = str_replace("*", "%", $keyword);

                    $keyword = rtrim($keyword, " ");

                    if ($keyword !== '') {
                        if ($hasPnPrimarioSecundario) {
                            $adwhereplus .= ' item.part_number LIKE "' . trim($keyword) . '" OR item.pn_primario_mpn LIKE "' . trim($keyword) . '" OR item.pn_secundario_ipn LIKE "' . trim($keyword) . '" OR ';
                        } else {
                            $adwhereplus .= ' item.part_number LIKE "' . trim($keyword) . '" OR ';
                        }
                    }
                }
                $adwhere_go = rtrim($adwhereplus, 'OR ');

                $where_in .= ' (' . $adwhere_go . ')';
                if ($where_in != ' ()') {
                    $this->db->where($where_in, NULL, FALSE);
                }
            } else {
                if ($where_in == '') {
                    if (strpos($search, '*') !== false) {


                        if (is_array($search)) {
                            $search = $search[0];
                        }


                        if (substr_count($search, '*') == 1) {

                            if (substr($search, 0, 1) === '*') {
                                $search = str_replace("*", "",  $search);
                                if ($hasPnPrimarioSecundario) {
                                    $where_in  .= ' (item.descricao LIKE "%' . $search . '" OR item.part_number LIKE "%' . $search . '" OR item.pn_primario_mpn LIKE "%' . $search . '" OR item.pn_secundario_ipn LIKE "%' . $search . '")';
                                } else {
                                    $where_in  .= ' (item.descricao LIKE "%' . $search . '" OR item.part_number LIKE "%' . $search . '")';
                                }
                            } else if (substr($search, -1) === '*') {
                                $search = str_replace("*", "",  $search);
                                if ($hasPnPrimarioSecundario) {
                                    $where_in  .= ' (item.descricao LIKE "' . $search . '%" OR item.part_number LIKE "' . $search . '%" OR item.pn_primario_mpn LIKE "' . $search . '%" OR item.pn_secundario_ipn LIKE "' . $search . '%")';
                                } else {
                                    $where_in  .= ' (item.descricao LIKE "' . $search . '%" OR item.part_number LIKE "' . $search . '%")';
                                }
                            } else {
                                $search = str_replace("*", "%",  $search);

                                if ($hasPnPrimarioSecundario) {
                                    $where_in  .= ' (item.descricao LIKE "' . $search . '" OR item.part_number LIKE "' . $search . '" OR item.pn_primario_mpn LIKE "' . $search . '" OR item.pn_secundario_ipn LIKE "' . $search . '" )';
                                } else {
                                    $where_in  .= ' (item.descricao LIKE "' . $search . '" OR item.part_number LIKE "' . $search . '")';
                                }
                            }
                        }

                        if (substr($search, 0, 1) === '*' && substr($search, -1) === '*' && substr_count($search, '*') <= 2) {
                            $search = str_replace("*", "",  $search);
                            if ($hasPnPrimarioSecundario) {
                                $where_in  .= ' (item.descricao LIKE "%' . $search . '%" OR item.part_number LIKE "%' . $search . '%" OR item.pn_primario_mpn LIKE "%' . $search . '%" OR item.pn_secundario_ipn LIKE "%' . $search . '%" )';
                            } else {
                                $where_in  .= ' (item.descricao LIKE "%' . $search . '%" OR item.part_number LIKE "%' . $search . '%")';
                            }
                        } else {
                            if (substr_count($search, '*') >= 2) {

                                $adwhereplus = '';

                                $search = str_replace("*", "%", $search);

                                if ($search !== '') {

                                    if ($hasPnPrimarioSecundario) {
                                        $adwhereplus .= 'item.descricao LIKE "' . $search . '" OR item.part_number LIKE "' . $search . '" OR item.pn_primario_mpn LIKE "' . $search . '" OR item.pn_secundario_ipn LIKE "' . $search . '" OR ';
                                    } else {
                                        $adwhereplus .= 'item.descricao LIKE "' . $search . '" OR item.part_number LIKE "' . $search . '" OR ';
                                    }
                                }

                                $adwhereplus = rtrim($adwhereplus, 'OR ');

                                $where_in .= ' (' . $adwhereplus . ')';
                            }
                        }

                        $this->db->where($where_in, NULL, FALSE);
                    } else {

                        if (!empty($search)) {
                            if (strpos($search, ',') !== false) {
                                $keywords = explode(',', $search);
                                $adwhere_go = "";
                                foreach ($keywords as $keyword) {
                                    if ($keyword !== '') {
                                        if ($hasPnPrimarioSecundario) {
                                            $adwhere_go .= ' item.part_number LIKE "%' . $keyword  . '%" OR item.pn_primario_mpn LIKE "%' . $keyword . '%" OR item.pn_secundario_ipn LIKE "%' . $keyword . '%" OR';
                                        } else {
                                            $adwhere_go .= ' item.part_number LIKE "%' . $keyword  . '%" OR ';
                                        }
                                    }
                                }
                                $adwhere_go = rtrim($adwhere_go, 'OR ');
                                $where_in .= ' (' . $adwhere_go . ')';

                                if ($where_in != ' ()') {
                                    $this->db->where($where_in, NULL, FALSE);
                                }
                            } else {
                                if ($hasPnPrimarioSecundario) {
                                    $this->db->where("(item.descricao LIKE '{$search}' OR item.part_number LIKE '{$search}' OR item.pn_primario_mpn LIKE '{$search}' OR item.pn_secundario_ipn LIKE '{$search}')", NULL, FALSE);
                                } else {
                                    $this->db->where("(item.descricao LIKE '{$search}' OR item.part_number LIKE '{$search}')", NULL, FALSE);
                                }
                            }
                        }
                    }
                }
            }
        }

        if ($novo_material_modal = $this->get_state('filter.novo_material_modal')) {
            $this->db->where("item.integracao_novo_material", $novo_material_modal);
        }

        if ($estabelecimento_modal = $this->get_state('filter.estabelecimento_modal')) {
            $this->db->where_in("item.estabelecimento", $estabelecimento_modal);
        }

        if ($ncm_proposta_modal = $this->get_state('filter.ncm_proposta_modal')) {
            $this->db->where_in("i.ncm_proposto ", $ncm_proposta_modal);
        }

        if ($ex_ipi_modal = $this->get_state('filter.ex_ipi_modal')) {
            $ex_ipi_modal = $this->getFirstPart($ex_ipi_modal);
            $this->db->where_in("i.num_ex_ipi", $ex_ipi_modal);
        }
        if ($ex_ii_modal = $this->get_state('filter.ex_ii_modal')) {
            $ex_ii_modal = $this->getFirstPart($ex_ii_modal);
            $this->db->where_in("i.num_ex_ii", $ex_ii_modal);
        }
        if ($sistema_origem_modal = $this->get_state('filter.sistema_origem_modal')) {
            $this->db->where_in("item.sistema_origem", $sistema_origem_modal);
        }

        $triagem_diana_falha = $this->get_state("filter.triagem_diana_falha");
        if (!empty($triagem_diana_falha)) {
            $this->db->where("item.status_triagem_diana", 'Falha na triagem');
        }

        if ($this->db->table_exists('comex') && $novo_importado = $this->get_state('filter.novo_importado')) {
            if ($novo_importado == 'S') {
                $this->db->where("comex.ind_ecomex = 'EI'");
            } else if ($novo_importado == 'N') {
                $this->db->where("(comex.ind_ecomex <> 'EI' OR comex.ind_ecomex IS NULL )");
            }
        }

        if ($status_atributos_selected = $this->get_state('filter.novo_status_atributos')) {

            if (!in_array(1, $status_atributos_selected)) {
                $this->db->where_in("item.wf_status_atributos", $status_atributos_selected);
            } else if (in_array(1, $status_atributos_selected)) {
                $base_condition = "(item.wf_status_atributos = '1' OR item.wf_status_atributos IS NULL)";

                $where_clause = '';

                if (count($status_atributos_selected) > 1) {
                    $escaped_values = array();
                    foreach ($status_atributos_selected as $value) {
                        $escaped_values[] = $this->db->escape($value);
                    }
                    $in_list = implode(',', $escaped_values);

                    $where_clause = "( " . $base_condition . " OR item.wf_status_atributos IN (" . $in_list . ") )";
                } else {
                    $where_clause = $base_condition;
                }

                $this->db->where($where_clause, NULL, FALSE);
            }
        }

        if ($descricao_completa_modal = $this->get_state('filter.descricao_completa_modal')) {

            $search = $descricao_completa_modal;

            if (strpos($search, '*') !== false) {

                if (is_array($search)) {
                    $search = $search[0];
                }

                if (substr_count($search, '*') == 1) {

                    if (substr($search, 0, 1) === '*') {
                        $search = str_replace("*", "",  $search);

                        $where_in  = ' item.descricao_proposta_completa LIKE "%' . $search . '"';
                    } else if (substr($search, -1) === '*') {
                        $search = str_replace("*", "",  $search);

                        $where_in  = ' item.descricao_proposta_completa LIKE "' . $search . '%"';
                    } else {
                        $search = str_replace("*", "%",  $search);
                        $where_in  = ' item.descricao_proposta_completa LIKE "' . $search . '"';
                    }
                }

                if (substr($search, 0, 1) === '*' && substr($search, -1) === '*' && substr_count($search, '*') <= 2) {
                    $search = str_replace("*", "",  $search);

                    $where_in  = ' item.descricao_proposta_completa LIKE "%' . $search . '%"';
                } else {
                    if (substr_count($search, '*') >= 2) {

                        $adwhereplus = '';

                        $search = str_replace("*", "%", $search);

                        if ($search !== '') {

                            $adwhereplus .= 'item.descricao_proposta_completa LIKE "' . $search . '" OR';
                        }

                        $adwhereplus = rtrim($adwhereplus, 'OR ');

                        $where_in .= ' (' . $adwhereplus . ')';
                    }
                }

                $this->db->where($where_in, NULL, FALSE);
            } else {
                if (!empty($search)) {
                    $this->db->where("item.descricao_proposta_completa LIKE '{$search}' ", NULL, FALSE);
                }
            }
        }
        if ($descricao_global_modal = $this->get_state('filter.descricao_global_modal')) {

            $search = $descricao_global_modal;

            if (strpos($search, '*') !== false) {

                if (is_array($search)) {
                    $search = $search[0];
                }

                if (substr_count($search, '*') == 1) {

                    if (substr($search, 0, 1) === '*') {
                        $search = str_replace("*", "",  $search);

                        $where_in  = ' item.descricao_global LIKE "%' . $search . '"';
                    } else if (substr($search, -1) === '*') {
                        $search = str_replace("*", "",  $search);

                        $where_in  = ' item.descricao_global LIKE "' . $search . '%"';
                    } else {
                        $search = str_replace("*", "%",  $search);
                        $where_in  = ' item.descricao_global LIKE "' . $search . '"';
                    }
                }

                if (substr($search, 0, 1) === '*' && substr($search, -1) === '*' && substr_count($search, '*') <= 2) {
                    $search = str_replace("*", "",  $search);

                    $where_in  = ' item.descricao_global LIKE "%' . $search . '%"';
                } else {
                    if (substr_count($search, '*') >= 2) {

                        $adwhereplus = '';

                        $search = str_replace("*", "%", $search);

                        if ($search !== '') {

                            $adwhereplus .= 'item.descricao_global LIKE "' . $search . '" OR';
                        }

                        $adwhereplus = rtrim($adwhereplus, 'OR ');

                        $where_in .= ' (' . $adwhereplus . ')';
                    }
                }

                $this->db->where($where_in, NULL, FALSE);
            } else {
                if (!empty($search)) {
                    $this->db->where(" item.descricao_global LIKE '{$search}' ", NULL, FALSE);
                }
            }
        }

        if ($data_inicio_modificacao_modal = $this->get_state('filter.data_inicio_modificacao_modal')) {
            $this->db->where('date(item.data_modificacao) >=', $data_inicio_modificacao_modal);
        }

        if ($data_fim_modificacao_modal = $this->get_state('filter.data_fim_modificacao_modal')) {
            $this->db->where('date(item.data_modificacao) <=', $data_fim_modificacao_modal);
        }

        if ($data_inicio_homologacao_modal = $this->get_state('filter.data_inicio_homologacao_modal')) {
            $this->db->where('date(cih.criado_em) >=', $data_inicio_homologacao_modal);
        }

        if ($data_fim_homologacao_modal = $this->get_state('filter.data_fim_homologacao_modal')) {
            $this->db->where('date(cih.criado_em) <=', $data_fim_homologacao_modal);
        }

        $this->db->group_by('item.part_number, item.id_empresa, item.estabelecimento');

        $this->db->order_by('item.part_number ASC');

        $query = $this->db->get('item', $limit, $offset);

        if (!empty($total)) {
            return  $query->num_rows();
        }

        return $query->result();
    }

    private function getFirstPart($input)
    {
        if (is_array($input)) {
            $result = [];
            foreach ($input as $key => $value) {
                if (strpos($input[$key], "|") !== false) {
                    $parts = explode("|", $input[$key]);
                    $result[] = $parts[0];
                }
            }
            return $result;
        } else {
            if (strpos($input, "|") !== false) {
                $parts = explode("|", $input);
                return $parts[0];
            } else {
                return $input;
            }
        }
    }

    public function get_total_entries()
    {
        if ($this->get_state('filter.part_numbers')) {
            $this->db->where_in('i.part_number', $this->get_state('filter.part_numbers'));
        }

        $this->apply_default_filter();

        if (!empty($this->_status)) {
            $this->db->join('v_item_status vis', 'vis.id_empresa = i.id_empresa AND vis.estabelecimento = i.estabelecimento AND vis.part_number = i.part_number', 'inner');
            $this->db->where('vis.status', $this->_status);
        }

        $this->db->join('empresa e', 'i.id_empresa = e.id_empresa', 'inner');

        $this->db->join(
            'cad_item ci',
            'ci.part_number = i.part_number and ci.id_empresa = i.id_empresa and ci.estabelecimento = i.estabelecimento',
            'left'
        );

        $total = $this->db->count_all_results($this->_table . ' i');

        return $total;
    }

    public function check_item_status($part_number,  $estabelecimento, $id_empresa)
    {
        $this->db->select('s.slug,i.part_number');
        $this->db->join('cad_item cad', 'cad.id_empresa = i.id_empresa AND cad.estabelecimento = i.estabelecimento AND cad.part_number = i.part_number', 'left');
        $this->db->join('status s', 's.id = i.id_status', 'inner');
        $this->db->where("(i.part_number LIKE '%{$part_number}%')", null, false);
        $this->db->where('i.id_empresa', $id_empresa);
        if (!empty($estabelecimento)) {
            $this->db->where('i.estabelecimento', $estabelecimento);
        } else {
            $this->db->where("(i.estabelecimento IS NULL OR i.estabelecimento = '')", null, false);
        }

        $query = $this->db->get($this->_table . ' i');

        return $query->row();
    }

    public function get_entry_usuarios_seguidores($part_number, $id_empresa, $estabelecimento = NULL, $get_row = false)
    {
        $this->db->select('*');
        $this->db->where('part_number', $part_number);
        $this->db->where('id_empresa', $id_empresa);
        $this->db->where('estabelecimento', $estabelecimento);
        $query = $this->db->get('rel_usuarios_seguidores');

        return $query->result();
    }

    public function get_usuarios_seguidores($part_number, $id_empresa, $estabelecimento = NULL, $get_row = false)
    {
        $this->db->select('*');
        $this->db->where('part_number', $part_number);
        $this->db->where('id_empresa', $id_empresa);
        $this->db->where('estabelecimento', $estabelecimento);
        $query = $this->db->get('rel_usuarios_seguidores');

        return $query->result();
    }

    public function get_email_usuarios_seguidores($part_number, $id_empresa, $estabelecimento = NULL, $get_row = false)
    {
        $this->db->select('email_seguidor');
        $this->db->where('part_number', $part_number);
        $this->db->where('id_empresa', $id_empresa);
        $this->db->where('estabelecimento', $estabelecimento);
        $query = $this->db->get('rel_usuarios_seguidores');

        return $query->result();
    }

    public function get_owners($part_number, $id_empresa, $estabelecimento = NULL, $get_row = false)
    {
        $this->db->select('o.codigo, o.id_owner, o.descricao, ou.id_owner_usuario, u.nome, u.email, ou.responsavel_gestor');
        $this->db->from('item i');
        $this->db->join('owner o', 'i.cod_owner = o.codigo', 'inner');
        $this->db->join('owner_usuario ou', 'o.id_owner = ou.id_owner', 'left');
        $this->db->join('usuario u', 'ou.id_usuario = u.id_usuario', 'left');
        $this->db->where('cod_owner IS NOT NULL', null, false);
        $this->db->where('i.part_number', $part_number);
        $this->db->where('i.id_empresa', $id_empresa);
        $this->db->where('i.estabelecimento', $estabelecimento);

        $query = $this->db->get();

        if ($query->num_rows() > 0) {
            return $query->result();
        } else {
            return false;
        }
    }

    public function get_data_owner($owner, $get_row = false)
    {
        $this->db->select('o.codigo, o.id_owner, o.descricao');
        $this->db->from('owner o');
        $this->db->where('o.codigo', $owner);

        $query = $this->db->get();

        if ($query->num_rows() > 0) {
            return $query->result();
        } else {
            return false;
        }
    }

    public function get_entry($part_number, $id_empresa, $estabelecimento = NULL, $get_row = false)
    {
        if (empty($part_number)) {
            throw new Exception('Part number inexistente.');
        }

        $useViewComex = $this->config->item('use_view_comex');

        $this->db->select('i.*, vis.status,cad.id_resp_fiscal as responsavel_fiscal, cad.id_resp_engenharia as responsavel_engenharia, cad.id_grupo_tarifario, cad.ncm_proposto, b.nome as nome_usuario_bloqueador');
        $this->db->select('s.status as status_formatado');
        $this->db->select('cad.descricao_mercado_local as descricao_resumida_cad_item');
        // Uso da Base Comex
        if ($useViewComex && $this->db->table_exists('vw_comex')) {
            $this->db->select('vw_comex.*');
        } elseif (!$useViewComex && $this->db->table_exists('comex')) {
            $this->db->select('c.id_comex, c.id_base_comex, c.part_number_original,
            c.part_number_modificado, c.cnpj_raiz, c.unidade_negocio,
            c.num_di, c.data_di, c.ind_ecomex, c.ind_drawback,
            c.data_criacao as comex_data_criacao,
            c.data_ultima_atualizacao');
        }

        $this->db->where('i.part_number', $part_number);
        $this->db->where('i.id_empresa', $id_empresa);

        if (!empty($estabelecimento)) {
            $this->db->where('i.estabelecimento', $estabelecimento);
        }

        $this->db->join('status s', 's.id = i.id_status', 'inner');
        $this->db->join('v_item_status vis', 'vis.id_empresa = i.id_empresa AND vis.estabelecimento = i.estabelecimento AND vis.part_number = i.part_number', 'inner');
        $this->db->join('cad_item cad', 'cad.id_empresa = i.id_empresa AND cad.estabelecimento = i.estabelecimento AND cad.part_number = i.part_number', 'left');
        $this->db->join('usuario b', 'b.id_usuario = i.usuario_bloqueador', 'left');

        if ($useViewComex && $this->db->table_exists('vw_comex')) {
            $this->db->join('v_item_comex vw_comex', 'vw_comex.part_number_original = i.part_number AND vw_comex.id_empresa = i.id_empresa', 'left');
        } elseif (!$useViewComex && $this->db->table_exists('comex')) {
            $this->db->join('comex c', 'c.part_number_original = i.part_number AND c.id_empresa = i.id_empresa AND c.unidade_negocio = i.estabelecimento', 'left');
        }

        $query = $this->db->get($this->_table . ' i');

        if ($get_row) {
            return $query->row();
        }

        if ($query->num_rows() > 0) {
            return $query->row();
        }
    }

    // ESSA OPÇÃO É EXCLUSIVA PARA A SIMPLUS
    public function get_entry_by_pn_company($part_number, $id_empresa = NULL, $estabelecimento = NULL)
    {
        $this->db->where('i.part_number', $part_number);

        if (!empty($estabelecimento)) {
            $this->db->where('i.estabelecimento', $estabelecimento);
        }

        $query = $this->db->get('item_extra_simplus' . ' i');

        if ($query->num_rows() > 0) {
            return $query->row();
        }
    }

    public function check_item_exists($part_number = NULL, $id_empresa = NULL, $estabelecimento = NULL)
    {
        if (!empty($part_number)) {
            $this->db->where('part_number', $part_number);
        }

        if (!empty($id_empresa)) {
            $this->db->where('id_empresa', $id_empresa);
        }

        if (!empty($estabelecimento)) {
            $this->db->where('estabelecimento', $estabelecimento);
        }

        $query = $this->db->get($this->_table);

        return ($query->num_rows() > 0) ? TRUE : FALSE;
    }

    public function check_item_has_photo($part_number, $id_empresa, $estabelecimento = NULL)
    {
        $this->db->where('i.part_number', $part_number);
        $this->db->where('i.id_empresa', $id_empresa);

        if (!empty($estabelecimento)) {
            $this->db->where('i.estabelecimento', $estabelecimento);
        }

        $this->db->join('foto f', 'i.part_number=f.part_number AND i.id_empresa=f.id_empresa', 'inner');

        $query = $this->db->get($this->_table . ' i');

        return ($query->num_rows() > 0) ? TRUE : FALSE;
    }

    public function remove($itens, $id_empresa)
    {
        if (is_array($itens) && count($itens) > 0) {
            $this->load->model('foto_model');
            $this->load->model('item_log_model');

            foreach ($itens as $item) {
                $this->foto_model->remove_fotos($item['part_number'], $item['estabelecimento'], $id_empresa);

                $pn = addslashes($item['part_number']);
                $estab = addslashes($item['estabelecimento']);

                $id_item_log = generate_item_log('exclusao', array(
                    'part_number'     => $item['part_number'],
                    'estabelecimento' => $item['estabelecimento'],
                    'id_empresa'      => $id_empresa
                ));

                $this->db->where("( part_number = '{$pn}' and estabelecimento = '{$estab}' and id_empresa = '{$id_empresa}' )", null, false);
                $this->db->delete(array($this->_table, 'foto'));
            }

            return TRUE;
        }

        return FALSE;
    }

    /*
     * Verifica se um part_number_similar da tabela item, possui divergência no grupo tarifário
     */
    public function similar_has_diff($part_number, $part_number_similar, $id_empresa)
    {
        $this->db->select('id_grupo_tarifario');
        $this->db->where('part_number', $part_number);
        $this->db->where('id_empresa', $id_empresa);
        $query = $this->db->get('cad_item', 1);

        $this->db->select('id_grupo_tarifario');
        $this->db->where('part_number', $part_number_similar);
        $this->db->where('id_empresa', $id_empresa);
        $query_similar = $this->db->get('cad_item', 1);

        if ($query->num_rows() && $query_similar->num_rows()) {
            $row = $query->row();
            $row_similar = $query_similar->row();

            if ($row->id_grupo_tarifario <> $row_similar->id_grupo_tarifario) {
                return TRUE;
            }
        }

        return FALSE;
    }

    public function get_entries_by_pn_or_desc(
        $search = null,
        $tag = null,
        $order_by = null,
        $use_wildcards = false,
        $generic_part_numbers = null,
        $part_numbers = null,
        $owner_user = null,
        $part_numbers_created_for_user = null,
        $limit = null,
        $offset = null
    ) {
        $evento = $this->get_state("filter.evento");

        $empresa = $this->empresa_model->get_entry(sess_user_company());
        $campos_adicionais = explode("|", $empresa->campos_adicionais);
        $hasPnPrimarioSecundario = in_array('pn_primario_secundario', $campos_adicionais);

        $vlPrimarioSecundario = 'N';
        if ($hasPnPrimarioSecundario) {
            $vlPrimarioSecundario = 'S';
        }
        $selectPrimarioSecundario = "('$vlPrimarioSecundario') as hasPnPrimarioSecundario, ";

        $funcoes_adicionais = explode("|", $empresa->funcoes_adicionais);

        $id_empresa = sess_user_company();

        if ($id_usuario = $this->get_state('filter.atribuido_para')) {
            $field_responsavel = customer_has_role('engenheiro', $id_usuario) ?
                'id_resp_engenharia' : 'id_resp_fiscal';
        }

        $this->db->select('
                    i.*, s.slug, o.codigo as owner_codigo, o.descricao as owner_descricao, ep.nome as empresa_prioridade, b.nome as nome_usuario_bloqueador,
                    (
                        SELECT count(*) as total FROM ctr_pendencias_pergunta pp WHERE i.part_number = pp.part_number AND pp.id_empresa = i.id_empresa AND pp.estabelecimento = i.estabelecimento
                    ) as has_pergunta,
                    (
                        SELECT count(*) as total FROM ctr_pendencias_pergunta pp WHERE i.part_number = pp.part_number AND pp.id_empresa = i.id_empresa AND pp.estabelecimento = i.estabelecimento AND pp.pendente = 1
                    ) as has_pergunta_pendente,
                    ' . $selectPrimarioSecundario . '
                    GROUP_CONCAT(DISTINCT rg.nome SEPARATOR ", ") as responsaveis_gestores_nomes,
                    GROUP_CONCAT(DISTINCT rg.email SEPARATOR ", ") as responsaveis_gestores_emails', FALSE, FALSE);

        // JOIN com a tabela owner
        $this->db->join('owner o', 'i.cod_owner = o.codigo', 'left');

        // JOIN com as tabelas owner_usuario e usuario para buscar os responsáveis gestores
        $this->db->join('owner_usuario ou', 'o.id_owner = ou.id_owner AND ou.responsavel_gestor = 1', 'left');
        $this->db->join('usuario rg', 'ou.id_usuario = rg.id_usuario', 'left');
        $this->db->join('empresa_prioridades ep', 'ep.id_prioridade = i.id_prioridade', 'left');
        $this->db->join('usuario b', 'b.id_usuario = i.usuario_bloqueador', 'left');


        if (in_array("integracao_ecomex", $funcoes_adicionais)) {
            $this->db->select('comex.ind_ecomex');
        }
        $this->db->join('comex', 'comex.part_number_original = i.part_number AND comex.id_empresa = i.id_empresa AND comex.unidade_negocio = i.estabelecimento', 'left');

        $this->db->group_by('i.part_number, i.estabelecimento');

        $this->db->where('i.id_empresa', $id_empresa);

        if ($evento = $this->get_state("filter.evento")) {
            if (!is_array($evento)) {
                $evento = array($evento);
            }

            $newEvento = array_filter($evento, function ($item) {
                return $item != "sem_evento";
            });

            $eventoConcatenado = "";

            foreach ($newEvento as $item) {
                if (empty($eventoConcatenado)) {
                    $eventoConcatenado .= "'{$item}'";
                } else {
                    $eventoConcatenado .= ",'{$item}'";
                }
            }

            if (!empty($newEvento)) {
                if (in_array("sem_evento", $evento)) {
                    $this->db->where("(i.evento IN ({$eventoConcatenado}) OR (i.evento = '' OR i.evento IS NULL))", null, false);
                } else {
                    $this->db->where("(i.evento IN ({$eventoConcatenado}))", null, false);
                }
            } elseif (in_array("sem_evento", $evento)) {
                $this->db->where("(i.evento = '' OR i.evento IS NULL)", NULL, FALSE);
            }
        }

        $owner = NULL;
        if (in_array('owner', $campos_adicionais)) {
            $owner = $this->get_state('filter.owner');
        }

        if ($prioridade = $this->get_state('filter.prioridade')) {
            if (!empty($prioridade)) {
                $this->db->where_in('i.id_prioridade', $prioridade);
            }
        }

        if ($sistema_origem = $this->get_state('filter.sistema_origem')) {

            $this->db->where_in("i.sistema_origem", $sistema_origem);
        }

        $temCriadoPor = false;

        if ($part_numbers_created_for_user) {
            $temCriadoPor = true;
        }

        if ($owner && !empty($owner)) {
            if ($temCriadoPor) {
                $this->db->where(" (`i`.`criado_por` =  '" . sess_user_id() . "'
                    or `i`.`cod_owner` IN ('" . join("','", $owner) . "')) ", NULL, FALSE);
            } else {
                $this->db->where("i.cod_owner IN ('" . join("','", $owner) . "') ", NULL, FALSE);
            }
        } elseif ($owner_user && !empty($owner_user)) {
            if ($temCriadoPor) {
                $this->db->where(" (`i`.`criado_por` =  '" . sess_user_id() . "'
                    or `i`.`cod_owner` IN ('" . join("','", $owner_user) . "')) ", NULL, FALSE);
            } else {
                $this->db->where("i.cod_owner IN ('" . join("','", $owner_user) . "') ", NULL, FALSE);
            }
        } elseif ($temCriadoPor) {
            $this->db->where('i.criado_por', sess_user_id());
        }

        $this->db->join('status s', 's.id=i.id_status', 'inner');

        $filter_status_classificacao_fiscal = $this->get_state('filter.status_classificacao_fiscal');
        if (!empty($filter_status_classificacao_fiscal)) {
            $whereIn = array();

            if (in_array("aguardando_definicao_responsavel", $filter_status_classificacao_fiscal)) {
                $whereIn[] = "aguardando_definicao_responsavel";
            }

            if (in_array("pendente", $filter_status_classificacao_fiscal)) {
                $whereIn[] = "pendente_duvidas";
            }

            if (in_array("analise", $filter_status_classificacao_fiscal)) {
                $whereIn[] = "em_analise";
                $this->db->where('c.part_number IS NULL', NULL, NULL);
            }

            if (in_array("perguntasRespondidas", $filter_status_classificacao_fiscal)) {
                $whereIn[] = "respondido";
                $this->db->select('ctr.criado_em');
                // $this->db->where("(ctr.criado_em IS NOT NULL)",NULL,FALSE);
                $this->db->join('ctr_resposta ctr', 'i.part_number=ctr.part_number AND i.id_empresa=ctr.id_empresa AND i.estabelecimento = ctr.estabelecimento', 'left');
                $this->db->group_by('i.part_number, i.estabelecimento', 'owner_descricao', 'empresa_prioridade');
                $this->db->order_by('ctr.criado_em ASC');
            }

            if (in_array("revisar_informacoes_erp", $filter_status_classificacao_fiscal)) {
                $whereIn[] = "revisar_informacoes_erp";
            }

            if (in_array("informacoes_erp_revisadas", $filter_status_classificacao_fiscal)) {
                $whereIn[] = "informacoes_erp_revisadas";
            }

            if (in_array("revisar_informacoes_tecnicas", $filter_status_classificacao_fiscal)) {
                $whereIn[] = "revisar_informacoes_tecnicas";
            }

            if (in_array("pendente_hom_ambos", $filter_status_classificacao_fiscal)) {
                $whereIn[] = "homologar";
            }

            if (in_Array("aguardando_descricao", $filter_status_classificacao_fiscal)) {
                $whereIn[] = "aguardando_descricao";
            }

            if (in_Array("perguntas_respondidas_novas", $filter_status_classificacao_fiscal)) {
                $whereIn[] = "perguntas_respondidas_novas";
            }

            if (!empty($whereIn) && in_array("pendente_atribuicao", $filter_status_classificacao_fiscal)) {
                $this->db->join('v_item_status vis', 'vis.id_empresa = i.id_empresa AND vis.estabelecimento = i.estabelecimento AND vis.part_number = i.part_number ', 'left');
                $this->db->where_in("( s.slug", $whereIn);
                $this->db->or_where(" vis.status = 'pendente_atribuicao' )", NULL, FALSE);
            } else if (empty($whereIn) && in_array("pendente_atribuicao", $filter_status_classificacao_fiscal)) {
                $this->db->join('v_item_status vis', 'vis.id_empresa = i.id_empresa AND vis.estabelecimento = i.estabelecimento AND vis.part_number = i.part_number ', 'left');
                $this->db->where("vis.status = 'pendente_atribuicao'", NULL, FALSE);
            } else {
                $this->db->where_in("s.slug", $whereIn);
            }
        } else {
            $this->db->where('c.part_number IS NULL', NULL, NULL);
            $this->db->where("s.slug IN ('respondido', 'em_analise', 'pendente_duvidas', 'revisar_informacoes_erp', 'informacoes_erp_revisadas', 'revisar_informacoes_tecnicas', 'aguardando_definicao_responsavel', 'aguardando_descricao','perguntas_respondidas_novas')", NULL, FALSE);
        }

        // Filtro Status Triagem Diana
        $triagem_diana_falha = $this->get_state("filter.triagem_diana_falha");
        if ($triagem_diana_falha === '1') {
            $this->db->where("i.status_triagem_diana", 'Falha na triagem');
        }

        $estabelecimento = $this->get_state("filter.estabelecimento");
        if (!empty($estabelecimento)) {
            $this->db->where_in("i.estabelecimento", $estabelecimento);
        }

        $novo_material = $this->get_state('filter.novo_material');
        if (!empty($novo_material)) {
            $this->db->where("i.integracao_novo_material", $novo_material);
        }

        $importado = $this->get_state('filter.importado');
        if (!empty($importado)) {
            if ($importado == 'S') {
                $this->db->where("comex.ind_ecomex = 'EI'");
            } elseif ($importado == 'N') {
                $this->db->where("(comex.ind_ecomex <> 'EI' OR comex.ind_ecomex IS NULL )");
            }
        }

        $data_criacao_from = $this->get_state('filter.data_criacao_from');
        if (!empty($data_criacao_from)) {
            // Forçar interpretação como DD/MM/YYYY
            $date_parts = explode('/', $data_criacao_from);
            if (count($date_parts) == 3) {
                $converted_date = $date_parts[2] . '-' . str_pad($date_parts[1], 2, '0', STR_PAD_LEFT) . '-' . str_pad($date_parts[0], 2, '0', STR_PAD_LEFT);
            } else {
                $converted_date = date('Y-m-d', strtotime($data_criacao_from));
            }
            $this->db->where('date(i.dat_criacao) >=', $converted_date);
        }

        $data_criacao_to = $this->get_state('filter.data_criacao_to');
        if (!empty($data_criacao_to)) {
            // Forçar interpretação como DD/MM/YYYY
            $date_parts = explode('/', $data_criacao_to);
            if (count($date_parts) == 3) {
                $converted_date_to = $date_parts[2] . '-' . str_pad($date_parts[1], 2, '0', STR_PAD_LEFT) . '-' . str_pad($date_parts[0], 2, '0', STR_PAD_LEFT);
            } else {
                $converted_date_to = date('Y-m-d', strtotime($data_criacao_to));
            }
            $this->db->where('date(i.dat_criacao) <=', $converted_date_to);
        }

        $data_modificacao_from = $this->get_state('filter.data_modificacao_from');
        if (!empty($data_modificacao_from)) {
            // Forçar interpretação como DD/MM/YYYY
            $date_parts = explode('/', $data_modificacao_from);
            if (count($date_parts) == 3) {
                $converted_date_mod_from = $date_parts[2] . '-' . str_pad($date_parts[1], 2, '0', STR_PAD_LEFT) . '-' . str_pad($date_parts[0], 2, '0', STR_PAD_LEFT);
            } else {
                $converted_date_mod_from = date('Y-m-d', strtotime($data_modificacao_from));
            }
            $this->db->where('date(i.data_modificacao) >=', $converted_date_mod_from);
        }

        $data_modificacao_to = $this->get_state('filter.data_modificacao_to');
        if (!empty($data_modificacao_to)) {
            // Forçar interpretação como DD/MM/YYYY
            $date_parts = explode('/', $data_modificacao_to);
            if (count($date_parts) == 3) {
                $converted_date_mod_to = $date_parts[2] . '-' . str_pad($date_parts[1], 2, '0', STR_PAD_LEFT) . '-' . str_pad($date_parts[0], 2, '0', STR_PAD_LEFT);
            } else {
                $converted_date_mod_to = date('Y-m-d', strtotime($data_modificacao_to));
            }
            $this->db->where('date(i.data_modificacao) <=', $converted_date_mod_to);
        }

        $data_importado_from = $this->get_state('filter.data_importado_from');
        if (!empty($data_importado_from)) {
            // Forçar interpretação como DD/MM/YYYY
            $date_parts = explode('/', $data_importado_from);
            if (count($date_parts) == 3) {
                $converted_date_imp_from = $date_parts[2] . '-' . str_pad($date_parts[1], 2, '0', STR_PAD_LEFT) . '-' . str_pad($date_parts[0], 2, '0', STR_PAD_LEFT);
            } else {
                $converted_date_imp_from = date('Y-m-d', strtotime($data_importado_from));
            }
            $this->db->where('date(comex.data_criacao) >=', $converted_date_imp_from);
            $this->db->where('comex.ind_ecomex = "EI"', null, false);
        }

        $data_importado_to = $this->get_state('filter.data_importado_to');
        if (!empty($data_importado_to)) {
            // Forçar interpretação como DD/MM/YYYY
            $date_parts = explode('/', $data_importado_to);
            if (count($date_parts) == 3) {
                $converted_date_imp_to = $date_parts[2] . '-' . str_pad($date_parts[1], 2, '0', STR_PAD_LEFT) . '-' . str_pad($date_parts[0], 2, '0', STR_PAD_LEFT);
            } else {
                $converted_date_imp_to = date('Y-m-d', strtotime($data_importado_to));
            }
            $this->db->where('date(comex.data_criacao) <=', $converted_date_imp_to);
            $this->db->where('comex.ind_ecomex = "EI"', null, false);
        }

        $where_in = "";

        if ($search = $this->get_state('filter.search')) {

            $keywords = array_filter(preg_split('/\r\n|[\r\n]/', $search));

            if (count($keywords) > 1) {

                $adwhereplus = "";
                foreach ($keywords as $keyword) {
                    if ($keyword !== '') {
                        $keyword = str_replace("*", "%",  $keyword);
                        if ($hasPnPrimarioSecundario) {
                            $adwhereplus .= ' i.part_number LIKE "' . trim($keyword) . '" OR i.pn_primario_mpn LIKE "' . trim($keyword) . '" OR i.pn_secundario_ipn LIKE "' . trim($keyword) . '" OR ';
                        } else {
                            $adwhereplus .= ' i.part_number LIKE "' . trim($keyword) . '" OR ';
                        }
                    }
                }

                $adwhereplus = rtrim($adwhereplus, 'OR ');

                $where_in .= ' (' . $adwhereplus . ')';

                $this->db->where($where_in, NULL, FALSE);
            } else if (strpos($search, "\n") !== false) {

                $search = str_replace(array("\t", "\r\n", "\s", "\n"), '#', $search);

                $keywords = explode('#', $search);
                $adwhereplus = "";
                $adwhere_go = "";

                foreach ($keywords as $keyword) {

                    $keyword = str_replace("*", "%", $keyword);

                    $keyword = rtrim($keyword, " ");

                    if ($keyword !== '') {
                        if ($hasPnPrimarioSecundario) {
                            $adwhereplus .= ' i.part_number LIKE "' . trim($keyword) . '" OR i.pn_primario_mpn LIKE "' . trim($keyword) . '" OR i.pn_secundario_ipn LIKE "' . trim($keyword) . '" OR ';
                        } else {
                            $adwhereplus .= ' i.part_number LIKE "' . trim($keyword) . '" OR ';
                        }
                    }
                }
                $adwhere_go = rtrim($adwhereplus, 'OR ');

                $where_in .= ' (' . $adwhere_go . ')';
                if ($where_in != ' ()') {
                    $this->db->where($where_in, NULL, FALSE);
                }
            } else {
                if ($where_in == '') {
                    if (strpos($search, '*') !== false) {


                        if (is_array($search)) {
                            $search = $search[0];
                        }


                        if (substr_count($search, '*') == 1) {

                            if (substr($search, 0, 1) === '*') {
                                $search = str_replace("*", "",  $search);
                                if ($hasPnPrimarioSecundario) {
                                    $where_in  .= ' (i.descricao LIKE "%' . $search . '" OR i.part_number LIKE "%' . $search . '" OR i.pn_primario_mpn LIKE "%' . $search . '" OR i.pn_secundario_ipn LIKE "%' . $search . '")';
                                } else {
                                    $where_in  .= ' (i.descricao LIKE "%' . $search . '" OR i.part_number LIKE "%' . $search . '")';
                                }
                            } else if (substr($search, -1) === '*') {
                                $search = str_replace("*", "",  $search);
                                if ($hasPnPrimarioSecundario) {
                                    $where_in  .= ' (i.descricao LIKE "' . $search . '%" OR i.part_number LIKE "' . $search . '%" OR i.pn_primario_mpn LIKE "' . $search . '%" OR i.pn_secundario_ipn LIKE "' . $search . '%")';
                                } else {
                                    $where_in  .= ' (i.descricao LIKE "' . $search . '%" OR i.part_number LIKE "' . $search . '%")';
                                }
                            } else {
                                $search = str_replace("*", "%",  $search);

                                if ($hasPnPrimarioSecundario) {
                                    $where_in  .= ' (i.descricao LIKE "' . $search . '" OR i.part_number LIKE "' . $search . '" OR i.pn_primario_mpn LIKE "' . $search . '" OR i.pn_secundario_ipn LIKE "' . $search . '" )';
                                } else {
                                    $where_in  .= ' (i.descricao LIKE "' . $search . '" OR i.part_number LIKE "' . $search . '")';
                                }
                            }
                        }

                        if (substr($search, 0, 1) === '*' && substr($search, -1) === '*' && substr_count($search, '*') <= 2) {
                            $search = str_replace("*", "",  $search);
                            if ($hasPnPrimarioSecundario) {
                                $where_in  .= ' (i.descricao LIKE "%' . $search . '%" OR i.part_number LIKE "%' . $search . '%" OR i.pn_primario_mpn LIKE "%' . $search . '%" OR i.pn_secundario_ipn LIKE "%' . $search . '%" )';
                            } else {
                                $where_in  .= ' (i.descricao LIKE "%' . $search . '%" OR i.part_number LIKE "%' . $search . '%")';
                            }
                        } else {
                            if (substr_count($search, '*') >= 2) {

                                $adwhereplus = '';

                                $search = str_replace("*", "%", $search);

                                if ($search !== '') {

                                    if ($hasPnPrimarioSecundario) {
                                        $adwhereplus .= 'i.descricao LIKE "' . $search . '" OR i.part_number LIKE "' . $search . '" OR i.pn_primario_mpn LIKE "' . $search . '" OR i.pn_secundario_ipn LIKE "' . $search . '" OR ';
                                    } else {
                                        $adwhereplus .= 'i.descricao LIKE "' . $search . '" OR i.part_number LIKE "' . $search . '" OR ';
                                    }
                                }

                                $adwhereplus = rtrim($adwhereplus, 'OR ');

                                $where_in .= ' (' . $adwhereplus . ')';
                            }
                        }

                        $this->db->where($where_in, NULL, FALSE);
                    } else {

                        if (!empty($search)) {
                            if (strpos($search, ',') !== false) {
                                $keywords = explode(',', $search);
                                $adwhere_go = "";
                                foreach ($keywords as $keyword) {
                                    if ($keyword !== '') {
                                        if ($hasPnPrimarioSecundario) {
                                            $adwhere_go .= ' i.part_number LIKE "%' . $keyword  . '%" OR i.pn_primario_mpn LIKE "%' . $keyword . '%" OR i.pn_secundario_ipn LIKE "%' . $keyword . '%" OR';
                                        } else {
                                            $adwhere_go .= ' i.part_number LIKE "%' . $keyword  . '%" OR ';
                                        }
                                    }
                                }
                                $adwhere_go = rtrim($adwhere_go, 'OR ');
                                $where_in .= ' (' . $adwhere_go . ')';

                                if ($where_in != ' ()') {
                                    $this->db->where($where_in, NULL, FALSE);
                                }
                            } else {
                                if ($hasPnPrimarioSecundario) {
                                    $this->db->where("(i.descricao LIKE '{$search}' OR i.part_number LIKE '{$search}' OR i.pn_primario_mpn LIKE '{$search}' OR i.pn_secundario_ipn LIKE '{$search}')", NULL, FALSE);
                                } else {
                                    $this->db->where("(i.descricao LIKE '{$search}' OR i.part_number LIKE '{$search}')", NULL, FALSE);
                                }
                            }
                        }
                    }
                }
            }
        }

        if ($id_usuario && $id_usuario != -1) {
            if ($id_usuario == 'sem_responsavel') {
                $this->db->where("i.id_resp_engenharia IS NULL");
            } else {
                $this->db->where("i.{$field_responsavel}", $id_usuario);
            }
        }

        $this->db->join('cad_item c', 'c.part_number=i.part_number AND c.id_empresa=i.id_empresa AND c.estabelecimento = i.estabelecimento', 'left');
        $this->db->where('c.id_item IS NULL', NULL, NULL);
        if ($tag === 0 || $tag === '0') {
            $this->db->where('tag', '0');
        } else {
            if (!empty($tag)) {
                $this->db->where('tag', $tag);
            }
        }

        if (!empty($order_by)) {
            $this->db->order_by($order_by);
        }

        $this->db->from($this->_table . ' i');

        // Aplicar limit e offset para paginação
        if ($limit !== null) {
            $this->db->limit($limit, $offset ?: 0);
        }

        $query = $this->db->get();

        // Debugar a consulta
        // ddQuery(true);

        if ($query->num_rows() > 0) {
            return $query->result();
        } else {
            return NULL;
        }
    }

    public function get_itens_tags($id_empresa, $id_usuario = NULL)
    {
        $show_all = $this->get_state('filter.show_all');

        $select = "
            i.tag,
            (COUNT(i.part_number) - COUNT(c.part_number)) as total_itens_pendentes,
            (
                (COUNT(c.part_number) * 100) / COUNT(i.part_number)
            ) AS perc
        ";

        $adwhere = "";

        if ($show_all != 1 && $id_usuario && $id_usuario != -1) {
            if ($id_usuario == 'sem_responsavel') {
                $adwhere = "(i.id_resp_engenharia IS NULL)";
            } else {
                $adwhere = "(i.id_resp_engenharia = '{$id_usuario}' OR i.id_resp_fiscal = '{$id_usuario}')";
            }
        }

        $this->db->select($select, FALSE);
        $this->db->where('i.id_empresa', $id_empresa);

        if ($adwhere) {
            $this->db->where($adwhere, NULL, FALSE);
        }

        $this->db->join(
            'cad_item c',
            'c.part_number = i.part_number AND c.estabelecimento = i.estabelecimento AND c.id_empresa = i.id_empresa',
            'left'
        );

        $this->db->group_by('i.tag');
        $this->db->order_by('i.tag ASC');
        $query = $this->db->get($this->_table . ' i');

        return $query->result();
    }

    public function get_item_tag($part_number, $id_empresa, $estabelecimento = null)
    {
        $this->db->select('tag');
        $this->db->where('part_number', $part_number);
        $this->db->where('id_empresa', $id_empresa);

        if (!empty($estabelecimento)) {
            $this->db->where('estabelecimento', $estabelecimento);
        }

        $query = $this->db->get($this->_table);

        return $query->row();
    }

    public function get_total_tag($tag, $id_empresa, $id_usuario)
    {
        $this->db->select('i.part_number');

        $this->db->where('i.id_empresa', $id_empresa);
        $this->db->where('i.tag', $tag);

        if ($id_usuario && $id_usuario != -1) {
            if ($id_usuario == 'sem_responsavel') {
                $this->db->where('i.id_resp_engenharia is null');
            } else {
                $this->db->where("(i.id_resp_engenharia = '{$id_usuario}' or i.id_resp_fiscal = '{$id_usuario}')");
            }
        }

        $this->db->where('F (
            select 1 from cad_item c
            where i.part_number = c.part_number
            and   i.id_empresa  = c.id_empresa
            and   i.estabelecimento = c.estabelecimento
        )', NULL, FALSE);

        $query = $this->db->get($this->_table . ' i');

        $total_cad_item = $query->num_rows();

        return $total_cad_item;
    }

    public function get_status_list()
    {
        $query = $this->db->get('status');

        return $query->result();
    }


    public function get_config_status($id_empresa)
    {
        $this->db->where('id_empresa', $id_empresa);
        $query = $this->db->get('rel_status');

        return $query->result();
    }

    public function set_change_send_mail($data)
    {
        $part_number = $data['part_number'];
        $id_empresa  = $data['id_empresa'];
        $estabelecimento  = $data['estabelecimento'];
        $item = $this->get_entry($part_number, $id_empresa, $estabelecimento);

        $send_mail = false;
        if (empty($item)) {
            return false;
        }

        if ($data['status_atual'] != 2 && $data['status_atual'] != 3 && $data['status_atual'] != 6 && $data['status_atual'] != 7) {
            return false;
        }

        $data['status_anterior'] =  null;
        if ($item->id_status != $data['status_atual']) {
            $data['status_anterior'] =  $item->id_status;
            $send_mail = true;
        }
        $data['responsavel_engenharia_anterior'] =  null;
        if ($item->id_resp_engenharia != $data['responsavel_engenharia_atual']) {
            $data['responsavel_engenharia_anterior'] =  $item->id_resp_engenharia;
            $send_mail = true;
        }
        $data['responsavel_fiscal_anterior'] =  null;
        if ($item->id_resp_fiscal != $data['responsavel_fiscal_atual']) {
            $data['responsavel_fiscal_anterior'] =  $item->id_resp_fiscal;
            $send_mail = true;
        }

        $date = new DateTime();
        $data['data_alteracao'] = $date->format('Y-m-d H:i:s');

        $verifica_usuarios = $this->get_config_status($id_empresa);
        if ($send_mail == true && !empty($verifica_usuarios)) {
            $this->db->insert('rotina_email', $data);
        }
    }

    public function update_item($part_number, $id_empresa, $data = array(), $motivo = FALSE, $estabelecimento = NULL, $id_usuario = NULL)
    {

        $item = $this->get_entry($part_number, $id_empresa);
        $data_estabelecimento = isset($data['estabelecimento']) && !empty($data['estabelecimento']) ? $data['estabelecimento'] : '';
        $data_estabelecimento = isset($estabelecimento) && !empty($estabelecimento) ? $estabelecimento : $data_estabelecimento;
        $data_descricao = isset($data['descricao']) && !empty($data['descricao']) ? $data['descricao'] : '';
        $data_status = isset($data['id_status']) && !empty($data['id_status']) ? $data['id_status'] : '';
        $data_resp_engenharia = isset($data['id_resp_engenharia']) && !empty($data['id_resp_engenharia']) ? $data['id_resp_engenharia'] : '';
        $data_resp_fiscal = isset($data['id_resp_fiscal']) && !empty($data['id_resp_fiscal']) ? $data['id_resp_fiscal'] : '';
        $data_id_prioridade = isset($data['id_prioridade']) && !empty($data['id_prioridade']) ? $data['id_prioridade'] : '';
        $is_drawback = isset($data['is_drawback']) && !empty($data['is_drawback']) ? $data['is_drawback'] : '';

        $this->db->set($data);
        $this->db->where('id_empresa', $id_empresa);
        $this->db->where('part_number', $part_number);

        if (!empty($estabelecimento)) {
            $this->db->where('estabelecimento', $estabelecimento);
        } else {
            $this->db->where("(estabelecimento IS NULL OR estabelecimento = '')", null, FALSE);
        }

        $res = $this->db->update('item i');

        if ($motivo !== FALSE) {
            $dbdata = array(
                'part_number'       => $part_number,
                'id_empresa'        => $id_empresa,
                'id_usuario'        => !empty($id_usuario) ? $id_usuario : sess_user_id(),
                'titulo'            => 'atualizacao',
                'motivo'            => $motivo,
                'criado_em'         => date("Y-m-d H:i:s"),
                'id_item'           => isset($item->id_item) ? $item->id_item : 0
            );

            if (!empty($estabelecimento)) {
                $dbdata['estabelecimento'] = $estabelecimento;
            }

            $this->db->insert('item_log', $dbdata);
        }

        $this->db->select('status_exportacao');
        $this->db->where('part_number', $part_number);
        $this->db->where('id_empresa', $id_empresa);
        $query = $this->db->get('cad_item');
        $row   = $query->row();
        if ($row) {
            $status_export = $row->status_exportacao;

            if ($res && $status_export == 1) {
                $this->load->model('cad_item_model');
                $this->cad_item_model->update_item($part_number, $id_empresa, NULL, FALSE, $estabelecimento);
            }
        }

        return $res;
    }

    public function check_ncm_divergente($item, $data)
    {
        $ncm_formatado = preg_replace("/[^0-9]/", "", $item['ncm']);
        $ncm_original = $item['ncm'];
        $this->db->select('ncm_proposto as ncm, estabelecimento');
        $this->db->where('id_empresa', $data['id_empresa']);
        $this->db->where('part_number', $item['part_number']);
        $this->db->where('estabelecimento <> ', $data['estabelecimento']);
        $this->db->where("( ncm_proposto <> '{$ncm_original}'  AND  ncm_proposto <> '{$ncm_formatado}' )", null, false);
        $query = $this->db->get('cad_item');

        return $query->result();
    }

    public function get_entries_for_xls($id_empresa)
    {
        $useViewComex = $this->config->item('use_view_comex');
        $this->load->model('empresa_model');
        $empresa = $this->empresa_model->get_entry(sess_user_company());
        $data = [];
        $data['campos_adicionais'] = explode("|", $empresa->campos_adicionais);
        $hasPnPrimarioSecundario = in_array('pn_primario_secundario', $data['campos_adicionais']);

        if ($id_usuario = $this->get_state('filter.atribuido_para')) {
            $field_responsavel = customer_has_role('engenheiro', $id_usuario) ?
                'id_resp_engenharia' : 'id_resp_fiscal';
        }


        $this->db->where('i.id_empresa', $id_empresa);

        if ($useViewComex && $this->db->table_exists('vw_comex')) {
            $this->db->select("i.*,
            o.codigo as owner_codigo,
            o.descricao as owner_descricao,
            vw_comex.ind_ecomex as indicador_ecomex,
            vw_comex.num_di as num_di,
            vw_comex.data_di as data_di,
            vw_comex.ind_drawback as ind_drawback,
            vw_comex.ncm as ncm_ecomex,
            ep.nome as empresa_prioridade,
            GROUP_CONCAT(DISTINCT rg.nome SEPARATOR ', ') as responsaveis_gestores_nomes,
            GROUP_CONCAT(DISTINCT rg.email SEPARATOR ', ') as responsaveis_gestores_emails, 
            GROUP_CONCAT(DATE_FORMAT(l.criado_em, '%d/%m/%Y'), '  - ', l.motivo SEPARATOR '\n\n') AS motivo", FALSE);
        } else if (!$useViewComex && $this->db->table_exists('comex')) {
            $this->db->select("i.*,
            ue.nome as resp_engenharia_nome,
            ue.email as resp_engenharia_email,
            uf.nome as resp_fiscal_nome,
            uf.email as resp_fiscal_email,
            o.codigo as owner_codigo,
            o.descricao as owner_descricao,
            comex.ind_ecomex as indicador_ecomex,
            comex.num_di as num_di,
            comex.data_di as data_di,
            comex.ind_drawback as ind_drawback,
            comex.ncm as ncm_ecomex,
            ep.nome as empresa_prioridade,
            GROUP_CONCAT(DISTINCT rg.nome SEPARATOR ', ') as responsaveis_gestores_nomes,
            GROUP_CONCAT(DISTINCT rg.email SEPARATOR ', ') as responsaveis_gestores_emails, 
            GROUP_CONCAT(DATE_FORMAT(l.criado_em, '%d/%m/%Y'), '  - ', l.motivo SEPARATOR '\n\n') AS motivo", FALSE);
        } else {
            $this->db->select("i.*,
            o.codigo as owner_codigo,
            o.descricao as owner_descricao,
            ep.nome as empresa_prioridade,
            GROUP_CONCAT(DISTINCT rg.nome SEPARATOR ', ') as responsaveis_gestores_nomes,
            GROUP_CONCAT(DISTINCT rg.email SEPARATOR ', ') as responsaveis_gestores_emails, 
            GROUP_CONCAT(DATE_FORMAT(l.criado_em, '%d/%m/%Y'), '  - ', l.motivo SEPARATOR '\n\n') AS motivo", FALSE);
        }

        if ($useViewComex && $this->db->table_exists('vw_comex')) {
            $this->db->join('vw_comex', 'vw_comex.part_number_original = i.part_number AND vw_comex.id_empresa = i.id_empresa', 'left');
        } else if (!$useViewComex && $this->db->table_exists('comex')) {
            $this->db->join('comex FORCE INDEX (index_helper)', 'comex.part_number_original = i.part_number AND comex.id_empresa = i.id_empresa AND comex.unidade_negocio = i.estabelecimento', 'left');
        }

        $this->db->join('item_log l', 'l.part_number = i.part_number AND l.id_empresa = i.id_empresa AND l.estabelecimento = i.estabelecimento', 'left');

        // JOIN TABELA OWNER
        $this->db->join('owner o', 'i.cod_owner = o.codigo', 'left');

        // Faça JOIN com as tabelas owner_usuario e usuario para buscar os responsáveis gestores
        $this->db->join('owner_usuario ou', 'o.id_owner = ou.id_owner AND ou.responsavel_gestor = 1', 'left');
        $this->db->join('usuario rg', 'ou.id_usuario = rg.id_usuario', 'left');
        $this->db->join('empresa_prioridades ep', 'ep.id_prioridade = i.id_prioridade', 'left');
        // Join com tabela usuário para buscar id_resp_fiscal
        $this->db->join('usuario uf', 'uf.id_usuario = i.id_resp_fiscal', 'left');
        // Join com tabela usuário para buscar id_resp_engenharia
        $this->db->join('usuario ue', 'ue.id_usuario = i.id_resp_engenharia', 'left');

        $where_in = "";
        if ($search = $this->get_state('filter.search')) {

            $keywords = array_filter(preg_split('/\r\n|[\r\n]/', $search));

            if (count($keywords) > 1) {

                $adwhereplus = "";
                foreach ($keywords as $keyword) {
                    if ($keyword !== '') {
                        $keyword = str_replace("*", "%",  $keyword);
                        if ($hasPnPrimarioSecundario) {
                            $adwhereplus .= ' i.part_number LIKE "' . trim($keyword) . '" OR i.pn_primario_mpn LIKE "' . trim($keyword) . '" OR i.pn_secundario_ipn LIKE "' . trim($keyword) . '" OR ';
                        } else {
                            $adwhereplus .= ' i.part_number LIKE "' . trim($keyword) . '" OR ';
                        }
                    }
                }

                $adwhereplus = rtrim($adwhereplus, 'OR ');

                $where_in .= ' (' . $adwhereplus . ')';

                $this->db->where($where_in, NULL, FALSE);
            } else if (strpos($search, "\n") !== false) {

                $search = str_replace(array("\t", "\r\n", "\s", "\n"), '#', $search);

                $keywords = explode('#', $search);
                $adwhereplus = "";
                $adwhere_go = "";

                foreach ($keywords as $keyword) {

                    $keyword = str_replace("*", "%", $keyword);

                    $keyword = rtrim($keyword, " ");

                    if ($keyword !== '') {
                        if ($hasPnPrimarioSecundario) {
                            $adwhereplus .= ' i.part_number LIKE "' . trim($keyword) . '" OR i.pn_primario_mpn LIKE "' . trim($keyword) . '" OR i.pn_secundario_ipn LIKE "' . trim($keyword) . '" OR ';
                        } else {
                            $adwhereplus .= ' i.part_number LIKE "' . trim($keyword) . '" OR ';
                        }
                    }
                }
                $adwhere_go = rtrim($adwhereplus, 'OR ');

                $where_in .= ' (' . $adwhere_go . ')';
                if ($where_in != ' ()') {
                    $this->db->where($where_in, NULL, FALSE);
                }
            } else {
                if ($where_in == '') {
                    if (strpos($search, '*') !== false) {


                        if (is_array($search)) {
                            $search = $search[0];
                        }


                        if (substr_count($search, '*') == 1) {

                            if (substr($search, 0, 1) === '*') {
                                $search = str_replace("*", "",  $search);
                                if ($hasPnPrimarioSecundario) {
                                    $where_in  .= ' (i.descricao LIKE "%' . $search . '" OR i.part_number LIKE "%' . $search . '" OR i.pn_primario_mpn LIKE "%' . $search . '" OR i.pn_secundario_ipn LIKE "%' . $search . '")';
                                } else {
                                    $where_in  .= ' (i.descricao LIKE "%' . $search . '" OR i.part_number LIKE "%' . $search . '")';
                                }
                            } else if (substr($search, -1) === '*') {
                                $search = str_replace("*", "",  $search);
                                if ($hasPnPrimarioSecundario) {
                                    $where_in  .= ' (i.descricao LIKE "' . $search . '%" OR i.part_number LIKE "' . $search . '%" OR i.pn_primario_mpn LIKE "' . $search . '%" OR i.pn_secundario_ipn LIKE "' . $search . '%")';
                                } else {
                                    $where_in  .= ' (i.descricao LIKE "' . $search . '%" OR i.part_number LIKE "' . $search . '%")';
                                }
                            } else {
                                $search = str_replace("*", "%",  $search);

                                if ($hasPnPrimarioSecundario) {
                                    $where_in  .= ' (i.descricao LIKE "' . $search . '" OR i.part_number LIKE "' . $search . '" OR i.pn_primario_mpn LIKE "' . $search . '" OR i.pn_secundario_ipn LIKE "' . $search . '" )';
                                } else {
                                    $where_in  .= ' (i.descricao LIKE "' . $search . '" OR i.part_number LIKE "' . $search . '")';
                                }
                            }
                        }

                        if (substr($search, 0, 1) === '*' && substr($search, -1) === '*' && substr_count($search, '*') <= 2) {
                            $search = str_replace("*", "",  $search);
                            if ($hasPnPrimarioSecundario) {
                                $where_in  .= ' (i.descricao LIKE "%' . $search . '%" OR i.part_number LIKE "%' . $search . '%" OR i.pn_primario_mpn LIKE "%' . $search . '%" OR i.pn_secundario_ipn LIKE "%' . $search . '%" )';
                            } else {
                                $where_in  .= ' (i.descricao LIKE "%' . $search . '%" OR i.part_number LIKE "%' . $search . '%")';
                            }
                        } else {
                            if (substr_count($search, '*') >= 2) {

                                $adwhereplus = '';

                                $search = str_replace("*", "%", $search);

                                if ($search !== '') {

                                    if ($hasPnPrimarioSecundario) {
                                        $adwhereplus .= 'i.descricao LIKE "' . $search . '" OR i.part_number LIKE "' . $search . '" OR i.pn_primario_mpn LIKE "' . $search . '" OR i.pn_secundario_ipn LIKE "' . $search . '" OR ';
                                    } else {
                                        $adwhereplus .= 'i.descricao LIKE "' . $search . '" OR i.part_number LIKE "' . $search . '" OR ';
                                    }
                                }

                                $adwhereplus = rtrim($adwhereplus, 'OR ');

                                $where_in .= ' (' . $adwhereplus . ')';
                            }
                        }

                        $this->db->where($where_in, NULL, FALSE);
                    } else {

                        if (!empty($search)) {
                            if (strpos($search, ',') !== false) {
                                $keywords = explode(',', $search);
                                $adwhere_go = "";
                                foreach ($keywords as $keyword) {
                                    if ($keyword !== '') {
                                        if ($hasPnPrimarioSecundario) {
                                            $adwhere_go .= ' i.part_number LIKE "%' . $keyword  . '%" OR i.pn_primario_mpn LIKE "%' . $keyword . '%" OR i.pn_secundario_ipn LIKE "%' . $keyword . '%" OR';
                                        } else {
                                            $adwhere_go .= ' i.part_number LIKE "%' . $keyword  . '%" OR ';
                                        }
                                    }
                                }
                                $adwhere_go = rtrim($adwhere_go, 'OR ');
                                $where_in .= ' (' . $adwhere_go . ')';

                                if ($where_in != ' ()') {
                                    $this->db->where($where_in, NULL, FALSE);
                                }
                            } else {
                                if ($hasPnPrimarioSecundario) {
                                    $this->db->where("(i.descricao LIKE '{$search}' OR i.part_number LIKE '{$search}' OR i.pn_primario_mpn LIKE '{$search}' OR i.pn_secundario_ipn LIKE '{$search}')", NULL, FALSE);
                                } else {
                                    $this->db->where("(i.descricao LIKE '{$search}' OR i.part_number LIKE '{$search}')", NULL, FALSE);
                                }
                            }
                        }
                    }
                }
            }
        }

        if ($this->get_state('filter.tag')) {
            $this->db->where('i.tag', $this->get_state('filter.tag'));
        }

        if ($sistema_origem = $this->get_state('filter.sistema_origem')) {

            $this->db->where_in("i.sistema_origem", $sistema_origem);
        }

        if ($id_usuario = $this->get_state('filter.id_usuario')) {
            $this->db->where("(i.id_resp_engenharia = '{$id_usuario}' OR i.id_resp_fiscal = '{$id_usuario}')", NULL, FALSE);
        }

        $this->db->join('cad_item c', 'c.part_number=i.part_number AND c.id_empresa=i.id_empresa AND c.estabelecimento = i.estabelecimento', 'left');

        if (!empty($this->get_state("filter.status"))) {
            $whereIn = array();

            if (in_array("pendente", $this->get_state("filter.status"))) {
                $whereIn[] = "pendente_duvidas";
            }

            if (in_array("analise", $this->get_state("filter.status"))) {
                $whereIn[] = "em_analise";
                $this->db->where('c.part_number IS NULL', NULL, NULL);
            }

            if (in_array("perguntasRespondidas", $this->get_state("filter.status"))) {
                $whereIn[] = "respondido";
            }

            if (in_array("revisar_informacoes_erp", $this->get_state("filter.status"))) {
                $whereIn[] = "revisar_informacoes_erp";
            }

            if (in_array("revisar_informacoes_tecnicas", $this->get_state("filter.status"))) {
                $whereIn[] = "revisar_informacoes_tecnicas";
            }
            if (in_array("aguardando_descricao", $this->get_state("filter.status"))) {
                $whereIn[] = "aguardando_descricao";
            }
            if (in_array("perguntas_respondidas_novas", $this->get_state("filter.status"))) {
                $whereIn[] = "perguntas_respondidas_novas";
            }

            if (in_array("aguardando_definicao_responsavel", $this->get_state("filter.status"))) {
                $whereIn[] = "aguardando_definicao_responsavel";
            }

            if (in_array("informacoes_erp_revisadas", $this->get_state("filter.status"))) {
                $whereIn[] = "informacoes_erp_revisadas";
            }

            $this->db->where_in("s.slug", $whereIn);
        } else {
            $this->db->where_in("s.slug", array('pendente_duvidas', 'em_analise', 'respondido', 'revisar_informacoes_erp', 'revisar_informacoes_tecnicas', 'aguardando_descricao', 'perguntas_respondidas_novas', 'informacoes_erp_revisadas', 'aguardando_definicao_responsavel'));
        }

        if (!empty($this->get_state("filter.owner"))) {
            $this->db->where_in("i.cod_owner", $this->get_state("filter.owner"));
        }

        if ($prioridade = $this->get_state('filter.prioridade')) {
            if (!empty($prioridade)) {
                $this->db->where_in('i.id_prioridade', $prioridade);
            }
        }
        $this->db->where('c.part_number IS NULL', NULL, NULL);
        $this->db->join('status s', 's.id=i.id_status', 'inner');

        if ($evento = $this->get_state("filter.evento")) {
            if (!is_array($evento)) {
                $evento = array($evento);
            }

            $newEvento = array_filter($evento, function ($item) {
                return $item != "sem_evento";
            });

            $eventoConcatenado = "";

            foreach ($newEvento as $item) {
                if (empty($eventoConcatenado)) {
                    $eventoConcatenado .= "'{$item}'";
                } else {
                    $eventoConcatenado .= ",'{$item}'";
                }
            }

            if (!empty($newEvento)) {
                if (in_array("sem_evento", $evento)) {
                    $this->db->where("(i.evento IN ({$eventoConcatenado}) OR (i.evento = '' OR i.evento IS NULL))", null, false);
                } else {
                    $this->db->where("(i.evento IN ({$eventoConcatenado}))", null, false);
                }
            } elseif (in_array("sem_evento", $evento)) {
                $this->db->where("(i.evento = '' OR i.evento IS NULL)", NULL, FALSE);
            }
        }

        if ($this->get_state('filter.atribuido_para') && $id_usuario && $id_usuario != -1) {
            if ($id_usuario == 'sem_responsavel') {
                $this->db->where("i.id_resp_engenharia IS NULL");
            } else {
                $this->db->where("i.{$field_responsavel}", $id_usuario);
            }
        }

        $estabelecimento = $this->get_state("filter.estabelecimento");
        if (!empty($estabelecimento)) {
            $this->db->where_in("i.estabelecimento", $estabelecimento);
        }

        // Filtro Status Triagem Diana
        $triagem_diana_falha = $this->get_state("filter.triagem_diana_falha");
        if ($triagem_diana_falha === '1') {
            $this->db->where("i.status_triagem_diana", 'Falha na triagem');
        }

        $novo_material = $this->get_state('filter.novo_material');
        if (!empty($novo_material)) {
            $this->db->where("i.integracao_novo_material", $novo_material);
        }

        $importado = $this->get_state('filter.importado');
        if (!empty($importado)) {
            if ($importado == 'S') {
                $this->db->where("comex.ind_ecomex = 'EI'");
            } elseif ($importado == 'N') {
                $this->db->where("(comex.ind_ecomex <> 'EI' OR comex.ind_ecomex IS NULL )");
            }
        }

        $data_criacao_from = $this->get_state('filter.data_criacao_from');
        if (!empty($data_criacao_from)) {
            // Forçar interpretação como DD/MM/YYYY
            $date_parts = explode('/', $data_criacao_from);
            if (count($date_parts) == 3) {
                $converted_date = $date_parts[2] . '-' . str_pad($date_parts[1], 2, '0', STR_PAD_LEFT) . '-' . str_pad($date_parts[0], 2, '0', STR_PAD_LEFT);
            } else {
                $converted_date = date('Y-m-d', strtotime($data_criacao_from));
            }
            $this->db->where('date(i.dat_criacao) >=', $converted_date);
        }

        $data_criacao_to = $this->get_state('filter.data_criacao_to');
        if (!empty($data_criacao_to)) {
            // Forçar interpretação como DD/MM/YYYY
            $date_parts = explode('/', $data_criacao_to);
            if (count($date_parts) == 3) {
                $converted_date_to = $date_parts[2] . '-' . str_pad($date_parts[1], 2, '0', STR_PAD_LEFT) . '-' . str_pad($date_parts[0], 2, '0', STR_PAD_LEFT);
            } else {
                $converted_date_to = date('Y-m-d', strtotime($data_criacao_to));
            }
            $this->db->where('date(i.dat_criacao) <=', $converted_date_to);
        }

        $data_modificacao_from = $this->get_state('filter.data_modificacao_from');
        if (!empty($data_modificacao_from)) {
            // Forçar interpretação como DD/MM/YYYY
            $date_parts = explode('/', $data_modificacao_from);
            if (count($date_parts) == 3) {
                $converted_date_mod_from = $date_parts[2] . '-' . str_pad($date_parts[1], 2, '0', STR_PAD_LEFT) . '-' . str_pad($date_parts[0], 2, '0', STR_PAD_LEFT);
            } else {
                $converted_date_mod_from = date('Y-m-d', strtotime($data_modificacao_from));
            }
            $this->db->where('date(i.data_modificacao) >=', $converted_date_mod_from);
        }

        $data_modificacao_to = $this->get_state('filter.data_modificacao_to');
        if (!empty($data_modificacao_to)) {
            // Forçar interpretação como DD/MM/YYYY
            $date_parts = explode('/', $data_modificacao_to);
            if (count($date_parts) == 3) {
                $converted_date_mod_to = $date_parts[2] . '-' . str_pad($date_parts[1], 2, '0', STR_PAD_LEFT) . '-' . str_pad($date_parts[0], 2, '0', STR_PAD_LEFT);
            } else {
                $converted_date_mod_to = date('Y-m-d', strtotime($data_modificacao_to));
            }
            $this->db->where('date(i.data_modificacao) <=', $converted_date_mod_to);
        }

        $data_importado_from = $this->get_state('filter.data_importado_from');
        if (!empty($data_importado_from)) {
            // Forçar interpretação como DD/MM/YYYY
            $date_parts = explode('/', $data_importado_from);
            if (count($date_parts) == 3) {
                $converted_date_imp_from = $date_parts[2] . '-' . str_pad($date_parts[1], 2, '0', STR_PAD_LEFT) . '-' . str_pad($date_parts[0], 2, '0', STR_PAD_LEFT);
            } else {
                $converted_date_imp_from = date('Y-m-d', strtotime($data_importado_from));
            }
            $this->db->where('date(comex.data_criacao) >=', $converted_date_imp_from);
            $this->db->where('comex.ind_ecomex = "EI"', null, false);
        }

        $data_importado_to = $this->get_state('filter.data_importado_to');
        if (!empty($data_importado_to)) {
            // Forçar interpretação como DD/MM/YYYY
            $date_parts = explode('/', $data_importado_to);
            if (count($date_parts) == 3) {
                $converted_date_imp_to = $date_parts[2] . '-' . str_pad($date_parts[1], 2, '0', STR_PAD_LEFT) . '-' . str_pad($date_parts[0], 2, '0', STR_PAD_LEFT);
            } else {
                $converted_date_imp_to = date('Y-m-d', strtotime($data_importado_to));
            }
            $this->db->where('date(comex.data_criacao) <=', $converted_date_imp_to);
            $this->db->where('comex.ind_ecomex = "EI"', null, false);
        }

        $this->db->group_by('i.part_number, i.estabelecimento');

        return $this->db->get('item i');
    }

    public function get_status()
    {
        $this->load->model('empresa_model');
        $empresa = $this->empresa_model->get_entry(sess_user_company());
        $campos_adicionais = explode("|", $empresa->campos_adicionais);
        $hasOwner = in_array("owner", $campos_adicionais);
        $hasTriagemOwner = in_array("triagem_owner", $campos_adicionais);
        $hasDescricaoGlobal = in_array("descricao_global", $campos_adicionais);

        $status = [
            [
                'status_formatado' => 'Homologado',
                'status' => 'homologado',
                'id' => 2
            ],
            [
                'status_formatado' => 'Reprovado',
                'status' => 'nao_homologado',
                'id' => 3
            ],
            [
                'status_formatado' => 'Inativo',
                'status' => 'obsoleto',
                'id' => 4
            ],
            [
                'status_formatado' => 'Pendente de Homologação',
                'status' => 'homologar',
                'id' => 1
            ],
            [
                'status_formatado' => 'Em Revisão',
                'status' => 'revisao',
                'id' => 5
            ],
            [
                'status_formatado' => 'Em Análise',
                'status' => 'em_analise',
                'id' => 6
            ],
            [
                'status_formatado' => 'Pendente de Informações',
                'status' => 'pendente_duvidas',
                'id' => 7
            ],
            [
                'status_formatado' => 'Perguntas Respondidas',
                'status' => 'respondido',
                'id' => 8
            ],
        ];

        if (in_array('owner', $campos_adicionais)) {
            array_splice($status, 1, 0, [[
                'status_formatado' => 'Homologado em Revisão',
                'status' => 'homologado_em_revisao',
                'id' => 10
            ]]);

            array_splice($status, 9, 0, [[
                'status_formatado' => 'Revisar Informações Técnicas',
                'status' => 'revisar_informacoes_tecnicas',
                'id' => 11
            ]]);
            array_splice($status, 15, 0, [[
                'status_formatado' => 'Perguntas Respondidas (Novas)',
                'status' => 'perguntas_respondidas_novas',
                'id' => 15
            ]]);
        }

        if (in_array("revisar_info_erp", $campos_adicionais)) {
            $status[] = [
                'status_formatado' => 'Revisar informações ERP',
                'status' => 'revisar_informacoes_erp',
                'id' => 9
            ];
            $status[] = [
                'status_formatado' => 'Informações ERP revisadas',
                'status' => 'informacoes_erp_revisadas',
                'id' => 12
            ];
        }

        // if (in_array('owner', $campos_adicionais)) {
        //     array_splice($status, 1, 0, [[
        //         'status_formatado' => 'Homologado em Revisão',
        //         'status' => 'homologado_em_revisao'
        //     ]]);
        //     array_splice($status, 8, 0, [[
        //         'status_formatado' => 'Revisar Informações Técnicas',
        //         'status' => 'revisar_informacoes_tecnicas'
        //     ]]);
        //     // array_splice($status, 6, 0, [[
        //     //     'status_formatado' => 'Aguardando Definição Responsável',
        //     //     'status' => 'aguardando_definicao_responsavel'
        //     // ]]);
        // }

        if ($hasOwner && $hasTriagemOwner) {
            array_splice($status, 6, 0, [[
                'status_formatado' => 'Aguardando Definição Responsável',
                'status' => 'aguardando_definicao_responsavel',
                'id' => 13
            ]]);
        }

        if ($hasDescricaoGlobal) {
            array_splice($status, 7, 0, [[
                'status_formatado' => 'Aguardando Descrição',
                'status' => 'aguardando_descricao',
                'id' => 14
            ]]);
        }


        return $status;
    }

    public function get_entries_pendente_atribuicao($limit = null, $offset = null)
    {
        $this->db->select('i.*');

        if ($part_numbers = $this->get_state('filter.part_numbers')) {
            if (!is_array($part_numbers)) {
                $part_numbers = array($part_numbers);
            }

            $this->db->where_in('part_number', $part_numbers);
        }

        $searchArray = $this->get_state("filter.searchArray");
        $empresa = $this->get_state('filter.id_empresa');
        $i = 0;
        if (!empty($searchArray) && is_array($searchArray)) {
            foreach ($searchArray as $k => $Array) {
                if ($Array == null || $Array == 'null')
                    continue;

                $i++;
                if ($searchArrayEstabelecimentos = $this->get_state("filter.searchArrayEstabelecimentos")) {
                    $estab = empty($searchArrayEstabelecimentos[$k]) ? 'IS NULL OR i.estabelecimento = ""' : '= "' . $searchArrayEstabelecimentos[$k] . '"';
                    if ($i > 1) {
                        $this->db->or_where("(i.part_number = '{$Array}' AND ( i.estabelecimento {$estab} ))", null, false);
                    } else {
                        $this->db->where("((i.part_number = '{$Array}' AND ( i.estabelecimento {$estab} ))", null, false);
                    }
                } else {
                    if ($i > 1) {
                        $this->db->or_where("(i.part_number = '{$Array}')", null, false);
                    } else {
                        $this->db->where("((i.part_number = '{$Array}')", null, false);
                    }
                }
            }
            $this->db->where("i.id_empresa = '{$empresa}')", null, false);
        }

        if ($this->get_state('filter.id_empresa')) {
            $this->db->where('i.id_empresa', $this->get_state('filter.id_empresa'));
        }

        if ($search = $this->get_state('filter.search')) {
            $this->db->like("i.part_number", $search);
        }

        // if ($searchArray = $this->get_state('filter.searchArray')) {
        //     $this->db->where_in("i.part_number", $searchArray);
        // }

        $this->db->join('empresa e', 'i.id_empresa = e.id_empresa', 'inner');
        $this->db->join(
            'cad_item ci',
            'i.part_number = ci.part_number and ci.estabelecimento = i.estabelecimento and ci.id_empresa = i.id_empresa',
            'left'
        );

        // $this->db->where('ci.id_item is null', null, false);

        $query = $this->db->get($this->_table . ' i', $limit, $offset);

        return $query->result();
    }

    public function get_usuario_responsavel_pn()
    {
        $this->load->model('empresa_model');
        $empresa = $this->empresa_model->get_entry(sess_user_company());
        $campos_adicionais = explode('|', $empresa->campos_adicionais);
        $hasOwner = in_array('owner', $campos_adicionais);

        $this->db->select('u.criado_em, u.nome, u.email, u.id_usuario');

        if ($part_numbers = $this->get_state('filter.part_numbers')) {
            if (!is_array($part_numbers)) {
                $part_numbers = array($part_numbers);
            }

            $this->db->where_in('i.part_number', $part_numbers);
        }

        if ($this->get_state('filter.id_empresa')) {
            $this->db->where('i.id_empresa', $this->get_state('filter.id_empresa'));
        }

        $this->db->join('empresa e', 'i.id_empresa = e.id_empresa', 'inner');
        $this->db->join(
            'cad_item ci',
            'i.part_number = ci.part_number and ci.estabelecimento = i.estabelecimento and ci.id_empresa = i.id_empresa',
            'left'
        );

        // if ($hasOwner) {
        //     $this->db->join('usuario u', 'u.id_usuario = i.criado_por', 'inner');
        // } else {
        $this->db->join('usuario u', 'u.id_usuario = i.id_resp_engenharia', 'inner');
        //}

        $this->db->group_by('u.id_usuario');

        // $this->db->where('ci.id_item is null', null, false);

        $query = $this->db->get($this->_table . ' i');

        if ($query->num_rows() > 1) {
            return array();
        }

        return $query->row();
    }

    public function getItensComPerguntasPendentes()
    {
        $this->db->where('i.id_empresa', sess_user_company());
        $this->db->where('pp.pendente', 1);

        $this->db->select("distinct(i.part_number)");

        $this->db->join("ctr_pendencias_pergunta pp", "i.part_number = pp.part_number AND i.estabelecimento = pp.estabelecimento", "inner");

        $query = $this->db->get($this->_table . ' i');

        return $query->num_rows();
    }

    public function getPartnumbers($text)
    {
        $this->db->like("part_number", $text);
        $this->db->where('i.id_empresa', sess_user_company());

        $query = $this->db->get($this->_table . ' i');

        return $query->result();
    }

    public function atualizar_owner($itens, $novo_owner, $id_empresa)
    {

        // Verificar se os parâmetros de entrada são válidos
        if (empty($itens) || empty($novo_owner) || empty($id_empresa)) {
            return FALSE;
        }

        $this->db->trans_begin();

        foreach ($itens as $item) {
            if (empty($item['part_number']))
                continue;

            $dados_item = $this->get_entry($item['part_number'], $id_empresa, $item['estabelecimento']);
            $this->db->where('part_number', $item['part_number']);
            $this->db->where('estabelecimento', $item['estabelecimento']);
            $this->db->where('id_empresa', $id_empresa);
            $this->db->update('item', array('cod_owner' => $novo_owner));

            if ($dados_item->id_status == 13) {
                $this->load->library('Item/Status');

                $this->status->set_status('em_analise');
                $this->status->update_item($item['part_number'], $item['estabelecimento'], $id_empresa);
            }
        }

        if ($this->db->trans_status() === FALSE) {
            $this->db->trans_rollback();
        } else {
            $this->db->trans_commit();
        }

        return $this->db->trans_status();
    }




    public function update_lessin($filters)
    {
        $this->db->where('c.id_empresa', sess_user_company());
        $this->db->where('c.part_number', $filters['part_number']);
        $this->db->where('c.estabelecimento', $filters['estabelecimento']);
        $this->db->join('item i', 'i.part_number = c.part_number AND i.estabelecimento = c.estabelecimento AND c.id_empresa = i.id_empresa');

        $query = $this->db->get('cad_item c');
        $row = $query->row();

        if (!empty($row)) {
            $this->load->model(array(
                'lessin_model',
                'item_log_model'
            ));

            $lessin = $this->lessin_model->utiliza_lessin(array(
                'ncm' => $row->ncm_proposto
            ), !empty($row->num_ex_ii) ? $row->num_ex_ii : false);

            $data = array(
                "regra_aplicada" => $lessin['regra'],
                "lista_becomex" => isset($lessin['utiliza_lessin']) && $lessin['utiliza_lessin'] ? 'SIM' : 'NÃO',
                "id_lessin" => $lessin['id']
            );

            try {
                $log_data['titulo'] = 'atualizacao_lessin';
                $log_data['motivo'] = "Atualização de Regra LESSIN para <strong>Regra aplicada:</strong> {$data['regra_aplicada']}";
                $log_data['motivo'] .= "<br /> <strong>Lista Becomex:</strong> {$data['lista_becomex']}";
                $log_data['motivo'] .= "<br /> <strong>NCM:</strong> {$row->ncm_proposto}";
                $log_data['part_number'] = $filters['part_number'];
                $log_data['estabelecimento'] = $filters['estabelecimento'];
                $log_data['id_usuario'] = sess_user_id();
                $log_data['id_empresa'] = sess_user_company();
                $log_data['criado_em'] = date('Y-m-d H:i:s');

                $this->item_log_model->save($log_data);
            } catch (Exception $e) {
                //do nothing
            }

            $this->db->where('id_empresa', sess_user_company());
            $this->db->where('part_number', $filters['part_number']);
            $this->db->where('estabelecimento', $filters['estabelecimento']);

            $this->db->update($this->_table, $data);

            $row->lista_becomex = $data['lista_becomex'];
            $row->id_lessin = $data['id_lessin'];
            $row->regra_aplicada = $data['regra_aplicada'];

            $this->lessin_model->set_state('id', $lessin['id']);
            return array(
                'item' => $row,
                'itemLessin' => !empty($lessin['id']) ? $this->lessin_model->get_item() : null
            );
        }

        return false;
    }

    public function getOwnerByItem($owner)
    {
        $this->db->select('o.codigo, o.id_owner, o.descricao, ou.id_owner_usuario, u.nome, u.email, ou.responsavel_gestor, GROUP_CONCAT(" ", u.nome ) AS nomes_resp');
        $this->db->from('owner o');
        $this->db->join('owner_usuario ou', 'o.id_owner = ou.id_owner', 'left');
        $this->db->join('usuario u', 'ou.id_usuario = u.id_usuario', 'left');
        $this->db->where('o.codigo', $owner);
        $this->db->where("ou.responsavel_gestor", 1);

        $query = $this->db->get();

        if ($query->num_rows() > 0) {
            return $query->result();
        } else {
            return false;
        }
    }
    // Método para atualizar tabela com os itens que já foram homologados
    // Para implementação de nova flag
    public function update_item_ja_homologado()
    {

        $this->db->set('item_ja_homologado', 1);
        $this->db->where('id_status', 2);
        $this->db->update('item');

        return $this->db->affected_rows() > 0;
    }

    /**
     * Summary of lista_itens_por_empresa_e_owner_e_status_revisao
     *  'status' => 'revisao'
     */
    public function lista_itens_por_empresa_e_owner_e_status($status = 'em_analise')
    {
        $this->db->query("SET SESSION sql_mode=''");
        $this->db->select('i.*, e.*, o.*');
        $this->db->where('s.slug', $status);
        $this->db->where('i.cod_owner is not null');
        $this->db->where('o.ativo', 1);

        $this->db->join('status s', 's.id = i.id_status', 'inner');
        $this->db->join('empresa e', 'e.id_empresa = i.id_empresa', 'inner');
        $this->db->join('owner o', 'o.codigo = i.cod_owner', 'inner');

        $this->db->group_by('i.id_empresa, i.part_number');
        $this->db->order_by('i.id_empresa asc');

        $query = $this->db->get($this->_table . ' i');

        return $query->result();
    }

    public function buscar_log_descricao_item($part_number, $id_empresa, $estabelecimento, $descricao)
    {
        $this->db->where('part_number', $part_number);
        $this->db->where('id_empresa', $id_empresa);
        $this->db->where('estabelecimento', $estabelecimento);
        $this->db->where('descricao_nova', $descricao);

        $query = $this->db->get('log_descricao_item');
        return $query->row();
    }

    public function atualizar_descricao_item($part_number, $id_empresa, $estabelecimento, $descricao_anterior)
    {
        $this->db->where('part_number', $part_number);
        $this->db->where('id_empresa', $id_empresa);
        $this->db->where('estabelecimento', $estabelecimento);

        $data = array(
            'descricao' => $descricao_anterior
        );

        return $this->db->update('item', $data);
    }

    public function get_itens_sem_owner_dia_anterior($id_status = 13)
    {
        $this->db->select('item.*, e.nome_fantasia as nome_empresa');
        $this->db->from('item');
        $this->db->join('empresa e', 'e.id_empresa = item.id_empresa', 'inner');
        $this->db->where("(cod_owner = '' OR cod_owner IS NULL)", NULL, FALSE);
        $this->db->where('id_status', $id_status);
        // $this->db->where('dat_criacao <', date('Y-m-d'));
        // $this->db->where('dat_criacao >=', date('Y-m-d', strtotime('-1 day')));

        $query = $this->db->get();

        return $query->result();
    }

    public function get_itens_sem_descricao_curta_dia_anterior($id_status = 14)
    {
        $this->db->select('item.*, e.nome_fantasia as nome_empresa');
        $this->db->from('item');
        $this->db->join('empresa e', 'e.id_empresa = item.id_empresa', 'inner');
        $this->db->where("(descricao = '' OR descricao IS NULL)", NULL, FALSE);
        $this->db->where('id_status', $id_status);
        // $this->db->where('dat_criacao <', date('Y-m-d'));
        // $this->db->where('dat_criacao >=', date('Y-m-d', strtotime('-1 day')));

        $query = $this->db->get();

        return $query->result();
    }

    public function get_user_owner_code($id_usuario, $force = FALSE)
    {
        if (!sess_user_owner() || $force) {
            $this->db->select('ow.codigo');
            $this->db->from('owner ow');
            $this->db->join('owner_usuario owu', 'ow.id_owner = owu.id_owner', 'inner');
            $this->db->where('owu.id_usuario', $id_usuario);


            $query = $this->db->get();

            if ($query->num_rows() > 0) {
                $this->session->set_userdata(['owner_id' =>  $query->row()->codigo]);
                return $query->row()->codigo;
            }
        } else {
            return sess_user_owner();
        }

        return NULL;
    }

    public function get_items_created_user($id_usuario)
    {
        $id_empresa = sess_user_company();
        $this->db->select('i.part_number');
        $this->db->from('item i');
        $this->db->where('i.criado_por', $id_usuario);
        $this->db->where('i.id_empresa', $id_empresa);
        $query = $this->db->get();

        if ($query->num_rows() > 0) {
            return $query->result();
        }

        return NULL;
    }

    public function get_itens_revisar_informacoes_erp($id_status = 9)
    {
        // buscar os itens que estejam no status 9 e trazer o motivo da revisão. Se não tiver motivo, trazer os itens mesmo assim, mesmo que não tiver motivo ou for null
        $this->db->select('item.*, e.nome_fantasia as nome_empresa, 
        GROUP_CONCAT(DISTINCT lnu.motivo) as motivos, 
        o.codigo as owner_codigo, o.descricao as owner_descricao, 
        GROUP_CONCAT(DISTINCT rg.nome) as responsaveis_gestores_nomes');
        $this->db->from('item');
        $this->db->join('empresa e', 'e.id_empresa = item.id_empresa', 'inner');
        $this->db->join('owner o', 'item.cod_owner = o.codigo', 'left');
        // Faça JOIN com as tabelas owner_usuario e usuario para buscar os responsáveis gestores
        $this->db->join('owner_usuario ou', 'o.id_owner = ou.id_owner AND ou.responsavel_gestor = 1', 'left');
        $this->db->join('usuario rg', 'ou.id_usuario = rg.id_usuario', 'left');
        $this->db->join('log_notificacao_usuario lnu', 'lnu.part_number = item.part_number', 'left');
        $this->db->where('id_status', $id_status);
        $this->db->group_by('item.part_number');

        $query = $this->db->get();

        return $query->result();
    }

    public function get_user_owner_codes($id_usuario, $force = FALSE)
    {
        if (!sess_user_owners() || $force) {
            $this->db->select('ow.codigo');
            $this->db->from('owner ow');
            $this->db->join('owner_usuario owu', 'ow.id_owner = owu.id_owner', 'inner');
            $this->db->where('owu.id_usuario', $id_usuario);

            $query = $this->db->get();

            if ($query->num_rows() > 0) {
                $owner_codes = array();
                foreach ($query->result() as $row) {
                    $owner_codes[] = $row->codigo;
                }
                $this->session->set_userdata(['owners_id' => $owner_codes]);
                return $owner_codes;
            }
        } else {
            return sess_user_owners();
        }

        return NULL;
    }

    public function get_list_sistemas_origens($id_empresa)
    {

        $data = [];
        $this->db->select('i.sistema_origem', FALSE);
        $this->db->where('i.id_empresa', $id_empresa);
        $this->db->where("TRIM(i.sistema_origem) != ''");
        $this->db->group_by('i.sistema_origem', TRUE);
        $this->db->order_by('i.sistema_origem');
        $this->db->from($this->_table . ' i');

        $query = $this->db->get();

        foreach ($query->result() as $row) {
            $data[] = $row->sistema_origem;
        }

        return $data;
    }
    public function set_usuario_bloqueador_itens($id_empresa, $part_numbers, $estabelecimentos = NULL)
    {

        $status = array();

        //Verifica se alguns desses itens está bloqueado por algum outro usuário
        $this->db->select('i.part_number, i.usuario_bloqueador, u.nome, u.email');
        $this->db->from('item i');
        $this->db->join('usuario u', 'u.id_usuario = i.usuario_bloqueador', 'inner');

        foreach ($part_numbers as $k => $part_number) {

            if ($estabelecimentos != NULL) {
                $this->db->where("(i.part_number = '{$part_number}' AND  i.estabelecimento = '{$estabelecimentos[$k]}')", NULL, FALSE);
            } else {
                $this->db->where('i.part_number', $part_number);
            }
        }

        $this->db->where('i.id_empresa', $id_empresa);

        $query = $this->db->get();

        if ($query->num_rows() > 0) {
            $itens_bloqueados_terceiro = array();

            foreach ($query->result() as $row) {
                if ($row->usuario_bloqueador != sess_user_id()) {
                    $itens_bloqueados_terceiro[] = [
                        'part_number' =>  $row->part_number,
                        'usuario_bloqueador' => $row->usuario_bloqueador,
                        'nome' => $row->nome,
                        'email' => $row->email
                    ];
                }
            }

            if (count($itens_bloqueados_terceiro) > 0) {
                $status[] = [
                    'status' => 'itens_bloqueados_terceiro',
                    'itens' => $itens_bloqueados_terceiro
                ];
                return $status;
            }
        }

        //Desbloqueia todos os itens do usuário
        $this->unset_usuario_bloqueador_itens($id_empresa, $estabelecimento = NULL);

        //Bloqueia o que está vindo
        foreach ($part_numbers as $k => $part_number) {

            $this->db->where('id_empresa', $id_empresa);
            if ($estabelecimentos != NULL) {
                $this->db->where("(part_number = '{$part_number}' AND  estabelecimento = '{$estabelecimentos[$k]}')", NULL, FALSE);
            } else {
                $this->db->where('part_number', $part_number);
            }
            $data = array(
                'usuario_bloqueador' => sess_user_id()
            );

            $this->db->update('item', $data);
        }


        $status[] = [
            'status' => 'ok',
            'itens' => null
        ];
        return $status;
    }

    public function unset_usuario_bloqueador_itens($id_empresa = NULL,  $estabelecimento = NULL)
    {
        //Desbloqueia todos os itens do usuário
        $this->db->where('usuario_bloqueador',  sess_user_id());

        if ($id_empresa != NULL) {
            $this->db->where('id_empresa', $id_empresa);
        }

        if ($estabelecimento != NULL) {
            $this->db->where('estabelecimento', $estabelecimento);
        }

        $data = array(
            'usuario_bloqueador' => NULL
        );

        return $this->db->update('item', $data);
    }

    public function set_desbloqueia_item($part_number, $id_empresa = NULL, $estabelecimento = NULL)
    {
        //Desbloqueia todos os itens do usuário
        $this->db->where('part_number',  $part_number);

        if ($id_empresa != NULL) {
            $this->db->where('id_empresa', $id_empresa);
        } else {
            $status[] = [
                'status' => 'erro',
                'msg' => 'Empresa não informada!'
            ];
            return $status;
        }

        if ($estabelecimento != NULL) {
            $this->db->where('estabelecimento', $estabelecimento);
        } else {
            $status[] = [
                'status' => 'erro',
                'msg' => 'Estabelecimento não informado!'
            ];
            return $status;
        }

        $data = array(
            'usuario_bloqueador' => NULL
        );

        $this->db->update('item', $data);
        $status[] = [
            'status' => $part_number . ' Desbloqueado com sucesso!',
            'itens' => null
        ];
        return $status;
    }

    public function get_usuario_bloqueador_item($part_numbers, $id_empresa,  $estabelecimento = NULL)
    {
        //Verifica se alguns desses itens está bloqueado por algum outro usuário
        $this->db->select('i.part_number, i.usuario_bloqueador, u.nome, u.email');
        $this->db->from('item i');
        $this->db->join('usuario u', 'u.id_usuario = i.usuario_bloqueador', 'inner');
        $this->db->where_in('i.part_number', $part_numbers);

        if ($estabelecimento != NULL) {
            $this->db->where('i.estabelecimento', $estabelecimento);
        }
        $this->db->where('i.id_empresa', $id_empresa);

        $query = $this->db->get();

        if ($query->num_rows() > 0) {
            $itens_bloqueados_terceiro = array();

            foreach ($query->result() as $row) {
                if ($row->usuario_bloqueador != sess_user_id()) {
                    $itens_bloqueados_terceiro[] = [
                        'part_number' =>  $row->part_number,
                        'usuario_bloqueador' => $row->usuario_bloqueador,
                        'nome' => $row->nome,
                        'email' => $row->email
                    ];
                }
            }

            if (count($itens_bloqueados_terceiro) > 0) {
                $status[] = [
                    'status' => 'itens_bloqueados_terceiro',
                    'itens' => $itens_bloqueados_terceiro
                ];
                return $status;
            }
        } else {
            $status[] = [
                'status' => 'ok',
                'itens' => null
            ];
            return $status;
        }
    }

    public function get_items_atribuicao_automatica()
    {
        $query = $this->db->query("SELECT
                                        part_number,estabelecimento,id_empresa,descricao
                                    FROM
                                        item i
                                    INNER JOIN status s2 ON
                                        i.id_status = s2.id
                                    WHERE
                                        s2.slug = 'respondido'
                                        AND i.id_empresa IN (SELECT
                                                                id_empresa
                                                            FROM
                                                                empresa e
                                                            WHERE
                                                                funcoes_adicionais LIKE '%atribuicao_automatica%')");

        if ($query->num_rows() > 0) {
            return $query->result();
        }

        return NULL;
    }

    public function get_entry_by_pn($part_number, $id_empresa, $estabelecimento = null)
    {
        $this->db->where('i.id_empresa', $id_empresa);
        $this->db->where('part_number', $part_number);

        if (!empty($estabelecimento)) {
            $this->db->where('i.estabelecimento', $estabelecimento);
        }

        $query = $this->db->get($this->_table . ' i');

        if ($query->num_rows() > 0) {
            return $query->row();
        }

        return false;
    }

    public function set_wf_status_atributos($part_number, $id_empresa, $estabelecimento, $status)
    {
        $this->db->where('part_number', $part_number);
        $this->db->where('id_empresa', $id_empresa);
        $this->db->where('estabelecimento', $estabelecimento);

        $data = array(
            'wf_status_atributos' => $status
        );

        return $this->db->update($this->_table, $data);
    }

    public function get_status_triagem_diana()
    {
        return [
            self::DIANA_STATUS_PENDENTE => 'Pendente de Triagem',
            self::DIANA_STATUS_APROVADA => 'Triagem Aprovada',
            self::DIANA_STATUS_REPROVADA => 'Triagem Reprovada',
            self::DIANA_STATUS_FALHA => 'Falha na Triagem'
        ];
    }

    /**
     * Busca itens para análise DIANA
     * que estejam no status 6 (Em análise) e status_triagem_diana 'Pendente de triagem'
     * @param int $id_empresa ID da empresa
     * @param int $limit Limite de itens a buscar (padrão: 3000)
     * @return array Lista de itens para análise
     */
    public function get_itens_para_triagem_diana_pendente($id_empresa, $limit = 3000)
    {
        $this->db->select('
            i.part_number,
            i.estabelecimento,
            i.id_empresa,
            i.descricao,
            i.funcao,
            i.aplicacao,
            i.material_constitutivo,
            i.status_triagem_diana
        ');

        $this->db->from('item i');

        // Filtrar apenas itens com status 6 (Em análise)
        $this->db->where('i.id_status', 6);

        // Filtrar apenas itens com status_triagem_diana 'Pendente de triagem'
        $this->db->where('i.status_triagem_diana', 'Pendente de triagem');

        // Filtrar apenas itens da empresa informada
        $this->db->where('i.id_empresa', $id_empresa);

        // Ordenar por data de criação (mais antigos primeiro) para processar FIFO
        $this->db->order_by('i.dat_criacao ASC, i.part_number ASC');

        // Aplicar limite
        if ($limit > 0) {
            $this->db->limit($limit);
        }

        $query = $this->db->get();

        return $query->result();
    }

    /**
     * Busca itens para análise DIANA
     * que estejam no status 8 (Perguntas Respondidas) e status_triagem_diana 'Triagem reprovada'
     * e as 3 perguntas, caso existam:
     * Qual a função do item (Para que ele serve?)
     * Qual a aplicação do item? (Onde ele é utilizado?)
     * Qual o material constitutivo do item?
     * e respostas, caso tenham sido respondidas
     * @param int $id_empresa ID da empresa
     * @param int $limit Limite de itens a buscar (padrão: 3000)
     * @return array Lista de itens para análise
     */
    public function get_itens_para_triagem_diana_reprovada($id_empresa, $limit = 3000)
    {
        // Primeiro, buscar apenas os itens únicos que atendem aos critérios
        $this->db->select('
            i.part_number,
            i.estabelecimento,
            i.id_empresa,
            i.descricao,
            i.funcao,
            i.aplicacao,
            i.material_constitutivo,
            i.status_triagem_diana
        ');

        $this->db->from('item i');

        // Filtrar apenas itens com status 8 (Perguntas Respondidas)
        $this->db->where('i.id_status', 8);

        // Filtrar apenas itens com status_triagem_diana 'Triagem reprovada'
        $this->db->where('i.status_triagem_diana', 'Triagem reprovada');

        // Filtrar apenas itens da empresa informada
        $this->db->where('i.id_empresa', $id_empresa);

        // Ordenar por data de criação (mais antigos primeiro) para processar FIFO
        $this->db->order_by('i.dat_criacao ASC, i.part_number ASC');

        // Aplicar limite
        if ($limit > 0) {
            $this->db->limit($limit);
        }

        $query = $this->db->get();
        $itens = $query->result();

        // Para cada item, buscar suas perguntas e respostas
        foreach ($itens as &$item) {
            $item->perguntas_respostas = $this->get_perguntas_respostas_diana($item->part_number, $item->estabelecimento, $item->id_empresa);
        }

        return $itens;
    }

    /**
     * Busca perguntas e respostas DIANA para um item específico
     */
    private function get_perguntas_respostas_diana($part_number, $estabelecimento, $id_empresa)
    {
        $this->db->select('p.pergunta, r.resposta');
        $this->db->from('ctr_pendencias_pergunta p');
        $this->db->join('ctr_resposta r', 'r.id_pergunta = p.id', 'left');

        $this->db->where('p.part_number', $part_number);
        $this->db->where('p.estabelecimento', $estabelecimento);
        $this->db->where('p.id_empresa', $id_empresa);

        // Filtrar apenas as 3 perguntas específicas da DIANA
        $perguntas_diana = [
            'Qual a função do item (Para que ele serve?)',
            'Qual a aplicação do item? (Onde ele é utilizado?)',
            'Qual o material constitutivo do item?'
        ];
        $this->db->where_in('p.pergunta', $perguntas_diana);

        $query = $this->db->get();
        return $query->result();
    }

    /**
     * Atualiza o status de triagem DIANA de um item
     *
     * @param string $part_number
     * @param string $estabelecimento
     * @param int $id_empresa
     * @param string $status_triagem_anterior Status anterior da triagem DIANA
     * @param string $status_triagem_novo Novo status da triagem DIANA
     * @return bool
     */
    public function update_status_triagem_diana(
        $part_number,
        $estabelecimento,
        $id_empresa,
        $status_triagem_anterior,
        $status_triagem_novo
    ) {
        $data = [
            'status_triagem_diana' => $status_triagem_novo
        ];


        $this->db->where('part_number', $part_number);
        $this->db->where('estabelecimento', $estabelecimento);
        $this->db->where('id_empresa', $id_empresa);

        $resultado = $this->db->update('item', $data);

        // Inserir informações de alteração do status de triagem na tabela de item_log
        if ($resultado) {
            $data_log = [
                'part_number' => $part_number,
                'estabelecimento' => $estabelecimento,
                'id_empresa' => $id_empresa,
                'id_usuario' => 1,
                'titulo' => 'atualizacao',
                'motivo' => "Alteração do Status de Triagem DIANA: <em>{$status_triagem_anterior}</em> &rarr; <strong>{$status_triagem_novo}</strong>",
                'criado_em' => date('Y-m-d H:i:s'),
                'id_item' => 0
            ];

            $this->db->insert('item_log', $data_log);
        }

        return $resultado;
    }

    /**
     * Processa resultado da análise DIANA e atualiza status do item
     *
     * @param object $item Item a ser processado
     * @param array $resultado_analise Resultado da análise DIANA
     * @return bool
     */
    public function processar_resultado_diana($item, $resultado_analise)
    {
        if (!$resultado_analise['sucesso']) {
            log_message('error', "DIANA: Erro ao processar item {$item->part_number}: " . $resultado_analise['erro']);
            return false;
        }

        if ($resultado_analise['triagem_aprovada']) {
            // Triagem aprovada - mantém status atual e atualiza triagem
            return $this->update_status_triagem_diana(
                $item->part_number,
                $item->estabelecimento,
                $item->id_empresa,
                $item->status_triagem_diana,
                'Triagem aprovada'
            );
        } else {
            // Triagem reprovada - muda para status 7 (pendente_duvidas) e insere perguntas

            $sucesso_status = $this->update_status_triagem_diana(
                $item->part_number,
                $item->estabelecimento,
                $item->id_empresa,
                $item->status_triagem_diana,
                'Triagem reprovada'
            );

            if ($sucesso_status && !empty($resultado_analise['perguntas_faltantes'])) {
                $this->load->library('Item/Status');
                $this->status->set_status("pendente_duvidas");
                $this->status->update_item(
                    $item->part_number,
                    $item->estabelecimento,
                    $item->id_empresa
                );

                // Inserir perguntas pendentes
                return $this->inserir_perguntas_diana($item, $resultado_analise['perguntas_faltantes']);
            }

            return $sucesso_status;
        }
    }

    /**
     * Insere perguntas geradas pela análise DIANA
     *
     * @param object $item Item relacionado às perguntas
     * @param array $perguntas Lista de perguntas a serem inseridas
     * @return bool
     */
    private function inserir_perguntas_diana($item, $perguntas)
    {
        try {
            $this->load->model('ctr_pendencias_pergunta_model');

            $data_atual = date('Y-m-d H:i:s');

            foreach ($perguntas as $pergunta) {
                $dados_pergunta = [
                    'part_number' => $item->part_number,
                    'estabelecimento' => $item->estabelecimento,
                    'id_empresa' => $item->id_empresa,
                    'pergunta' => $pergunta,
                    'pendente' => 1,
                    'status' => 1,
                    'id_pergunta' => 0,
                    'anexo' => '',
                    'id_usuario_pergunta' => 1,
                    'id_usuario_resposta' => 0,
                    'criado_em' => $data_atual,
                    'inf_partnumber' => 0,
                    'atualizado_em' => $data_atual
                ];

                $this->db->insert('ctr_pendencias_pergunta', $dados_pergunta);
                $id_pergunta = $this->db->insert_id();

                if ($id_pergunta) {
                    $this->db->insert('rel_perguntas_responsavel', [
                        'id_ctr_pendencias' => $id_pergunta,
                        'id_responsavel' => 1
                    ]);
                }
            }

            return true;
        } catch (Exception $e) {
            log_message('error', "DIANA: Erro ao inserir perguntas para item {$item->part_number}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Processa resultado da análise DIANA para itens com respostas (passo 2)
     *
     * @param object $item Item a ser processado
     * @param array $resultado_analise Resultado da análise DIANA
     * @return bool
     */
    public function processar_resultado_diana_com_respostas($item, $resultado_analise)
    {
        if (!$resultado_analise['sucesso']) {
            log_message('error', "DIANA: Erro ao processar item com respostas {$item->part_number}: " . $resultado_analise['erro']);
            return false;
        }

        if ($resultado_analise['triagem_aprovada']) {
            // Triagem aprovada - mantém status 8 (Perguntas respondidas) e atualiza triagem para "Triagem aprovada"
            return $this->update_status_triagem_diana(
                $item->part_number,
                $item->estabelecimento,
                $item->id_empresa,
                'Triagem reprovada',
                'Triagem aprovada'
            );
        } else {
            // Falha na triagem - mantém status 8 (Perguntas respondidas) e atualiza triagem para "Falha na triagem"
            // Não insere novas perguntas para evitar loop infinito
            return $this->update_status_triagem_diana(
                $item->part_number,
                $item->estabelecimento,
                $item->id_empresa,
                'Triagem reprovada',
                'Falha na triagem'
            );
        }
    }

    /**
     * Calcula dados de SLA para um item específico
     */
    public function calculate_sla_data($part_number, $id_empresa, $estabelecimento)
    {
        // Buscar horas SLA da prioridade
        $this->db->select('ep.qdt_horas_uteis');
        $this->db->from('item i');
        $this->db->join('empresa_prioridades ep', 'ep.id_prioridade = i.id_prioridade', 'left');
        $this->db->where('i.part_number', $part_number);
        $this->db->where('i.id_empresa', $id_empresa);
        $this->db->where('i.estabelecimento', $estabelecimento);
        $prioridade_query = $this->db->get();
        $horas_sla_prioridade = $prioridade_query->num_rows() > 0 ? $prioridade_query->row()->qdt_horas_uteis : 0;

        // Calcular tempo consumido em status Becomex (histórico)
        // Usando F_HOUR_DIFF_UTIL que existe no banco
        $this->db->select('SUM(F_HOUR_DIFF_UTIL(data_status_anterior, data_novo_status, 8.00)) as tempo_consumido', FALSE);
        $this->db->from('item_log_status_sla');
        $this->db->where('part_number', $part_number);
        $this->db->where('id_empresa', $id_empresa);
        $this->db->where('estabelecimento', $estabelecimento);
        $this->db->where_in('status_anterior_descricao', [
            'Em Análise',
            'Pendente de Dúvidas',
            'Perguntas Respondidas (Novas)',
            'Homologado em Revisão',
            'Informações ERP Revisadas'
        ]);
        $this->db->where('data_novo_status IS NOT NULL');
        $this->db->where('data_status_anterior IS NOT NULL');
        $consumido_query = $this->db->get();
        $tempo_consumido_becomex = $consumido_query->num_rows() > 0 ? ($consumido_query->row()->tempo_consumido ?? 0) : 0;

        // Calcular tempo atual no status Becomex (se estiver em um)
        // Usando F_HOUR_DIFF_UTIL que existe no banco
        $this->db->select('F_HOUR_DIFF_UTIL(data_novo_status, NOW(), 8.00) as tempo_atual', FALSE);
        $this->db->from('item_log_status_sla');
        $this->db->where('part_number', $part_number);
        $this->db->where('id_empresa', $id_empresa);
        $this->db->where('estabelecimento', $estabelecimento);
        $this->db->where_in('novo_status_descricao', [
            'Em Análise',
            'Pendente de Dúvidas',
            'Perguntas Respondidas (Novas)',
            'Homologado em Revisão',
            'Informações ERP Revisadas'
        ]);
        $this->db->order_by('id_item_log_status_sla', 'DESC');
        $this->db->limit(1);
        $atual_query = $this->db->get();
        $tempo_ultimo_status_becomex = $atual_query->num_rows() > 0 ? ($atual_query->row()->tempo_atual ?? 0) : 0;

        // Calcular tempo restante
        $tempo_restante = $horas_sla_prioridade - $tempo_consumido_becomex - $tempo_ultimo_status_becomex;

        return [
            'horas_sla_prioridade' => $horas_sla_prioridade,
            'tempo_consumido_becomex' => $tempo_consumido_becomex,
            'tempo_ultimo_status_becomex' => $tempo_ultimo_status_becomex,
            'tempo_restante' => $tempo_restante
        ];
    }
}
