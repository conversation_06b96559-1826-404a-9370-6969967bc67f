<?php

/**
 * Email Queue Model
 *
 * This model handles the email queue operations such as adding emails to the queue,
 * retrieving pending emails, and updating their statuses.
 *
 * @package     Application
 * @subpackage  Models
 * @category    Email Queue
 */
class Email_queue_model extends CI_Model
{
    protected const TABLE_NAME = 'email_queue';
    protected const MAX_ATTEMPTS = 3;
    protected const STATUS_PENDING = 'pending';
    protected const STATUS_PROCESSING = 'processing';
    protected const STATUS_SENT = 'sent';
    protected const STATUS_FAILED = 'failed';

    public $table = self::TABLE_NAME;

    /**
     * Add an email to the queue
     *
     * @param array $payload
     * @return void
     */
    public function add_to_queue($payload)
    {
        if (empty($payload) || !is_array($payload)) {
            throw new InvalidArgumentException('Payload deve ser um array não vazio.');
        }
        
        $data = [
            'payload' => json_encode($payload),
            'status' => self::STATUS_PENDING,
        ];
        $this->db->insert($this->table, $data);
        return $this->db->insert_id();
    }

    /**
     * Retrieve pending emails from the queue
     * Conditions:
     * 1. Status: pending, processing, failed
     * 2. Attempts <= MAX_ATTEMPTS
     *
     * @param int $limit
     * @return array
     */
    public function get_pending_emails($limit = 10)
    {
        $this->db->where_in('status', [
            self::STATUS_PENDING,
            self::STATUS_PROCESSING,
            self::STATUS_FAILED
        ]);
        $this->db->where('attempts <=', self::MAX_ATTEMPTS);
        $this->db->order_by('created_at', 'ASC');
        $this->db->limit($limit);
        $query = $this->db->get($this->table);
        
        return $query->result();
    }

    /**
     * Mark an email as processing
     *
     * @param int $id
     * @return void
     */
    public function mark_as_processing($id)
    {
        if (empty($id)) {
            throw new InvalidArgumentException('ID não pode ser vazio.');
        }
        $this->db->where('id', $id);
        $this->db->update($this->table, ['status' => self::STATUS_PROCESSING]);
    }

    public function mark_as_sent($id)
    {
        if (empty($id)) {
            throw new InvalidArgumentException('ID não pode ser vazio.');
        }
        $this->db->where('id', $id);
        $this->db->update($this->table, [
            'status' => self::STATUS_SENT,
            'processed_at' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Mark an email as failed
     *
     * @param int $id
     * @param string $error_message
     * @return void
     */
    public function mark_as_failed($id, $error_message)
    {
        if (empty($id) || empty($error_message)) {
            throw new InvalidArgumentException('ID e mensagem de erro não podem ser vazios.');
        }
        $this->db->set('attempts', 'attempts+1', false);
        $this->db->where('id', $id);
        $this->db->update($this->table, [
            'status' => self::STATUS_FAILED,
            'error_message' => $error_message
        ]);
    }
}
