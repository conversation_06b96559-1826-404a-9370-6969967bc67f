<?php

class Ctr_pendencias_pergunta_model extends MY_Model {

	public $_table = 'ctr_pendencias_pergunta';

	public function __construct() {
		parent::__construct();
	}

	public function get_entries($limit = NULL, $offset = NULL)
	{
		$query = $this->db->get($this->_table, $limit, $offset);

		return $query->result();
	}

	public function getEntryById($id)
    {   
		$idEmpresa = sess_user_company();
		
        $this->db->where('id', $id);
        $query = $this->db->get($this->_table);
                
		return $query->row();
    }

	public function getEntry($item = null, $id = null)
    {   
		$idEmpresa = sess_user_company();
		
		$this->db->select("pp.*");
        $this->db->where('pp.id', $item->id_pergunta);
        $query = $this->db->get("{$this->_table} pp");
        
        $query =  $query->row();
		if (empty($query))
			return false;
		$pergunta = $query->pergunta;
		$part_number_array = explode(",", $item->part_number);

        $this->db->select("pp.*");
        $this->db->where('pp.id_empresa', $idEmpresa);
        $this->db->where('pp.pergunta', $pergunta);
		$this->db->where('pp.estabelecimento', $item->estabelecimento);
        $this->db->where_in('pp.part_number', $part_number_array);
        $query = $this->db->get("{$this->_table} pp");
        
        return $query->result();
    }

	public function get_questions_to_answer($email, $id_empresa, $limit = NULL)
    {
        $sess_user_id = sess_user_id();

        $this->db->select('q.*, i.*, u.nome as usuario_pergunta');

        $this->db->join('ctr_pergunta_item rel', 'rel.id_ctr_pergunta=q.id_ctr_pergunta','inner');
        $this->db->join('ctr_pendencias_item i', 'rel.id_ctr_item=i.id_ctr_item','inner');
        $this->db->join('usuario u', 'u.id_usuario=q.id_usuario_pergunta','inner');

        $this->db->where("(q.id_empresa = '{$id_empresa}' AND i.usuario_especifico IS NULL OR i.usuario_especifico = '{$email}')", NULL, FALSE);
        $this->db->where('i.item_respondido = 0');

        $this->db->order_by('data_pergunta DESC');

        $query = $this->db->get($this->_table . ' q', $limit);

        return $query->result();
	}

	public function count_questions_owner_to_answer($id_usuario_pergunta)
	{
		$this->db->select('q.*, i.*, u.nome, r.*');

	    $this->db->join('ctr_pergunta_item reli', 'reli.id_ctr_pergunta = q.id_ctr_pergunta','inner');
	    $this->db->join('ctr_pendencias_item i', 'reli.id_ctr_item = i.id_ctr_item','inner');
	    $this->db->join('ctr_resposta_item relr', 'relr.id_ctr_item = i.id_ctr_item','left');
	    $this->db->join('ctr_pendencias_resposta r', 'relr.id_ctr_resposta = r.id_ctr_resposta','left');
	    $this->db->join('usuario u', 'u.id_usuario = q.id_usuario_pergunta','inner');

		$this->db->where('i.item_respondido', 0);
	    $this->db->where('q.id_empresa', $this->get_state('filter.id_empresa'));

	    if (!$this->get_state('get_from_all_owners'))
	    {
	    	$this->db->where('q.id_usuario_pergunta', $id_usuario_pergunta);
	    }

	    $this->db->order_by('data_pergunta DESC');

	    $query = $this->db->get($this->_table . ' q');

	    return $query->num_rows();
	}

	public function count_questions_to_answer($email, $id_empresa)
    {
        $sess_user_id = sess_user_id();

	    $this->db->join('ctr_pergunta_item rel', 'rel.id_ctr_pergunta = q.id_ctr_pergunta','inner');
	    $this->db->join('ctr_pendencias_item i', 'rel.id_ctr_item = i.id_ctr_item','inner');
	    $this->db->join('usuario u', 'u.id_usuario = q.id_usuario_pergunta','inner');

	    $this->db->where("(q.id_empresa = '{$id_empresa}' AND i.usuario_especifico IS NULL OR i.usuario_especifico = '{$email}')", NULL, FALSE);
        $this->db->where('i.item_respondido = 0');

	    $query = $this->db->get($this->_table . ' q');

	    return $query->num_rows();

	}

	public function get_questions_from_owner($id_usuario_pergunta, $limit, $offset)
    {
	    $this->db->select('q.*, i.*, u.nome, r.*');

	    $this->db->join('ctr_pergunta_item reli', 'reli.id_ctr_pergunta = q.id_ctr_pergunta','inner');
	    $this->db->join('ctr_pendencias_item i', 'reli.id_ctr_item = i.id_ctr_item','inner');
	    $this->db->join('ctr_resposta_item relr', 'relr.id_ctr_item = i.id_ctr_item','left');
	    $this->db->join('ctr_pendencias_resposta r', 'relr.id_ctr_resposta = r.id_ctr_resposta','left');
	    $this->db->join('usuario u', 'u.id_usuario = q.id_usuario_pergunta','inner');

	    if ($data_ini = $this->get_state('filter.data_ini'))
	    {
	    	$data_ini = str_replace('/', '-', $data_ini);
	        $this->db->where('q.data_pergunta >= "'. date('Y-m-d H:i:s', strtotime($data_ini)).'"');
	    }

	    if($data_fim = $this->get_state('filter.data_fim'))
        {
        	$data_fim = str_replace('/', '-', $data_fim);
            $this->db->where('q.data_pergunta <= "'. date('Y-m-d 23:59:59', strtotime($data_fim)).'"');
        }/*else
        {
            $this->db->where('q.data_pergunta <= "'. date('Y-m-d 23:59:59', strtotime($data_ini)).'"');
        }*/

	    if ($data_ini = $this->get_state('filter.data_resposta_ini'))
	    {
	    	$data_ini = str_replace('/', '-', $data_ini);
	        $this->db->where('r.data_resposta >= "'. date('Y-m-d H:i:s', strtotime($data_ini)).'"');
	    }

	    if($data_fim = $this->get_state('filter.data_resposta_fim'))
        {
        	$data_fim = str_replace('/', '-', $data_fim);
            $this->db->where('r.data_resposta <= "'. date('Y-m-d 23:59:59', strtotime($data_fim)).'"');
        }/*else
        {
            $this->db->where('r.data_resposta <= "'. date('Y-m-d 23:59:59', strtotime($data_ini)).'"');
        }*/

        switch ($this->get_state('filter.status')) {
            case 'nao_respondidas':
                $this->db->where('i.item_respondido', 0);
                break;
            case 'nao_lidas':
                $this->db->where('r.lida', 0);
                $this->db->where('i.item_respondido', 1);
                break;
            case 'lidas':
                $this->db->where('r.lida', 1);
                $this->db->where('i.item_respondido', 1);
                break;
        }

        if ($part_number = $this->get_state('filter.part_number'))
        {
        	$this->db->like('i.part_number', $part_number);
        }

	    $this->db->where('q.id_empresa', $this->get_state('filter.id_empresa'));

	    if (!$this->get_state('get_from_all_owners'))
	    {
	    	$this->db->where('q.id_usuario_pergunta', $id_usuario_pergunta);
	    }

	    $this->db->order_by('data_pergunta DESC');

	    $query = $this->db->get($this->_table . ' q', $limit, $offset);

	    return $query->result();
	}

	public function count_questions_from_owner($id_usuario_pergunta)
	{

	    $this->db->join('ctr_pergunta_item reli', 'reli.id_ctr_pergunta = q.id_ctr_pergunta','inner');
        $this->db->join('ctr_pendencias_item i', 'reli.id_ctr_item = i.id_ctr_item','inner');
        $this->db->join('ctr_resposta_item relr', 'relr.id_ctr_item = i.id_ctr_item','left');
        $this->db->join('ctr_pendencias_resposta r', 'relr.id_ctr_resposta = r.id_ctr_resposta','left');
        $this->db->join('usuario u', 'u.id_usuario = q.id_usuario_pergunta','inner');

        if ($data_ini = $this->get_state('filter.data_ini'))
        {
            $data_ini = str_replace('/', '-', $data_ini);
            $this->db->where('q.data_pergunta >= "'. date('Y-m-d H:i:s', strtotime($data_ini)).'"');
        }

        if($data_fim = $this->get_state('filter.data_fim'))
        {
            $data_fim = str_replace('/', '-', $data_fim);
            $this->db->where('q.data_pergunta <= "'. date('Y-m-d 23:59:59', strtotime($data_fim)).'"');
        }

        if ($data_ini = $this->get_state('filter.data_resposta_ini'))
        {
            $data_ini = str_replace('/', '-', $data_ini);
            $this->db->where('r.data_resposta >= "'. date('Y-m-d H:i:s', strtotime($data_ini)).'"');
        }

        if($data_fim = $this->get_state('filter.data_resposta_fim'))
        {
            $data_fim = str_replace('/', '-', $data_fim);
            $this->db->where('r.data_resposta <= "'. date('Y-m-d 23:59:59', strtotime($data_fim)).'"');
        }

        switch ($this->get_state('filter.status')) {
            case 'nao_respondidas':
                $this->db->where('i.item_respondido', 0);
                break;
            case 'nao_lidas':
                $this->db->where('r.lida', 0);
                $this->db->where('i.item_respondido', 1);
                break;
            case 'lidas':
                $this->db->where('r.lida', 1);
                $this->db->where('i.item_respondido', 1);
                break;
        }

        if ($part_number = $this->get_state('filter.part_number'))
        {
            $this->db->like('i.part_number', $part_number);
        }

        $this->db->where('q.id_empresa', $this->get_state('filter.id_empresa'));

        if (!$this->get_state('get_from_all_owners'))
        {
            $this->db->where('q.id_usuario_pergunta', $id_usuario_pergunta);
        }

        $this->db->order_by('data_pergunta DESC');

	    $query = $this->db->get($this->_table . ' q');

	    return $query->num_rows();

	}

	public function get_all_questions($email, $id_empresa)
    {
	    $this->db->select('q.*, i.*, u.nome as usuario_pergunta');

	    $this->db->join('ctr_pergunta_item rel', 'rel.id_ctr_pergunta = q.id_ctr_pergunta','inner');
	    $this->db->join('ctr_pendencias_item i', 'rel.id_ctr_item = i.id_ctr_item','inner');
	    $this->db->join('usuario u', 'u.id_usuario = q.id_usuario_pergunta','inner');

	    $this->db->where('q.id_empresa', $id_empresa);
	    $this->db->or_where('i.usuario_especifico', $email);

	    $this->db->order_by('data_pergunta DESC');

	    $query = $this->db->get($this->_table . ' q');

	    return $query->result();
	}

	public function get_itens_from_question($id_ctr_pergunta){

	    $this->db->select('q.*, i.*');

	    $this->db->join('ctr_pergunta_item rel', 'rel.id_ctr_pergunta = q.id_ctr_pergunta','inner');
        $this->db->join('ctr_pendencias_item i', 'rel.id_ctr_item = i.id_ctr_item','inner');

        $this->db->where('q.id_ctr_pergunta', $id_ctr_pergunta);

        $query = $this->db->get($this->_table . ' q');

        return $query->result();
	}

	public function get_question_detail($id_ctr_pergunta, $id_ctr_item)
	{

        $this->db->select('q.*, i.*, u.nome, r.*');

	    $this->db->join('ctr_pergunta_item reli', 'reli.id_ctr_pergunta = q.id_ctr_pergunta','inner');
	    $this->db->join('ctr_pendencias_item i', 'reli.id_ctr_item = i.id_ctr_item','inner');
	    $this->db->join('ctr_resposta_item relr', 'relr.id_ctr_item = i.id_ctr_item','left');
	    $this->db->join('ctr_pendencias_resposta r', 'relr.id_ctr_resposta = r.id_ctr_resposta','left');
	    $this->db->join('usuario u', 'u.id_usuario = q.id_usuario_pergunta','inner');

	    $this->db->where('q.id_ctr_pergunta', $id_ctr_pergunta);
	    $this->db->where('i.id_ctr_item', $id_ctr_item);

	    $query = $this->db->get($this->_table . ' q');

	    return $query->row();

	}

	//a partir daqui é quando teve a alteração para o novo controle de pendências
	public function salvarPendenciasPerguntas($partnumbers, $perguntas, $responsaveis, $owners, $tipoResponsavel, $grupoPerguntas)
	{
		if (empty($partnumbers) || empty($perguntas)) {
			return false;
		}
		
		$this->load->model(array(
            "cad_item_model",
			"empresa_model"
        ));

		$date = date('Y-m-d H:i:s');
		$idEmpresa = sess_user_company();
		$usuarioPergunta = sess_user_id();
		$hasErrors = false;
		$errors = [];
		$empresa = $this->empresa_model->get_entry(sess_user_company());
		$campos_adicionais = explode("|", $empresa->campos_adicionais);


		foreach($partnumbers as $partnumber) {

			foreach($perguntas as $pergunta) {
				if (
					!$this->checkPerguntaJaExiste(
						$pergunta['pergunta'], 
						$partnumber['part_number'], 
						$partnumber['estabelecimento'],
						$pergunta['id']
					)
				){	
					$res = $this->db->insert($this->_table, array(
						'estabelecimento' => $partnumber['estabelecimento'],
						'part_number' => $partnumber['part_number'],
						'pergunta' => $pergunta['pergunta'],
						'pendente' => 1,
						'status' => 1,
						'criado_em' => $date,
						'atualizado_em' => $date,
						'id_pergunta' => isset($pergunta['id']) ? $pergunta['id'] : NULL,
						'id_ctr_grupo' => isset($grupoPerguntas['key']) ? $grupoPerguntas['key'] : NULL,
						'id_usuario_pergunta' => $usuarioPergunta,
						'id_empresa' => $idEmpresa,
						'inf_partnumber' => isset($pergunta['campo']) && !empty($pergunta['campo']) ? 1 : 0,
						'campo_partnumber' => isset($pergunta['campo']) && !empty($pergunta['campo']) ? $pergunta['campo'] : '',
					));
					$id_ctr_pendencias = $this->db->insert_id();

					if ($tipoResponsavel === 'user' && $responsaveis !== NULL) {

						foreach ($responsaveis as $usuarioResponsavel)
						{

							if (is_array($usuarioResponsavel))
							{
							
								$id_responsavel = isset($usuarioResponsavel['key']) && !empty($usuarioResponsavel['key']) && $usuarioResponsavel['key'] > 0 ? $usuarioResponsavel['key'] : (isset($usuarioResponsavel['id_usuario']) ? $usuarioResponsavel['id_usuario'] : $usuarioResponsavel['item']['id_usuario']) ;

								
									$this->db->insert('rel_perguntas_responsavel', array(
									'id_ctr_pendencias' => $id_ctr_pendencias,
									'id_responsavel' => $id_responsavel
								));
							}
						}
					}

					if ($tipoResponsavel === 'owner' && $owners !== NULL) {
						// foreach ($owners as $owner) {
							// if (is_array($owner)) {
								

								$codigo_owner = isset($owners['key']) && !empty($owners['key']) && $owners['key'] > 0 ? $owners['codigo'] : (isset($owners['codigo']) ? $owners['codigo'] : $owners['item']['codigo']) ;

								// var_dump($codigo_owner); exit;

								$this->db->insert('rel_perguntas_responsavel', array(
									'id_ctr_pendencias' => $id_ctr_pendencias,
									'cod_owner' => $codigo_owner
								));
							// } 
						// }
					}

					$has_cad_item = "";

					try {
						$has_cad_item = $this->cad_item_model->get_entry_by_pn($partnumber['part_number'], $idEmpresa, $partnumber['estabelecimento']);
					} catch(Exception $e) {
						$has_cad_item = false;
					}
					
					$item_atual = $this->item_model->get_entry($partnumber['part_number'], $idEmpresa, $partnumber['estabelecimento']);

					if (!$has_cad_item) {
						$this->load->library("Item/Status");

						// Verificar se o item já possui alguma pergunta
						$has_perguntas = $this->getTotalPerguntasPorItem(
							$partnumber['part_number'], 
							$partnumber['estabelecimento'], 
							$idEmpresa
						);

						// Verificar se o status atual não é 'pendente_duvidas' antes de atualizar
						if ($item_atual->status_formatado != 'Pendente de Informações'  ) {
							if (in_array('owner', $campos_adicionais) &&
									 ($item_atual->status_formatado == 'Perguntas Respondidas' ||
									 mb_strtolower($item_atual->status_formatado) == 'informações erp revisadas' ||
									 mb_strtolower($item_atual->status_formatado) == 'perguntas respondidas (novas)'
									 )
								) {

								if ($has_perguntas > 0) {
									$this->status->set_status("revisar_informacoes_tecnicas");
									$this->status->update_item($partnumber['part_number'], $partnumber['estabelecimento']);
									
								} else {
									$this->status->set_status("pendente_duvidas");
									$this->status->update_item($partnumber['part_number'], $partnumber['estabelecimento']);
								}

							} else if(mb_strtolower($item_atual->status_formatado) != 'revisar informações técnicas') {

								$this->status->set_status("pendente_duvidas");
								$this->status->update_item($partnumber['part_number'], $partnumber['estabelecimento']);

							}
						}						
					}
				} else {
					$errors[] = "Pergunta não foi realizada ({$pergunta['pergunta']}), porque já existe uma sem resposta para o <strong>PARTNUMBER {$partnumber['part_number']}</strong>";
				}
			}
		}

		return (object) array(
			'status' => true,
			'errors' => $errors
		);
	}


	public function getTotalPendentes($itens,$status = null,$usuarios = null)
	{
		foreach ($itens as $k => $item)
		{
		    $this->db->select('DISTINCT(pp.id)');
			$this->db->where('pp.part_number', $item->part_number);
			$this->db->where('pp.id_empresa', $item->id_empresa);
			$this->db->join('rel_perguntas_responsavel rel', 'rel.id_ctr_pendencias = pp.id', 'inner');
			if (empty($item->estabelecimento))
			{
				$this->db->where(" (pp.estabelecimento  IS NULL OR pp.estabelecimento = '') ",null,false);
			} else {
				$this->db->where('pp.estabelecimento', $item->estabelecimento);
			}

			if (!empty($status))
			{
				$this->db->where_in('pp.pendente', $status);
			}

			if (!empty($usuarios))
			{
				$this->db->where_in('rel.id_responsavel', $usuarios);
			}

			$query = $this->db->get($this->_table . ' pp');
			$total = $query->num_rows();

			$itens[$k]->total = $total;
		}

		return $itens;
	}

	public function checkPerguntaJaExiste($pergunta, $partnumber, $estabelecimento = null, $id_pergunta = null)
	{
		$this->db->where('id_empresa', sess_user_company());

		if (!empty($estabelecimento)) {
			$this->db->where('estabelecimento', $estabelecimento);
		}

		if (!empty($id_pergunta)) {
			// $this->db->where('id_pergunta', $id_pergunta);
		}

		$this->db->where('part_number', $partnumber);
		$this->db->where('pergunta', $pergunta);
		$this->db->where('pendente', 1);
		$query = $this->db->get($this->_table);

		return $query->num_rows();
	}

	public function gerarEmailPendenciasPerguntas($partnumbers, $usuarioResponsavel)
	{
		$temp_data = array(
			'base_url' => config_item('online_url'),
			'html_message' => $this->load->view(
				'templates/notificacao_pendencias_perguntas', 
				array(
					'base_url' => config_item('online_url'),
					'partnumbers' => $partnumbers,
					'usuarioResponsavel' => $usuarioResponsavel
				), TRUE
			)
		);

		$body = $this->load->view('templates/basic_template', $temp_data, TRUE);

		if (!isset($usuarioResponsavel['email']))
			return;

		if (empty($usuarioResponsavel['email']))
			return;

		$this->load->library('email');

		$this->email->from(config_item('mail_from_addr'), config_item('mail_from_name'));
		$this->email->to($usuarioResponsavel['email']);
		$this->email->subject('[Gestão Tarifária] - Nova pendência cadastrada');
		$this->email->message($body);

		return $this->email->send();
	}

	private function filterPerguntas($apenas_busca = FALSE, $desabilita_filtro_pendencias = FALSE)
	{
		if ($apenas_busca == FALSE)
		{
			if ($this->get_state('filter.partnumbers')) {
				$this->db->where_in(
					'pp.part_number', 
					is_array($this->get_state('filter.partnumbers')) ? 
						$this->get_state('filter.partnumbers') : 
						array($this->get_state('filter.partnumbers'))
				);
			}
		}

		// if ($this->get_state('filter.usuarioPergunta')) {
		// 	$this->db->where_in('pp.id_usuario_pergunta', $this->get_state('filter.usuarioPergunta'));
		// }

		if ($periodoIni = $this->get_state('filter.periodoIni')) {
			$periodoIni = str_replace('/', '-', $periodoIni);
			$this->db->where('pp.atualizado_em >=', date('Y-m-d 00:00:00', strtotime($periodoIni)));
		}

		if ($periodoFim = $this->get_state('filter.periodoFim')) {
			$periodoFim = str_replace('/', '-', $periodoFim);
			$this->db->where('pp.atualizado_em <=', date('Y-m-d 23:59:59', strtotime($periodoFim)));
		}

		if ($desabilita_filtro_pendencias == false)
		{



		if ($this->get_state('filter.status')) {
			$this->db->where_in('pp.pendente', $this->get_state('filter.status'));
		} else {
			if ($this->get_state('filter.pendente')) {
				$this->db->where_in('pp.pendente', $this->get_state('filter.pendente'));
			}
		}
		}
		$adwhereplus = "";
		if (($like_search = $this->get_state('filter.partnumber_like')) && !$this->get_state('filter.partnumbers')) {
			$where_in = '';
		
			if (is_array($like_search)){
				$like_search = $like_search[0];
			}

			if (strpos($like_search, '*') !== false) {

				if(substr_count($like_search, '*') == 1) {

					if (substr($like_search, 0, 1) === '*' ){
						$like_search = str_replace("*", "", $like_search);
						$where_in  .= ' (i.descricao LIKE "%' . $like_search. '" OR pp.part_number LIKE "%' . $like_search . '")';
					}else if(substr($like_search, -1) === '*'){
						$like_search = str_replace("*", "", $like_search);
						$where_in  .= ' (i.descricao LIKE "' . $like_search. '%" OR pp.part_number LIKE "' . $like_search . '%")';

					}else{
						$like_search = str_replace("*", "%", $like_search);
						$where_in  .= ' (i.descricao LIKE "' . $like_search. '" OR pp.part_number LIKE "' . $like_search . '")';
					}
				}
				if (substr($like_search, 0, 1) === '*' && substr($like_search, -1) === '*' && substr_count($like_search, '*') <= 2) {
					$like_search = str_replace("*", "", $like_search);
					$where_in  .= ' (i.descricao LIKE "%' . $like_search. '%" OR pp.part_number LIKE "%' . $like_search. '%")';
				}else{
					if (substr_count($like_search, '*') >= 2) {
						
						$adwhereplus = "";

						$like_search = str_replace("*", "%", $like_search);

						if ($like_search !== '') {
							$adwhereplus .= 'i.descricao LIKE "' . $like_search. '" OR pp.part_number LIKE "' . $like_search. '" OR ';
						}
	
						$adwhereplus = rtrim($adwhereplus, 'OR ');
	
						$where_in .= ' (' . $adwhereplus . ')';
					}
				}
				if ($where_in != ' ()')            
				{
					$this->db->where($where_in, null, false);
				}
	
			}else{
				$this->db->where("( pp.part_number LIKE '%{$like_search}%'  ) ",null,false);
			}

		}

	}

	public function getPerguntasRealizadas($limit = NULL, $offset = NULL, $total = false)
	{
		// $hasRole = customer_has_role('ver_todo_historico', sess_user_id());

		// if (!$hasRole) {
		// 	$this->db->where('id_usuario_pergunta', sess_user_id());
        //     // $this->db->where('cpp.id_usuario_resposta', sess_user_id());
        //     // $this->db->or_where('cpp.id_usuario_pergunta', sess_user_id());
        // }

		$this->db->where('pp.id_empresa', sess_user_company());

		$this->filterPerguntas(TRUE);

		$this->db->select('
			pp.id, pp.pergunta, pp.part_number, pp.pendente, 
			pp.status, max(pp.atualizado_em) as atualizado_em, 
			pp.id_empresa, pp.id_usuario_pergunta, u.nome,
			pp.criado_em, max(pp.criado_em) as criado_em, count(pp.part_number) as total_perguntas,
			sum(IF(pp.pendente = 1, 1, 0)) as total, i.descricao
		', false);

		$this->db->join('usuario u', 'pp.id_usuario_pergunta = u.id_usuario', 'inner');
		$this->db->join('item i', 'i.part_number = pp.part_number AND i.id_empresa = pp.id_empresa AND i.estabelecimento = pp.estabelecimento', 'inner');

		$this->db->group_by('pp.part_number');

		if ($total) {
			$query = $this->db->get($this->_table . ' pp');
			return $query->num_rows();
		}
		
		$this->db->order_by('max(pp.atualizado_em)', 'desc');
		
		$query = $this->db->get($this->_table . ' pp', $limit, $offset);

		return $query->result();
	}

	public function TotaisGetPartnumbersComPerguntasPendentes($limit = NULL, $offset = NULL, $total = false, $search_part_number = null,$remover_busca_estabelecimento = false, $apenas_usuario_logado = false, 
	$owner_user = NULL)
	{ 
		$this->load->model("empresa_model");
		$empresa = $this->empresa_model->get_entry(sess_user_company());
        $data['campos_adicionais'] = explode("|", $empresa->campos_adicionais);
        $hasOwner =  in_array('owner', $data['campos_adicionais']);
		$this->ctr_pendencias_pergunta_model->set_state_store_session(TRUE);
		$user_id = sess_user_id();
		$company_id = sess_user_company();		
		$hasJoinRel = false;

			if ($hasOwner)
			{
				$cnpj_raiz_logado = $this->empresa_model->get_entry_cnpj_raiz($company_id);

				$this->db->join('rel_perguntas_responsavel rel', 'rel.id_ctr_pendencias = pp.id', 'inner');
				$hasJoinRel	= true;
				$this->db->join('owner o', "o.codigo = rel.cod_owner AND (o.cnpj_raiz = '{$cnpj_raiz_logado}' OR o.cnpj_raiz IS NULL) ", 'left');
				$this->db->join('owner_usuario ou', 'ou.id_owner = o.id_owner', 'left');
			}

			if ($hasOwner)
			{ 
				$this->db->where("(rel.id_responsavel = '{$user_id}' OR ou.id_usuario = '{$user_id}')", null, false);
			} else {
				$this->db->where("(rel.id_responsavel = '{$user_id}')", null, false);
			}
			
			$this->db->where('pp.id_empresa', $company_id);
	 
		if(!$hasJoinRel){
			if ($hasOwner)
			{
				$this->db->join('rel_perguntas_responsavel rel', 'rel.id_ctr_pendencias = pp.id', 'inner', false);
			} else {
				$this->db->join('rel_perguntas_responsavel rel ', 'rel.id_ctr_pendencias = pp.id', 'inner', false);
			}
		}
		
		$this->db->select('DISTINCT pp.id,
			(SELECT count(id) FROM ctr_pendencias_pergunta ctrpp WHERE ctrpp.part_number = pp.part_number
			AND ctrpp.estabelecimento = pp.estabelecimento AND ctrpp.id_empresa = pp.id_empresa AND
			ctrpp.pendente = 1) as total,
		', false);


		$this->db->join('usuario u', 'pp.id_usuario_pergunta = u.id_usuario', 'inner');
		$this->db->join('item i', "i.part_number = pp.part_number AND i.id_empresa = pp.id_empresa AND i.estabelecimento = pp.estabelecimento", 'inner');

		// $this->db->join('empresa_prioridades ep', 'ep.id_prioridade = i.id_prioridade', 'left');
        
		if ($hasOwner)
		{
			if ($owner_user) {
				if (is_array($owner_user)) {
					$owner_user_condition = "i.cod_owner IN ('" . implode("', '", $owner_user) . "')";
				} else {
					$owner_user_condition = "i.cod_owner = '{$owner_user}'";
				}
				$this->db->where("({$owner_user_condition} OR i.criado_por = '{$user_id}') ", null, false);
			}
		}

		// if ($filterContagemLogado = $this->get_state('filter.usuarioContagemLogado')) {
		// 	$this->db->where('rel.id_responsavel', $filterContagemLogado);
		// }

		$this->db->group_by('pp.part_number, pp.estabelecimento');

		if (  isset($filter_status) && is_array($filter_status) && count($filter_status) == 1 && in_array('0',$filter_status))
		{
			$this->db->having(" total = 0", NULL, FALSE);
		}
		$this->db->where('pp.pendente', 1);
		if ($total) {
			$query = $this->db->get($this->_table . ' pp');
			return $query->num_rows();
		}
		
		$query = $this->db->get($this->_table . ' pp', $limit, $offset);
		return $query->result();
	}

	public function getPartnumbersComPerguntasPendentes(
						$limit = null,
						$offset = null,
						$total = false,
						$search_part_number = null,
						$remover_busca_estabelecimento = false,
						$apenas_usuario_logado = false,
						$owner_user = null,
						$itens_selected = null
						)
	{
		$this->load->model("empresa_model");
		$empresa = $this->empresa_model->get_entry(sess_user_company());
        $data['campos_adicionais'] = explode("|", $empresa->campos_adicionais);
        $hasOwner =  in_array('owner', $data['campos_adicionais']);
		$hasPrioridade =  in_array('prioridade', $data['campos_adicionais']);

		$this->ctr_pendencias_pergunta_model->set_state_store_session(TRUE);
		$separator = get_company_separator(null);
		$user_id = sess_user_id();
		$company_id = sess_user_company();
		$buscaPorLike = false;
		$visualizar_transferir_perguntas = customer_has_role('visualizar_transferir_perguntas', $user_id);
		
		$hasJoinRel = false;
		if ($hasOwner)
		{
			$cnpj_raiz_logado = $this->empresa_model->get_entry_cnpj_raiz($company_id);

			$this->db->join('rel_perguntas_responsavel rel ', 'rel.id_ctr_pendencias = pp.id', 'inner');
			$hasJoinRel	= true;
			$this->db->join('owner o', "o.codigo = rel.cod_owner AND (o.cnpj_raiz = '{$cnpj_raiz_logado}' OR o.cnpj_raiz IS NULL) ", 'left');
			$this->db->join('owner_usuario ou', 'ou.id_owner = o.id_owner', 'left');
		}

		if ($apenas_usuario_logado == false)
		{
			if ($filterUsuarioPergunta = $this->get_state('filter.usuarioPergunta')) {

				if ($visualizar_transferir_perguntas == false)
				{
					if ($hasOwner)
					{
						$this->db->where("(rel.id_responsavel = '{$user_id}' OR ou.id_usuario = '{$user_id}')", null, false);
					}  else {
						$this->db->where('rel.id_responsavel', $user_id);
					}

				} else {
					if (!$this->get_state('filter.ownerPergunta'))
						$this->db->where_in('rel.id_responsavel', $filterUsuarioPergunta);
				} 
			} 
			
			if (!$this->get_state('filter.usuarioPergunta') && !$this->get_state('filter.ownerPergunta')) 
			{
				if ($hasOwner)
				{
					$this->db->where("(rel.id_responsavel = '{$user_id}' OR ou.id_usuario = '{$user_id}')", null, false);
				} else {
					$this->db->where('rel.id_responsavel', $user_id);
				}
			}
	
			if ($hasOwner)
			{
				if ($ownerPergunta = $this->get_state('filter.ownerPergunta')) {
					$this->db->where_in('rel.cod_owner', $ownerPergunta);
				}
			}

			if($hasPrioridade)
            {
                if ($prioridades = $this->get_state('filter.prioridade')) {
                $this->db->where_in('i.id_prioridade', $prioridades);
				}
            }

			$this->db->where('pp.id_empresa', $company_id);
			$listPn = '';
			$where_in = '';
			$adwhereplus = '';

			if (($partnumbers = $this->input->get('partnumbers')) || (!empty($itens_selected))) {

				if(is_array($partnumbers)){
					$partnumbers = implode(" ", $partnumbers);
				}
				
				if (!empty($partnumbers)) $this->ctr_pendencias_pergunta_model->set_state('filter.partnumbers', $partnumbers);
				
				$partnumbers = $this->ctr_pendencias_pergunta_model->get_state('filter.partnumbers');
				
				
				if(!empty($itens_selected)){
					$partnumbers = $itens_selected;
				}

				if (strpos($partnumbers, " ") !== false) {
					//$partnumbers = str_replace(array("\t", "\r\n", "\s", "\n"), '#', $partnumbers);
					if (!empty($separator)   && count(explode($separator, $partnumbers)) > 1)
					{
						$partnumbers = str_replace(" ", "#", $partnumbers);
					}  

					$keywords = explode('#', $partnumbers);

					$adwhere_go = "";

					foreach ($keywords as $index => $keyword) {
						if ($index === 0 && strpos($keyword, ' ') === 0) {
							$keyword = str_replace(' ', '%', $keyword);
						}

						$keyword = str_replace("*", "%", $keyword);

						$keyword = rtrim($keyword, " ");

						if ($keyword !== '') {
							$adwhere_go .= 'i.part_number LIKE "%' . $keyword  . '" OR ';
							$adwhere_go .= 'pp.part_number LIKE "%' . $keyword  . '" OR ';
							//$adwhere_go .= 'i.part_number_similar LIKE "' . $keyword  . '" OR ';
							//$adwhere_go .= 'i.ncm LIKE "' . $keyword  . '" OR ';
							//$adwhere_go .= 'i.estabelecimento LIKE "' . $keyword  . '" OR ';
							$adwhere_go .= 'i.descricao LIKE "%' . $keyword  . '" OR ';
							$adwhere_go .= 'i.descricao_global LIKE "%' . $keyword  . '" OR ';
						}

					}

					$adwhere_go = rtrim($adwhere_go, 'OR ');
							
					$where_in .= ' (' .$adwhere_go.')';

					    if ($where_in != ' ()')            
                        {
                            $this->db->where($where_in, null, false);
                        }

				}else{
					if (strpos($partnumbers, '*') !== false) {

						if(substr_count($partnumbers, '*') == 1) {

							if (substr($partnumbers, 0, 1) === '*' ){
								$partnumbers = str_replace("*", "", $partnumbers);
								$where_in  .= ' (i.part_number LIKE "' . $partnumbers. '" OR i.descricao LIKE "%' . $partnumbers. '" OR i.descricao_global LIKE "%' . $partnumbers. '"  OR pp.part_number LIKE "%' . $partnumbers . '")';
							}else if(substr($partnumbers, -1) === '*'){
								$partnumbers = str_replace("*", "", $partnumbers);
								$where_in  .= ' (i.part_number LIKE "' . $partnumbers. '" OR i.descricao LIKE "' . $partnumbers. '%" OR i.descricao_global LIKE "' . $partnumbers. '%"  OR pp.part_number LIKE "' . $partnumbers . '%")';
		
							}else{
								$partnumbers = str_replace("*", "%", $partnumbers);
								$where_in  .= ' (i.part_number LIKE "' . $partnumbers. '" OR i.descricao LIKE "' . $partnumbers. '" OR i.descricao_global LIKE "' . $partnumbers. '"  OR pp.part_number LIKE "' . $partnumbers . '")';
							}
						}
						
						if (substr($partnumbers, 0, 1) === '*' && substr($partnumbers, -1) === '*' && substr_count($partnumbers, '*') <= 2) {
							$partnumbers = str_replace("*", "", $partnumbers);
							$where_in  .= ' (i.descricao LIKE "%' . $partnumbers. '%" OR i.descricao_global LIKE "%' . $partnumbers. '%" OR pp.part_number LIKE "%' . $partnumbers . '%")';
						}else{
							if (substr_count($partnumbers, '*') >= 2) {


								$adwhereplus = "";
			
								$partnumbers = str_replace("*", "%", $partnumbers);

								if ($partnumbers !== '') {
									$adwhereplus = 'i.descricao LIKE "' . $partnumbers. '" OR i.descricao_global LIKE "' . $partnumbers. '" OR pp.part_number LIKE "' . $partnumbers . '" OR ';
								}
			
								$adwhereplus = rtrim($adwhereplus, 'OR ');
			
								$where_in .= ' (' . $adwhereplus . ')';
							}
						}
						
						if ($where_in != ' ()')            
                        {
                            $this->db->where($where_in, null, false);
                        }
			
					}else{

						if (strpos($partnumbers, '*') !== false) {
							$partnumbers = str_replace("*", "%", $partnumbers);
						} else{
							$partnumbers = '%'.$partnumbers;
						}
						$adwhere_go = "";
						$adwhere_go .= 'i.part_number LIKE "' . $partnumbers . '" OR ';
						$adwhere_go .= 'pp.part_number LIKE "' . $partnumbers  . '" OR ';
						$adwhere_go .= 'i.descricao LIKE "' . $partnumbers . '" OR ';
						$adwhere_go .= 'i.descricao_global LIKE "' . $partnumbers  . '" OR ';
					
						$adwhere_go = rtrim($adwhere_go, 'OR ');
								
						$where_in .= ' (' .$adwhere_go.')';
						
						if ($where_in != ' ()')            
                        {
                            $this->db->where($where_in, null, false);
                        }
					}
					
				}
			}

			if ($periodoIni = $this->get_state('filter.periodoIni')) {
				$periodoIni = str_replace('/', '-', $periodoIni);
				$this->db->where('pp.atualizado_em >=', date('Y-m-d 00:00:00', strtotime($periodoIni)));
			}

			if ($periodoFim = $this->get_state('filter.periodoFim')) {
				$periodoFim = str_replace('/', '-', $periodoFim);
				$this->db->where('pp.atualizado_em <=', date('Y-m-d 23:59:59', strtotime($periodoFim)));
			}

			if ($filter_status = $this->get_state('filter.status')) {
				$this->db->where_in('pp.pendente', $this->get_state('filter.status'));
			} else {
				$this->db->where('pp.pendente', 1);
			}
		} else {

			if ($hasOwner)
			{
				$cnpj_raiz_logado = $this->empresa_model->get_entry_cnpj_raiz($company_id);

			//	$this->db->join('rel_perguntas_responsavel rel FORCE INDEX (idx_cod_owner)', 'rel.id_ctr_pendencias = pp.id', 'inner');
				$hasJoinRel	= true;
				//$this->db->join('owner o', "o.codigo = rel.cod_owner AND (o.cnpj_raiz = '{$cnpj_raiz_logado}' OR o.cnpj_raiz IS NULL) ", 'left');
				//$this->db->join('owner_usuario ou', 'ou.id_owner = o.id_owner', 'left');
			}
			
			// $this->db->where("(rel.id_responsavel = '{$user_id}' OR rel.id_responsavel IS NULL)", null,false);
			// $this->db->or_where("ou.id_usuario", $user_id);

			if ($hasOwner)
			{ 
				$this->db->where("(rel.id_responsavel = '{$user_id}' OR ou.id_usuario = '{$user_id}')", null, false);
			} else {
				$this->db->where("(rel.id_responsavel = '{$user_id}')", null, false);
			}
			
			$this->db->where('pp.id_empresa', $company_id);
		}
		
		if(!$hasJoinRel){
			$this->db->join('rel_perguntas_responsavel rel ', 'rel.id_ctr_pendencias = pp.id', 'inner', false);
		}
		
		$this->db->select('
			DISTINCT pp.id,
			pp.pergunta, pp.part_number, pp.estabelecimento, pp.pendente,
			pp.status, max(pp.atualizado_em) as atualizado_em, pp.id_empresa, pp.id_usuario_pergunta,
			u.nome, i.evento, i.descricao, i.descricao_global, i.peso, i.inf_adicionais, ep.nome as empresa_prioridade,
			pp.criado_em, max(pp.criado_em) as criado_em,
			(SELECT count(id) FROM ctr_pendencias_pergunta ctrpp WHERE ctrpp.part_number = pp.part_number
			AND ctrpp.estabelecimento = pp.estabelecimento AND ctrpp.id_empresa = pp.id_empresa AND ctrpp.pendente = 1) as total,
			',
			false
		);

		$this->db->join('usuario u', 'pp.id_usuario_pergunta = u.id_usuario', 'inner');

		// if ($owner_user) {
		// 	$this->db->join('item i', "i.part_number = pp.part_number AND i.id_empresa = pp.id_empresa AND i.estabelecimento = pp.estabelecimento AND (i.cod_owner = '{$owner_user}' OR i.criado_por = '{$user_id}') ", 'inner');

		// } else {
		// 	$this->db->join('item i', "i.part_number = pp.part_number AND i.id_empresa = pp.id_empresa AND i.estabelecimento = pp.estabelecimento", 'inner');
		// }

		$this->db->join('item i', "i.part_number = pp.part_number AND i.id_empresa = pp.id_empresa AND i.estabelecimento = pp.estabelecimento", 'inner');
		
        $this->db->join('empresa_prioridades ep', 'ep.id_prioridade = i.id_prioridade', 'left');

		if ($hasOwner)
		{
			if ($owner_user) {
				if (is_array($owner_user)) {
					$owner_user_condition = "i.cod_owner IN ('" . implode("', '", $owner_user) . "')";
				} else {
					$owner_user_condition = "i.cod_owner = '{$owner_user}'";
				}

				$this->db->where("({$owner_user_condition} OR i.criado_por = '{$user_id}') ", null, false);
			
			}

			$this->db->where("(o.cnpj_raiz = '{$cnpj_raiz_logado}' OR o.cnpj_raiz IS NULL)", null, false);
		}
		
		$this->db->group_by('pp.part_number, pp.estabelecimento');

		if (  isset($filter_status) && is_array($filter_status) && count($filter_status) == 1 && in_array('0',$filter_status))
		{
			$this->db->having(" total = 0", NULL, FALSE);
		}

		if ($total) {
			$query = $this->db->get($this->_table . ' pp');
			return $query->num_rows();
		}
		
		$query = $this->db->get($this->_table . ' pp', $limit, $offset);
		return $query->result();
	}

	public function getBulk($getCount = false)
    {
        $exist_bulk = $this->ctr_pendencias_pergunta_model->get_state("filter.bulk_item") ? true : false;

		if ($exist_bulk)
        {
            $item_bulk = $this->ctr_pendencias_pergunta_model->get_state("filter.bulk_item");

			if ($getCount)
			{
				return count($item_bulk);
			}

			return array(
				"item" => $item_bulk
			);
       }
		return false;
	}

	public function getPerguntas($item)
    {
		$this->db->select("pergunta, rel.id_responsavel, u.nome as nome_responsavel, u.email as email_responsavel");
		$this->db->join('rel_perguntas_responsavel rel', 'rel.id_ctr_pendencias = pp.id', 'left');
		$this->db->join('usuario u', 'rel.id_responsavel = u.id_usuario', 'left');
		$this->db->where('pp.id_empresa', $item['id_empresa']);
		$this->db->where('pp.part_number', $item['part_number']);
		$this->db->where('pp.estabelecimento', $item['estabelecimento']);
		$this->db->where('pendente',1);

		$query = $this->db->get($this->_table . ' pp');

		return $query->result();
    }

	public function responderPerguntasLote($part_number, $estabelecimento, $pergunta, $resposta)
    {
		$this->load->model('ctr_resposta_model');
		if (empty($part_number) || empty($pergunta) || empty($resposta))
			return;

		$estabelecimento = $estabelecimento == 'N/A' ? null : $estabelecimento;
		$id_empresa = sess_user_company();
		$id_usuario = sess_user_id();

		$this->db->where(['id_empresa' => $id_empresa]);
	    
		if (empty($estabelecimento))
		{
			$this->db->where("(estabelecimento IS NULL or estabelecimento = '')",null,false);
		} else {
			$this->db->where('estabelecimento',$estabelecimento);
		}
	    
		// Nova abordagem para tratar a pergunta
		$pergunta_tratada = $this->tratarPerguntaParaBusca($pergunta);
    
		$this->db->where("part_number LIKE '%{$part_number}%'",null,false);
		$this->db->where("pergunta LIKE '%{$pergunta_tratada}%'", null, false);
		$this->db->where('pendente',1);
		
		$query = $this->db->get('ctr_pendencias_pergunta');
		$db_item = $query->row();

		if (empty($db_item))
			return;
			
		$id_pergunta = !empty($db_item->id) ? $db_item->id : 0;

		$this->ctr_resposta_model->save(array(
			"resposta"        => empty($resposta) ? null : $resposta,
			"part_number"     => $db_item->part_number,
			"id_usuario"      => $id_usuario,
			"id_empresa"      => $id_empresa,
			"estabelecimento" => $db_item->estabelecimento,
			"id_pergunta"     => $id_pergunta
		));

		$this->db->where('id',$id_pergunta);
		$this->db->where('part_number',$db_item->part_number);
		$this->db->where('id_empresa',$id_empresa);
		if (empty($db_item->estabelecimento))
		{
			$this->db->where("(estabelecimento IS NULL or estabelecimento = '')",null,false);
		} else {
			$this->db->where('estabelecimento',$db_item->estabelecimento);
		}

		$this->db->update($this->_table, array('pendente' => 0));
 
		return true;
    }

	private function tratarPerguntaParaBusca($pergunta) {
		// Lista de caracteres especiais comuns que podem causar problemas
		$caracteresEspeciais = array(
			'^', '~', '°', 'º', '²', '³', '±', '×', '÷',
			'∑', '∏', '√', '∫', '∞', '≠', '≤', '≥'
		);
		
		// Encontra a posição do primeiro caractere especial
		$posicao = strlen($pergunta);
		foreach ($caracteresEspeciais as $char) {
			$pos = strpos($pergunta, $char);
			if ($pos !== false && $pos < $posicao) {
				$posicao = $pos;
			}
		}
		
		// Se encontrou caractere especial, reduz a string até antes dele
		if ($posicao < strlen($pergunta)) {
			// Pega todo o texto desde o início até o caractere especial
			return substr($pergunta, 0, $posicao);
		}
		
		// Se não encontrou caracteres especiais, retorna a pergunta original
		return addslashes($pergunta);
	}

	public function getPartnumbersComPerguntasPendentesFilter()
	{
		$this->db->where('pp.id_empresa', sess_user_company());
		$this->db->where('rel.id_responsavel', sess_user_id());
	    // $this->db->where('pp.pendente', 1);
		
		$this->db->select("
			pp.id, pp.pergunta, pp.part_number, pp.estabelecimento, pp.pendente,
			pp.status, max(pp.atualizado_em) as atualizado_em, 
			pp.id_empresa, pp.id_usuario_pergunta, ep.nome as empresa_prioridade,
			pp.criado_em, max(pp.criado_em) as criado_em, count(pp.part_number) as total,
			GROUP_CONCAT(rel.id_responsavel ) as id_usuario_resposta
		");

        $this->db->join('rel_perguntas_responsavel rel', 'rel.id_ctr_pendencias = pp.id', 'inner');


		$this->db->join('item i', "i.part_number = pp.part_number AND i.id_empresa = pp.id_empresa AND i.estabelecimento = pp.estabelecimento", 'inner');

		
        $this->db->join('empresa_prioridades ep', 'ep.id_prioridade = i.id_prioridade', 'left');
        

		$this->db->group_by('pp.part_number, pp.estabelecimento');
	
		$query = $this->db->get($this->_table . ' pp');

		return $query->result();
	}

	public function getUsuariosComPerguntasRealizadas()
	{
		$this->db->where('pp.id_empresa', sess_user_company());
		$this->db->where('rel.id_responsavel', sess_user_id());

		$this->db->select('distinct(u.id_usuario), u.nome');

		$this->db->join('usuario u', 'pp.id_usuario_pergunta = u.id_usuario', 'inner');
        $this->db->join('rel_perguntas_responsavel rel', 'rel.id_ctr_pendencias = pp.id', 'inner');
		$query = $this->db->get($this->_table . ' pp');

		return $query->result();
	}

	public function getUsuariosResponsaveisComPerguntasRealizadas()
	{
		$user_id =  sess_user_id();
		$empresa_id =  sess_user_company();

		if (!customer_has_role('visualizar_transferir_perguntas', $user_id))
		{
			$this->db->where('rel.id_responsavel', $user_id);
		}
		$this->db->where("(pp.pendente = '1' AND pp.id_empresa = '{$empresa_id}' ) OR (u.id_usuario = '{$user_id}' )", null,false);

		$this->db->select('distinct(u.id_usuario), u.nome');

        $this->db->join('rel_perguntas_responsavel rel', 'rel.id_ctr_pendencias = pp.id', 'inner');
		$this->db->join('usuario u', 'rel.id_responsavel = u.id_usuario', 'inner');
		$query = $this->db->get($this->_table . ' pp');

		return $query->result();
	}

	public function getPerguntasPendentesAgrupadasPn($limit = NULL, $offset = NULL, $total = false, $regra_somente_pendentes = null)
	{
		$id_usuario = sess_user_id();
		$id_empresa = sess_user_company();

		// if (!customer_has_role('visualizar_transferir_perguntas', $id_usuario))
		// {
		// 	$this->db->where('rel.id_responsavel', $id_usuario);
		// }
		$this->db->where('pp.id_empresa', $id_empresa);

		if ($regra_somente_pendentes == true)
		{
			$this->db->where('pp.pendente', 1);
		}
		// $this->filterPerguntas();
		$partnumbers = $this->get_state("filter.partnumbers");
		$estabelecimento = $this->get_state("filter.estabelecimento");
		$i = 0;

		if (!empty($partnumbers) )
		{
			if (is_array($partnumbers))
			{
				$i = 0;
				foreach ($partnumbers as  $k => $part_number)
				{						
					$list = explode('&', $part_number);

					$listPn    = $list[0];
					$listEstab = isset($list[1]) ? $list[1] : '';
					$listEstab = empty($listEstab) && isset($estabelecimento[$k]) ? $estabelecimento[$k] : $listEstab;

					// if (empty($listEstab))
					// {
					// 	print_r(['listPn' => $listPn, 'listEstab' => $listEstab, 'list0' => $list]);

					// 	exit;
					// }
					// if (isset($estabelecimento) && is_array($estabelecimento) )
					// {
					// 	$listEstab = isset($estabelecimento) && !empty($estabelecimento) && is_array($estabelecimento) ? reset($estabelecimento) : '';
					// }
					$estab = isset($listEstab) && !empty($listEstab) ?   " pp.estabelecimento = '{$listEstab}' "  : ' pp.estabelecimento  IS NULL OR pp.estabelecimento = ""';
			
					$i++;
					if ($i == 1)
					{
						$this->db->where("(( pp.part_number = '{$listPn}' AND  ({$estab}) ) ",null,false);
					} else {
						$this->db->or_where("( pp.part_number = '{$listPn}' AND  ({$estab}) ) ",null,false);
					}
					if ($i == count($partnumbers))
					{
						$this->db->or_where("( pp.part_number = '{$listPn}' AND  ({$estab}) ) ) ",null,false);
					}
				}
			} else {
				if (is_array($estabelecimento))
					$estabelecimento = reset($estabelecimento);

					$estab = isset($estabelecimento) && !empty($estabelecimento) ?   " pp.estabelecimento = '{$estabelecimento}' "  : ' pp.estabelecimento  IS NULL OR pp.estabelecimento = ""';
					$this->db->where("(pp.part_number = '{$partnumbers}' AND  ({$estab}) )",null,false);
			}
		}

		$this->db->select(" pp.id,
			pp.pergunta, pp.part_number, pp.pendente, pp.estabelecimento,
			pp.status, max(pp.atualizado_em) as atualizado_em, pp.id_pergunta,
			pp.id_empresa, pp.id_usuario_pergunta,
			pp.inf_partnumber, pp.campo_partnumber, ip.campo, ip.obrigatorio,
			pp.criado_em, max(pp.criado_em) as criado_em, count(pp.part_number) as total,
			GROUP_CONCAT(distinct pp.part_number separator ',') AS part_numbers,
			GROUP_CONCAT(distinct pp.id separator ',') AS ids
		", false);

		$this->db->join('info_partnumber ip', 'ip.campo = pp.campo_partnumber', 'left');
		// $this->db->join('rel_perguntas_responsavel rel', 'rel.id_ctr_pendencias = pp.id', 'inner');
		// if ($this->get_state('filter.usuarioPergunta')) {
		// 	$this->db->where_in('rel.id_responsavel', $this->get_state('filter.usuarioPergunta'));
		// }
		if ($this->input->get('estabelecimento'))
		{
			$this->db->group_by('pp.pergunta,pp.estabelecimento');
		} else {
			$this->db->group_by('pp.pergunta');
		}

		if ($total) {
			$query = $this->db->get($this->_table . ' pp');
			return $query->num_rows();
		}
		
		$query = $this->db->get($this->_table . ' pp', $limit, $offset);

		return $query->result();
	}


	public function getPerguntasPendentesAgrupadasPnUnico($limit = NULL, $offset = NULL, $total = false)
	{
		$id_usuario = sess_user_id();
		$id_empresa = sess_user_company();	
		if (!customer_has_role('visualizar_transferir_perguntas', $id_usuario))
		{
			$this->db->where('rel.id_responsavel', $id_usuario);
		}
		$this->db->where('pp.id_empresa', $id_empresa);
		$partnumbers = $this->get_state("filter.partnumbers");
		$estabelecimento = $this->get_state("filter.estabelecimento");
		$i = 0;
		if (!empty($partnumbers) )
		{
			if (is_array($partnumbers))
			{
				foreach ($partnumbers as $k => $part_number)
				{
					$i++;
					$estab = empty($estabelecimento[$k]) ? 'IS NULL OR pp.estabelecimento = ""' : '= "'.$estabelecimento[$k].'"';
					if ($i > 1)
					{
						$this->db->or_where("(pp.part_number = '{$part_number}' AND ( pp.estabelecimento {$estab} ))",null,false);
					}else{
						$this->db->where("(pp.part_number = '{$part_number}' AND ( pp.estabelecimento {$estab} ))",null,false);
					}
					
				}
			} else {
				if (is_array($estabelecimento))
					$estabelecimento = reset($estabelecimento);

				$estab = empty($estabelecimento) ? 'IS NULL OR pp.estabelecimento = ""' : '= "'.$estabelecimento.'"';
				$this->db->where("(pp.part_number = '{$partnumbers}' AND ( pp.estabelecimento {$estab} ))",null,false);
			}
		}

		$this->db->select("
			DISTINCT pp.id, pp.pergunta, pp.part_number, pp.pendente, pp.estabelecimento,
			pp.status, pp.atualizado_em, pp.id_pergunta,
			pp.id_empresa, pp.id_usuario_pergunta, 
			pp.inf_partnumber, pp.campo_partnumber, ip.campo, ip.obrigatorio,
			pp.criado_em, pp.criado_em, pp.part_number AS part_numbers,
			pp.id AS ids ", false);

		$this->db->join('info_partnumber ip', 'ip.campo = pp.campo_partnumber', 'left');
		$this->db->join('rel_perguntas_responsavel rel', 'rel.id_ctr_pendencias = pp.id', 'inner');


		if ($this->get_state('filter.usuarioPergunta')) {
			$this->db->where_in('rel.id_responsavel', $this->get_state('filter.usuarioPergunta'));
		}

		if ($this->get_state('filter.ownerPergunta')) {
			$this->db->where_in('rel.cod_owner', $this->get_state('filter.ownerPergunta'));
		}

		$status  = !empty($this->get_state('filter.status'))  ? $this->get_state('filter.status') : array('1');  
		$this->db->where_in('pp.pendente', $status);
		if ($total) {
			$query = $this->db->get($this->_table . ' pp');
			return $query->num_rows();
		}
		$this->db->group_by('pp.pergunta, rel.id_responsavel');
		$query = $this->db->get($this->_table . ' pp', $limit, $offset);

		return $query->result();
	}

	public function transferirResponsavel($responsaveis, $partnumber, $estabelecimento, $ownerResponsavel, $tipoResponsavel) 
	{
		$id_empresa = sess_user_company();
		$this->load->model(array(
			"item_model",
			"usuario_model"
		));

		$this->db->where('id_empresa', $id_empresa);
		$this->db->where('part_number', $partnumber);

		if (empty($estabelecimento))
		{
			$this->db->where("(estabelecimento is null OR estabelecimento = '')",null,false);
		} else {
			$this->db->where('estabelecimento', $estabelecimento);
		}

		$perguntasPendentes = $this->db->get($this->_table)->result();

		if (isset($responsaveis) && !empty($responsaveis))
		{
			foreach ($perguntasPendentes as $p)
			{
				$this->db->delete('rel_perguntas_responsavel', array('id_ctr_pendencias' => $p->id ));
				foreach ($responsaveis as $id_responsavel)
				{
					$this->db->insert('rel_perguntas_responsavel', array(
						'id_ctr_pendencias' => $p->id,
						'id_responsavel' => $id_responsavel
					));
	
				}
			}
		}

		if ($tipoResponsavel === 'owner' && isset($ownerResponsavel) && !empty($ownerResponsavel))
		{
			foreach ($perguntasPendentes as $p)
			{
				$this->db->delete('rel_perguntas_responsavel', array('id_ctr_pendencias' => $p->id ));
					
				$this->db->insert('rel_perguntas_responsavel', array(
						'id_ctr_pendencias' => $p->id,
						'cod_owner' => $ownerResponsavel,
					));
				
			}
		}
	
		$this->item_model->set_state('filter.id_empresa', $id_empresa);
		$this->item_model->set_state('filter.search', $partnumber);

		$partnumbers = $this->item_model->get_entries_pendente_atribuicao(1);
		$partnumbersArray = array();

		foreach($partnumbers as $partnumber) {
			$partnumbersArray[] = (array) $partnumber;
		}


		foreach ($responsaveis as $id_responsavel)
		{
			$usuarioResponsavel = (array) $this->usuario_model->get_entry($id_responsavel);
			$this->gerarEmailPendenciasPerguntas($partnumbersArray, $usuarioResponsavel);
		}
		
		return true;
	}

	private function defaultPendenciaWhere($pergunta) {
		$this->db->where('pergunta', $pergunta->pergunta);
		$this->db->where('part_number', $pergunta->part_number);
		$this->db->where('estabelecimento', $pergunta->estabelecimento);
		$this->db->where('id_empresa', $pergunta->id_empresa);
	}

	public function preparar_notificacao($item)
	{
		$this->db->where('part_number', $item['part_number']);

		if (empty($item['estabelecimento']))
		{
			$this->db->where("(estabelecimento = '' OR estabelecimento is null )", null, false);
		} else {
			$this->db->where('estabelecimento', $item['estabelecimento']);
		}
		$this->db->where('id_empresa', $item['id_empresa']);
		$this->db->where('id', $item['id_resposta']);

		$query = $this->db->get($this->_table);

	    $result = $query->row();
		
		if ($result->pendente == 0)
		{
			$data = array(
				"notificar" => 'notificar'
			);
			$this->db->where('id', $result->id);
			$this->db->update($this->_table, $data);
		}

		return true;
	}

	public function atualizarPendencia($pergunta, $idPergunta = null)
	{
		$pergunta = (object) $pergunta;
		
		$this->load->model(array(
            "cad_item_model"
        ));
		
		if (empty($idPergunta)) {
			$this->defaultPendenciaWhere($pergunta);
		} else {
			$this->db->where("id", $idPergunta);
		}
		
		if ($this->db->update($this->_table, array('pendente' => 0))) {
			if (empty($idPergunta)) {
				$this->defaultPendenciaWhere($pergunta);
			} else {
				$this->db->where("id", $idPergunta);
			}

			$query = $this->db->get($this->_table);

			$row = $query->row();

			$has_cad_item = "";

			try {
				$has_cad_item = $this->cad_item_model->get_entry_by_pn($row->part_number, $row->id_empresa, $row->estabelecimento);
			} catch(Exception $e) {
				$has_cad_item = false;
			}
			// var_dump($row->part_number, $row->id_empresa, $row->estabelecimento); die();
			$this->load->model(array(
				"item_model",
				"empresa_model",
			));

			
			if (!$has_cad_item) {
				$this->load->library("Item/Status");
				$item = $this->item_model->get_entry($row->part_number, $row->id_empresa, $row->estabelecimento);

				$status_anterior = $item->id_status;
				// var_dump($status_anterior); die();
				$this->status->check_status_item_perguntas($row->part_number, $row->estabelecimento, $status_anterior);
				// var_dump($this->status->check_status_item_perguntas($item->part_number, $item->estabelecimento, $status_anterior)); die();

				// $empresa = $this->empresa_model->get_entry(sess_user_company());
				// $campos_adicionais = explode("|", $empresa->campos_adicionais);
				// $hasDescricaoGlobal = in_array("descricao_global", $campos_adicionais) ? true : false;

				// var_dump($hasDescricaoGlobal); die();
				// $respostas_pendentes = $this->status->get_perguntas_pendentes(TRUE, $row->part_number, $row->estabelecimento, $row->id_empresa);

        		// $perguntas_do_item   = $this->status->get_perguntas_pendentes(FALSE, $row->part_number, $row->estabelecimento, $row->id_empresa);


				// if ($hasDescricaoGlobal) {

				// 	$item = $this->item_model->get_entry($row->part_number, $row->id_empresa, $row->estabelecimento);

				// 	if(($item->descricao === '' || $item->descricao === null) && $respostas_pendentes->perguntas == 0 && $perguntas_do_item->perguntas > 0) {

				// 		$this->status->set_status("aguardando_descricao");
				// 		$this->status->update_item($row->part_number, $row->estabelecimento, $row->id_empresa);
				// 	} 

				// } else {
					
				// 	$this->status->set_status("respondido");
				// 	$this->status->update_item($row->part_number, $row->estabelecimento, $row->id_empresa);
				// }

			}

			return $row;
		}

		return false;
	}

	public function getTotalPendencias()
	{
		$id_usuario = sess_user_id();
		$id_empresa = sess_user_company();

		$this->db->where('pp.id_empresa', $id_empresa);
		$this->db->where('rel.id_responsavel', $id_usuario);

		$this->filterPerguntas();

		$this->db->select('sum(IF(pp.pendente = 1, 1, 0)) as total', false);
		$this->db->join('rel_perguntas_responsavel rel', 'rel.id_ctr_pendencias = pp.id', 'inner');
		$query = $this->db->get($this->_table . ' pp');
		$row = $query->row();
		
		return $row->total;
	}

	public function getHistoricoPerguntas($search_part_number_post = false, $desabilita_filtro_pendencias = false)
	{
		// if (!customer_has_role('ver_todo_historico', sess_user_id())) {
		// 	$this->db->where('id_usuario_pergunta', sess_user_id());
		// }
		$id_empresa = sess_user_company();
		$post = $this->input->post();
        $partnumber = isset($post['dataItem']) ? $post['dataItem'] : '';

		$this->db->where('pp.id_empresa', $id_empresa);

		if (isset($partnumber['part_number']))
		{
			if (isset($partnumber['estabelecimento']))
			{
				if (!empty($partnumber['estabelecimento']))
				{
					$this->db->where('pp.estabelecimento', $partnumber['estabelecimento']);
				} else {
					$this->db->where("(pp.estabelecimento IS NULL OR pp.estabelecimento  = '')", null,false);
				}
			}
	
			if (isset($partnumber['part_number']) && !empty($partnumber['part_number']))
			{
				$this->db->where_in('pp.part_number', $partnumber['part_number']);
			}
		} else {
			if (!empty($partnumber))
			{
				$this->db->where_in('pp.part_number', $partnumber);
			}	
		}

		$this->filterPerguntas($search_part_number_post,$desabilita_filtro_pendencias);

		$this->db->select(' u.nome as usu_resposta, pp.part_number, pp.estabelecimento, cr.id as id_resposta,
			pp.id, pp.pergunta, pp.part_number, pp.pendente, pp.status, pp.atualizado_em, pp.criado_em,
			pp.id_empresa, pp.id_usuario_pergunta, u2.nome as nome, ow.descricao as owner, ow.codigo as cod_owner,
			cr.resposta, cr.criado_em as respondida_em, CONCAT(rel.id_responsavel ) as id_usuario_resposta 
			', false);
			
		$this->db->join('rel_perguntas_responsavel rel', 'rel.id_ctr_pendencias = pp.id', 'left');
		$this->db->join('ctr_resposta cr', 'cr.id_pergunta = pp.id', 'left');
		$this->db->join('usuario u', 'cr.id_usuario = u.id_usuario', 'left');
		$this->db->join('usuario u2', 'rel.id_responsavel = u2.id_usuario', 'left');
		$this->db->join('owner ow', 'rel.cod_owner = ow.codigo', 'left');



		$this->db->group_by('pp.id');
		$this->db->order_by('pp.criado_em', 'desc');
		
		$query = $this->db->get($this->_table . ' pp');

		return $query->result();
	}

	
    public function get_perguntas_pendentes($part_number, $estabelecimento, $id_empresa)
    {
        $this->CI->db->select("count(*) as perguntas");
        $this->CI->db->join('usuario u', 'pp.id_usuario_pergunta = u.id_usuario', 'inner');
        $this->CI->db->join('item i', 'i.part_number = pp.part_number AND i.id_empresa = pp.id_empresa AND i.estabelecimento = pp.estabelecimento', 'inner');
        $this->CI->db->join('rel_perguntas_responsavel rel', 'rel.id_ctr_pendencias = pp.id', 'inner');                     
        $this->CI->db->where("pp.id_empresa", $id_empresa);
       
    	$this->CI->db->where("pp.pendente", '1');

        if (empty($estabelecimento))
        {
            $this->CI->db->where("(pp.estabelecimento = '' OR pp.estabelecimento is null)",null,false);
        } else {
            $this->CI->db->where("pp.estabelecimento", $estabelecimento);
        }
        
        $this->CI->db->where("pp.part_number", $part_number);
        $query = $this->CI->db->get('ctr_pendencias_pergunta pp');

        return $query->row();
    }

	public function getPerguntasByEmpresa() 
	{
		$id_empresa = sess_user_company();
		$this->db->where('pp.id_empresa', $id_empresa);

		if ($estabelecimento = $this->get_state("filter.estabelecimento")) {
			is_array($estabelecimento) ? 	
				$this->db->where_in("pp.estabelecimento", $estabelecimento) :
				$this->db->where_in("pp.estabelecimento", $estabelecimento);
		}

		$this->db->select("SUM(IF(pp.pendente = 1, 1, 0)) as pendentes, SUM(IF(pp.pendente = 0, 1, 0)) as respondidas, COUNT(pp.id) as total", false);

		$query = $this->db->get($this->_table . ' pp');

		return $query->row();
	}

	public function getPerguntasByPn($partnumber,$estabelecimento = null) 
	{
		$id_empresa = sess_user_company();
		$this->db->where('id_empresa', $id_empresa);

		$this->db->where_in("estabelecimento", $estabelecimento);
		$this->db->where_in("part_number", $partnumber);

		$perguntas = $this->db->get($this->_table );

		$pergunta_pendente = false;
		$possui_perguntas = false;

        if ($perguntas->num_rows() > 0) {
			$possui_perguntas = true;
			foreach ($perguntas->result() as $pergunta)
			{
				if ($pergunta->pendente == 1)
				{
					$pergunta_pendente = true;
				}
			}
		}

		return ['possui_perguntas'  => "$possui_perguntas",
				'pergunta_pendente' => "$pergunta_pendente"];
	}

	public function getOwnersComPerguntasPendentes ()
	{
		$user_id = sess_user_id();
		$id_empresa = sess_user_company();


	}

	private function isJson($string) {
		json_decode($string);
		return json_last_error() === JSON_ERROR_NONE;
	 }

	/**
	 * Verifica se já existem perguntas para o part_number, estabelecimento e empresa informados.
	 * @param string $part_number
	 * @param string $estabelecimento
	 * @param int $id_empresa
	 * @return int Quantidade de perguntas encontradas
	 */
	private function getTotalPerguntasPorItem($part_number, $estabelecimento, $id_empresa)
	{
		$this->db->where('part_number', $part_number);
		$this->db->where('estabelecimento', $estabelecimento);
		$this->db->where('id_empresa', $id_empresa);
		$query = $this->db->get($this->_table);
		return $query->num_rows();
	}
}
