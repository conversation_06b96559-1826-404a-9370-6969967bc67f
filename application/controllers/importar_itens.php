<?php if (!defined('BASEPATH')) exit('No direct script access allowed');

/**
 * Importar itens 
 * @property CI_Loader $load
 * @property MY_Input $input
 * @property CI_Output $output
 * @property CI_DB_query_builder $db
 * @property CI_Session $session
 * @property CI_URI $uri
 * @property CI_Form_validation $form_validation
 * @property CI_Config $config
 * @property CI_Zip $zip
 * @property CI_Email $email
 * @property CI_Upload $upload
 * @property Item_model $item_model
 * @property Cad_item_model $cad_item_model
 * @property Empresa_model $empresa_model
 * @property Owner_model $owner_model
 * @property Empresas_prioridades_model $empresa_prioridades_model
 * @property Ex_tarifario_model $ex_tarifario_model
 * @property Area_model $area_model
 * @property ItemDirect $itemdirect
 * @property Status $status
 * @property Breadcrumbs $breadcrumbs
 * @property Excel $excel
 *
 */
class Importar_itens extends <PERSON><PERSON>_<PERSON>
{
    public $title = "Importar Itens";

    public function __construct()
    {
        parent::__construct();

        $this->load->library('breadcrumbs');

        if (!is_logged()) {
            redirect('/login');
        }

        if (!customer_can("importar_itens", false, false)) {
            show_permission();
        }
    }

    public function index()
    {
        $this->load->library("Item/Status");
        $this->load->model('cad_item_model');
        $this->load->helper('formatador_helper');
        $data = array();

        $empresa = $this->empresa_model->get_entry(sess_user_company());
       
        $campos_adicionais = explode("|", $empresa->campos_adicionais);

        $hasDescricaoGlobal = in_array("descricao_global", $campos_adicionais);
        $data['hasDescricaoGlobal'] = $hasDescricaoGlobal;

        $hasPnPrimarioSecundario = in_array('pn_primario_secundario', $campos_adicionais);
        $data['hasPnPrimarioSecundario'] = $hasPnPrimarioSecundario;

        if ($this->input->is_set("submit")) {
            $upload_path = config_item('upload_tmp_path');

            $this->load->model(array(
                'empresa_model', 'item/item_direct_model',
                'item_model', 'area_model','empresa_prioridades_model'
            ));


            $config['upload_path']   = $upload_path;
            $config['allowed_types'] = 'txt';

            $this->load->library('upload', $config);

            if (!$this->upload->do_upload('arquivo')) {
                $errors = array("error" => $this->upload->display_errors());

                $data['errors'] = $errors;
            } else {
                $file_data = $this->upload->data();
                $full_path = $file_data['full_path'];
                $file_lines = file($full_path);

                $itens = array();
                $feedback = array();

                $idEmpresa = sess_user_company();

                $empresa_prioridades = $this->empresa_prioridades_model->get_entry($idEmpresa);

                $empresaLogged = $this->empresa_model->get_entry($idEmpresa);

                $empresaCnpj = $empresaLogged->cnpj;

                // $areas = $this->area_model->getEntries();
                // $areasIndexed = array();

                // foreach ($areas as $value) {
                //     $areasIndexed[$value->area] = $value->prioridade;
                // }

                $feedback['atualizados'] = 0;
                $feedback['interrompidos'] = 0;
                $feedback['inseridos'] = 0;
                foreach ($file_lines as $line) {
                    $item = array_map('trim', explode('|', $line));

                    $peso = str_replace('+', '', $item[15]);
                    $peso = str_replace('.', '', $peso);
                    $peso = str_replace(',', '.', $peso);

                    $info_adicional = implode(";", array_filter(array(
                        "area:=>{$item[3]}",
                        "chave:=>{$item[4]}",
                        "centro_custo:=>{$item[9]}",
                        "almoxarifado:=>{$item[10]}",
                        "cama:=>{$item[11]}",
                        "mofo:=>{$item[12]}",
                        "divcode:=>{$item[13]}",
                        "coiord:=>{$item[14]}",
                        "und_peso:=>{$item[16]}"
                    )));

                    $inf_adicionais = "Especificações Técnicas: ";

                    $inf_adicionais .= implode(";", array(
                        $item[5],
                        $item[6],
                        $item[7],
                        $item[8]
                    ));

                    // $tag = !empty($item[3]) ? $areasIndexed[$item[3]] : null;
                    $prioridade = !empty($item[3]) ? $item[3] : null;
                    // $areas = $this->area_model->getEntries();
                    // $id_prioridade = null;
                    // if(!empty($prioridade)){

                    //     //Verifica se combina com a prioridade da tabela área
                    //     foreach ($areas as $v) {
                    //         if(strtoupper(remove_acentos($prioridade)) == strtoupper(remove_acentos($v->area))){
                    //             $prioridade = $v->prioridade;
                    //         }
                    //     }


                    //     foreach($empresa_prioridades as $p){
                    //         if(strtoupper(remove_acentos($prioridade)) == strtoupper(remove_acentos($p->nome))){
                    //             $id_prioridade = $p->id_prioridade;
                    //         }
                    //         //Combinação assiociativa adicional
                    //         if(strtoupper(remove_acentos($prioridade)) == 'SUPER CRITICO' &&  strtoupper(remove_acentos($p->nome)) == 'SUPERCRITICO'){
                    //             $id_prioridade = $p->id_prioridade;
                    //         }

                    //     } 
                    // }else{
                    //     foreach($empresa_prioridades as $p){
                    //         if('NORMAL' == strtoupper(remove_acentos($p->nome))){
                    //             $id_prioridade = $p->id_prioridade;
                    //         }
                    //     }
                    // }


                    $areas = $this->area_model->getEntries();

                    $id_prioridade = null;
                    if(isset($prioridade) && $prioridade != ''){

                        //Verifica se combina com a prioridade da tabela área
                        foreach ($areas as $v) {
                            if(strtoupper(remove_acentos($prioridade)) == strtoupper(remove_acentos($v->area))){
                                $prioridade = $v->prioridade;
                            }
                        }


                        foreach($empresa_prioridades as $p){
                            if(strtoupper(remove_acentos($prioridade)) == strtoupper(remove_acentos($p->nome))){
                                $id_prioridade = $p->id_prioridade;
                            }
                            //Combinação assiociativa adicional
                            if(strtoupper(remove_acentos($prioridade)) == 'SUPER CRITICO' &&  strtoupper(remove_acentos($p->nome)) == 'SUPERCRITICO'){
                                $id_prioridade = $p->id_prioridade;
                            }

                        } 
                    }else{
                        foreach($empresa_prioridades as $p){
                            if('NORMAL' == strtoupper(remove_acentos($p->nome))){
                                $id_prioridade = $p->id_prioridade;
                            }
                        }
                    }

                    $day = substr($item[2], 0, 2);
                    $month = substr($item[2], 2, 2);
                    $year = substr($item[2], 4, 4);

                    $evento = "{$year}-{$month}-{$day}";
                    $dat_criacao = date("Y-m-d H:i:s");

                    $itemEspecificado = array(
                        "part_number"         => rtrim($item[21]),
                        //"pn_primario_mpn"     => isset($item[21]) && !empty($item[21]) ? rtrim($item[21]) : '',
                        "pn_secundario_ipn"   => isset($item[0]) && !empty($item[0]) ? rtrim($item[0]) : '',
                        "descricao"           => $item[1],
                        "id_empresa"          => $idEmpresa,
                        "estabelecimento"     => !empty($item[13]) ? $item[13] : $empresaLogged->estabelecimento_default,
                        // "tag"                 => $tag,
                        "status"              => null,
                        "prioridade"          => null,
                        //ESPECIFICO PARA MERCEDE
                        "id_resp_engenharia"  => 1059,
                        "id_resp_fiscal"      => 1060,
                        //ESPECIFICO PARA MERCEDE
                        "dat_criacao"         => $dat_criacao,
                        "evento"              => $evento,
                        "info_adicional"      => $info_adicional,
                        "inf_adicionais"      => $inf_adicionais,
                        "ncm"                 => $item[17],
                        "peso"                => $peso,
                        "maquina"             => isset($item[18]) && !empty($item[18]) ? $item[18] : '',
                        "origem"              => isset($item[19]) && !empty($item[19]) ? $item[19] : '',
                        "descricao_global"    => isset($item[20]) && !empty($item[20]) ? $item[20] : '',
                        "id_prioridade"       => $id_prioridade,
                        "sistema_origem"        => 'MANUAL',
                    );

                    if (!empty($itemEspecificado['part_number'])) {
                        if(!empty($itemEspecificado['id_prioridade'])){
                            try {
                                $entry = $this->item_model->get_entry($itemEspecificado['part_number'], $idEmpresa, $itemEspecificado['estabelecimento'], true);
                                $item_homologado = false;
                                
                                if (!empty($entry)) {

                                    $tipo = array('retornar_erro' => false, 'atualizar' => true);
                                // $existe_cad_item = $this->cad_item_model->check_item_exists($itemEspecificado['part_number'], $idEmpresa, $itemEspecificado['estabelecimento']);
                                    $this->item_model->update_item($itemEspecificado['part_number'], $idEmpresa, $itemEspecificado, false, $itemEspecificado['estabelecimento']);
                                    $item_homologado = $entry->id_status == 2 ? true : false;
                                    // if ($item_homologado == false)
                                    // {
                                    //         $this->status->set_status("em_analise");
                                    //         $this->status->update_item($itemEspecificado['part_number'],  $itemEspecificado['estabelecimento']);
                                    // }
                                    
                                    $feedback['atualizados'] += 1;
                                } else {
                                    $tipo = array('retornar_erro' => false, 'atualizar' => false);
                                    $id = $this->item_model->save($itemEspecificado);
                                    // $this->status->set_status("em_analise");
                                    // $this->status->update_item($itemEspecificado['part_number'],  $itemEspecificado['estabelecimento']);
                                    $feedback['inseridos'] += 1;
                                }

                                $this->load->library('Item/ItemDirect');
                                
                                $result = $this->itemdirect->isDirectItem($itemEspecificado, $empresaCnpj,$tipo);
        
                                if ($result == true && $item_homologado == false)
                                {
                                    $this->status->set_status("homologar");
                                    $this->status->update_item($itemEspecificado['part_number'],  $itemEspecificado['estabelecimento']);
                                }
                                
                            } catch (Exception $e) {
                                $feedback['interrompidos'] += 1;

                                $feedback['errors'][] = $e->getMessage();
                            }
                        } else {
                            $feedback['interrompidos'] += 1;
                            $feedback['errors'][] = 'Part number [<b>'.$itemEspecificado['part_number'].'</b>][<b>'.$itemEspecificado['estabelecimento'].'</b>] - Definiçao de prioridade: [<b>'.$prioridade.'</b>], Inválida!';
                        }
                    }
                }

                $feedback['nomeArquivo'] = $file_data['file_name'];
                $feedback['processados'] = count($file_lines);

                $data['feedback'] = (object) $feedback;

                unlink($upload_path . $file_data['file_name']);
            }
        }

        $this->title = "Importar Itens";
        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Importar Itens', '/importar_itens/');

        $this->include_js(array(
            'bootstrap-select/bootstrap-select.js',
        ));

        $this->include_css(array(
            'bootstrap-select/bootstrap-select.css'
        ));

        $this->render('importar_itens', $data);
    }

    public function exportar()
    {
        error_reporting(0);
        set_time_limit(0);
        ini_set('memory_limit', '2048M');
        
        $this->load->model(array('item_model', "cad_item_model", "ex_tarifario_model", 'empresa_model'));

        $customer_can_ex_ipi = customer_can('ipi');

        //Status
        $status = $this->input->post('status') ? $this->input->post('status') : null;   
        if (!empty($status)) {
            $this->item_model->set_state('filter.status', $status);
        }else{
            $this->item_model->unset_state('filter.status');
        }

        $data['lista_status'] = $this->item_model->get_status();

        //Pacote/Evento
        $evento = $this->input->post('evento') ? $this->input->post('evento') : NULL;
        if (!empty($evento)) {
            $this->item_model->set_state('filter.evento', $evento);
        }else{
            $this->item_model->unset_state('filter.evento');
        }

        //Data Inicial Criação
        $data_ini_str = $this->input->post('data_ini') ? $this->input->post('data_ini') :  NULL;
        if (!empty($data_ini_str)) {
            $data_ini = str_replace('/', '-', $data_ini_str);
            $data_ini = date('Y-m-d', strtotime($data_ini));

            $this->item_model->set_state('filter.data_ini', $data_ini);
        }else{
            $this->item_model->unset_state('filter.data_ini');
        }

        //Data Final Criação
        $data_fim_str = $this->input->post('data_fim') ? $this->input->post('data_fim') :  NULL;
        if (!empty($data_fim_str)) {
            $data_fim = str_replace('/', '-', $data_fim_str);
            $data_fim = date('Y-m-d', strtotime($data_fim));

            $this->item_model->set_state('filter.data_fim', $data_fim);
        }else{
            $this->item_model->unset_state('filter.data_fim');
        }

        //Status Exportação
        $statusExportacao = $this->input->post('statusExportacao') ? $this->input->post('statusExportacao') : NULL;
        if (!empty($statusExportacao) && $statusExportacao != '-1') {
            $this->item_model->set_state('filter.statusExportacao', $statusExportacao);
        }else{
            $this->item_model->unset_state('filter.statusExportacao');
        }

        //Owner
        $owner_filter = $this->input->post('owner');
        $owner_filter = is_array($owner_filter) ? $owner_filter : [];
        if (!empty($owner_filter) && !in_array(-1, $owner_filter)) {
            $this->load->model('owner_model');
            $codigo_owner_dbdata = $this->owner_model->get_owner($owner_filter);
            $this->item_model->set_state('filter.owner', $codigo_owner_dbdata->codigo);
        } else {
            $this->item_model->unset_state('filter.owner');
        }

        //Prioridade
        $prioridade_post = $this->input->post('prioridade');
        $empresa_prioridade_filter = is_array($prioridade_post) ? $prioridade_post : [];
        if (!empty($empresa_prioridade_filter) && !in_array(-1, $empresa_prioridade_filter)) {
            $this->item_model->set_state('filter.prioridade', $empresa_prioridade_filter);
        } else {
            $this->item_model->unset_state('filter.prioridade');
        }

        //Pesquisar
        $search = $this->input->post('search') ? $this->input->post('search') : NULL;
        if (!is_null($search)) {
            $this->item_model->set_state("filter.search", $search);
            $data['search'] = $search;
        }else{
            $this->item_model->unset_state("filter.search");
            $data['search'] = NULL;
        }

        //Novo Material
        $novo_material_modal = $this->input->post('novo_material_modal') ? $this->input->post('novo_material_modal') : NULL;
        if(!empty($novo_material_modal) && $novo_material_modal != '-1'){
            $this->item_model->set_state('filter.novo_material_modal', $novo_material_modal);
        }else{
            $this->item_model->unset_state('filter.novo_material_modal');
        }

        //Estabelecimento
        $estabelecimento_modal = $this->input->post('estabelecimento_modal') ? $this->input->post('estabelecimento_modal') : NULL;
        if(is_array($estabelecimento_modal) && !in_array(-1, $estabelecimento_modal)){
            $this->item_model->set_state('filter.estabelecimento_modal', $estabelecimento_modal);
        }else{
            $this->item_model->unset_state('filter.estabelecimento_modal');
        }

        //NCM Proposto
        $ncm_proposta_modal = $this->input->post('ncm_proposta_modal') ? $this->input->post('ncm_proposta_modal') : NULL;
        if(is_array($ncm_proposta_modal) && !in_array(-1, $ncm_proposta_modal)){
            $this->item_model->set_state('filter.ncm_proposta_modal', $ncm_proposta_modal);
        }else{
            $this->item_model->unset_state('filter.ncm_proposta_modal');
        }

        //Sistema de Origem
        $sistema_origem_modal = $this->input->post('sistema_origem_modal') ? $this->input->post('sistema_origem_modal') : NULL;
        if(is_array($sistema_origem_modal) && !in_array(-1, $sistema_origem_modal)){
            $this->item_model->set_state('filter.sistema_origem_modal', $sistema_origem_modal);
        }else{
            $this->item_model->unset_state('filter.sistema_origem_modal');
        }

        //Ex-IPI
        $ex_ipi_modal = $this->input->post('ex_ipi_modal') ? $this->input->post('ex_ipi_modal') : NULL;
        if(is_array($ex_ipi_modal) && !in_array(-1, $ex_ipi_modal)){
            $this->item_model->set_state('filter.ex_ipi_modal', $ex_ipi_modal);
        }else{
            $this->item_model->unset_state('filter.ex_ipi_modal');
        }

        //Ex-II
        $ex_ii_modal = $this->input->post('ex_ii_modal') ? $this->input->post('ex_ii_modal') : NULL;
        if(is_array($ex_ii_modal) && !in_array(-1, $ex_ii_modal)){
            $this->item_model->set_state('filter.ex_ii_modal', $ex_ii_modal);
        }else{
            $this->item_model->unset_state('filter.ex_ii_modal');
        }

        //Descrição Completa
        $descricao_completa_modal = $this->input->post('descricao_completa_modal') ? $this->input->post('descricao_completa_modal') : NULL;
        if(!empty($descricao_completa_modal)){
            $this->item_model->set_state('filter.descricao_completa_modal', $descricao_completa_modal);
        }else{
            $this->item_model->unset_state('filter.descricao_completa_modal');
        }

        //Descrição Global
        $descricao_global_modal = $this->input->post('descricao_global_modal') ? $this->input->post('descricao_global_modal') : NULL;
        if(!empty($descricao_global_modal)){
            $this->item_model->set_state('filter.descricao_global_modal', $descricao_global_modal);
        }else{
            $this->item_model->unset_state('filter.descricao_global_modal');
        }

        //Data Inicial Modificação
        $data_inicio_modificacao_modal = $this->input->post('data_inicio_modificacao_modal') ? $this->input->post('data_inicio_modificacao_modal') : NULL;
        if (!is_null($data_inicio_modificacao_modal)) {
            $data_inicio_modificacao_modal = str_replace('/', '-', $data_inicio_modificacao_modal);
            $data_inicio_modificacao_modal = date('Y-m-d', strtotime($data_inicio_modificacao_modal));

            $this->item_model->set_state('filter.data_inicio_modificacao_modal', $data_inicio_modificacao_modal);
        }else{
            $this->item_model->unset_state('filter.data_inicio_modificacao_modal');
        }

        //Data Final Modificação
        $data_fim_modificacao_modal = $this->input->post('data_fim_modificacao_modal') ? $this->input->post('data_fim_modificacao_modal') : NULL;
        if (!is_null($data_fim_modificacao_modal)) {
            $data_fim_modificacao_modal = str_replace('/', '-', $data_fim_modificacao_modal);
            $data_fim_modificacao_modal = date('Y-m-d', strtotime($data_fim_modificacao_modal));

            $this->item_model->set_state('filter.data_fim_modificacao_modal', $data_fim_modificacao_modal);
        }else{
            $this->item_model->unset_state('filter.data_fim_modificacao_modal');
        }

        //Data Inicial Homologação
        $data_inicio_homologacao_modal = $this->input->post('data_inicio_homologacao_modal') ? $this->input->post('data_inicio_homologacao_modal') : NULL;
        if (!is_null($data_inicio_homologacao_modal)) {
            $data_inicio_homologacao_modal = str_replace('/', '-', $data_inicio_homologacao_modal);
            $data_inicio_homologacao_modal = date('Y-m-d', strtotime($data_inicio_homologacao_modal));
            $this->item_model->set_state('filter.data_inicio_homologacao_modal', $data_inicio_homologacao_modal);
        } else {
            $this->item_model->unset_state('filter.data_inicio_homologacao_modal');
        }

        //Data Final Homologação
        $data_fim_homologacao_modal = $this->input->post('data_fim_homologacao_modal') ? $this->input->post('data_fim_homologacao_modal') : NULL;
        if (!is_null($data_fim_homologacao_modal)) {
            $data_fim_homologacao_modal = str_replace('/', '-', $data_fim_homologacao_modal);
            $data_fim_homologacao_modal = date('Y-m-d', strtotime($data_fim_homologacao_modal));
            $this->item_model->set_state('filter.data_fim_homologacao_modal', $data_fim_homologacao_modal);
        } else {
            $this->item_model->unset_state('filter.data_fim_homologacao_modal');
        }

        //Filtred
        if ($this->input->is_set('filtered')) {
            $this->item_model->set_state('filter.filtered', $this->input->post('filtered'));
        } else {
            $this->item_model->set_state('filter.filtered', 0);
        }

        //Outros
        $this->item_model->set_state('filter.ignore_like', TRUE);
        $empresa = $this->empresa_model->get_entry(sess_user_company());
        $data['campos_adicionais'] = explode("|", $empresa->campos_adicionais);

        $hasPnPrimarioSecundario = in_array('pn_primario_secundario', $data['campos_adicionais']);
        if($hasPnPrimarioSecundario){ 
            (!is_null($search)) ? $this->item_model->set_state("filter.pn_primario_secundario", TRUE): NULL;
        }else{
            $this->item_model->unset_state("filter.pn_primario_secundario");
        }

        $this->item_model->set_state('filter.id_empresa', sess_user_company());

        $itens = $this->item_model->get_entries_export_mestre_itens();
        $empresa = $this->empresa_model->get_entry(sess_user_company());
        $campos_adicionais = explode("|", $empresa->campos_adicionais);

        $this->load->library('Excel');

        $this->excel->setActiveSheetIndex(0);
        $this->excel->getActiveSheet()->setTitle('Mestre de itens');

        //$mercedes = ["MERCEDES-BENZ", "MERCEDES"];
        //$regex_mercedes = "/" . implode("|", array_map('preg_quote', $mercedes)) . "/";
        
        $letras = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'];
        $descricao = ['Part Number', 'Descrição', 'Esp. Técnica 1', 'Esp. Técnica 2', 'Esp. Técnica 3', 'Esp. Técnica 4', 'C.C.', 'MOFO', 'DIVCODE', 'Profit-center', 'Resumo', 'Peso', 'Prioridade', 'Analista Fiscal', 'NCM', 'Item já homologado?', 'Data da Homologação'];
        

        if ($customer_can_ex_ipi) {
            array_push($descricao, 'EX de IPI', 'TEXTO DO EX-IPI');
        }

        if (in_array('maquina', $campos_adicionais)) {
            array_push($descricao, 'Maquina');
        }

        if (in_array('origem', $campos_adicionais)) {
            array_push($descricao, 'Origem');
        }

        if (in_array('evento', $campos_adicionais)) {
            array_push($descricao, 'Evento');
        }

        //Implementaçao do 'PN Primário-MPN' e 'PN Secundário-IPN'  na importação foi descontinuado a pedido do cliente 
        if (in_array('pn_primario_secundario', $campos_adicionais)) {

            //if (preg_match($regex_mercedes, strtoupper($empresa->nome_fantasia))) {
                array_push($descricao, 'PN Secundário-IPN');
            //}
        }
        

        for ($n = 0; $n <= count($descricao) - 1; $n++) {
            $letra =  $letras[$n] . '1';
            $ateLetra = $letras[$n];
            $this->excel->getActiveSheet()->setCellValue($letra, $descricao[$n]);
        }

        $this->excel->getActiveSheet()->getStyle("A1:{$letra}")->getFont()->setBold(true);
        $this->excel->getActiveSheet()->getStyle("A1:{$letra}")->getFill()
            ->setFillType(PHPExcel_Style_Fill::FILL_SOLID)
            ->getStartColor()->setARGB('F9FF00');
        $this->excel->getActiveSheet()->getStyle("A1:{$letra}")->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);

        foreach (range('A', $ateLetra) as $columnID) {
            $this->excel->getActiveSheet()->getColumnDimension($columnID)->setAutoSize(true);
            $this->excel->getActiveSheet()
                ->getStyle($columnID)
                ->getAlignment()
                ->setVertical(PHPExcel_Style_Alignment::VERTICAL_CENTER);
        }

        if (!empty($itens)) {
            $horizontal_left = array(
                'alignment' => array(
                    'horizontal' => PHPExcel_Style_Alignment::HORIZONTAL_LEFT,
                ),
            );

            $i = count($itens) + 1;
            $this->excel->getActiveSheet()->getStyle('A1:A' . $i)->applyFromArray($horizontal_left);
            $key = 0;
       
            while ($item = $itens->unbuffered_row()) {
                $atualizar[] = $item->id_item;

                $i = $key + 2;
                $key++;
                $info_adicional = explode(";", $item->info_adicional);

                $info_adicional_indexed = array();

                foreach ($info_adicional as $k => $value) {
                    $valueExploded = explode(":=>", $value);
                    $info_adicional_indexed[$valueExploded[0]] = isset($valueExploded[1]) ? $valueExploded[1] : '';
                }

                $inf_adicionais_indexed = array();

                $inf_adicionais = $item->inf_adicionais;

                if (strpos($item->inf_adicionais, 'Especificações Técnicas: ') !== false) {
                    $inf = explode('Especificações Técnicas: ', $inf_adicionais);
                    $inf = $inf[1];
                    foreach (explode(';', $inf) as $value) {
                        $inf_adicionais_indexed[] = trim($value);
                    }
                }

                $data_fonte_homologacao = $item->homolog_engenharia_data ?? $item->homolog_fiscal_data ?? '';
                $data_homologacao = !empty($data_fonte_homologacao) ? date("d/m/Y", strtotime($data_fonte_homologacao)) : '';

                $valor = [
                    $item->part_number,
                    $item->descricao,
                    isset($inf_adicionais_indexed[0]) ? $inf_adicionais_indexed[0] : '',
                    isset($inf_adicionais_indexed[1]) ? $inf_adicionais_indexed[1] : '',
                    isset($inf_adicionais_indexed[2]) ? $inf_adicionais_indexed[2] : '',
                    isset($inf_adicionais_indexed[3]) ? $inf_adicionais_indexed[3] : '',
                    isset($info_adicional_indexed["centro_custo"]) ? $info_adicional_indexed["centro_custo"] : "",
                    isset($info_adicional_indexed["mofo"]) ? $info_adicional_indexed["mofo"] : "",
                    isset($info_adicional_indexed["divcode"]) ? $info_adicional_indexed["divcode"] : "",
                    '',
                    $item->descricao_proposta_completa,
                    $item->peso,
                    $item->empresa_prioridade,
                    $item->nome,
                    $item->ncm_proposto,
                    $item->item_ja_homologado ? "Sim" : "Não",
                    $data_homologacao
                ];

                if ($customer_can_ex_ipi) {
                    $item_ex_ipi = false;

                    if (!empty($item->num_ex_ipi)) {
                        $item_ex_ipi = $this->ex_tarifario_model->get_ex_ipi_by_ncm($item->num_ex_ipi, $item->ncm_proposto);
                    }
                    array_push($valor, $item_ex_ipi ? $item_ex_ipi->num_ex : '');
                    array_push($valor, $item_ex_ipi ? $item_ex_ipi->descricao_linha1 : '');
                }
                if (in_array('maquina', $campos_adicionais)) {
                    array_push($valor, $item->maquina);
                }

                if (in_array('origem', $campos_adicionais)) {
                    array_push($valor, $item->origem);
                }

                if (in_array('evento', $campos_adicionais)) {
                    array_push($valor, $item->evento);
                }
           
                if (in_array('pn_primario_secundario', $campos_adicionais)) {
                    //if (preg_match($regex_mercedes, strtoupper($empresa->nome_fantasia))) {
                        array_push($valor, $item->pn_secundario_ipn); 
                    //}
                }

                for ($n = 0; $n <= count($valor) - 1; $n++) {
                    $this->excel->getActiveSheet()->setCellValueExplicit($letras[$n] . $i, $valor[$n], PHPExcel_Cell_DataType::TYPE_STRING);
                }

            }

            $this->cad_item_model->atualiza_status_exportacao($status = 1, $atualizar, sess_user_company());
        }

        $filename = 'itens_' . date('Y-m-d_H-i-s') . '.xlsx';

        header('Content-Type: application/vnd.ms-excel');
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $objWriter = PHPExcel_IOFactory::createWriter($this->excel, 'Excel2007');
        $objWriter->save('php://output');
    }

}
