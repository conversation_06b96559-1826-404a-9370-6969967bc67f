<?php if (!defined('BASEPATH')) exit('No direct script access allowed');

class Upload extends MY_Controller
{

    public function __construct()
    {
        parent::__construct();

        if (!is_logged()) {
            redirect('/login');
        }

        if (!has_role('arquivos_mestre') && !has_role('gerenciar_mestre')) {
            show_permission();
        }

        $this->load->library('breadcrumbs');
        $this->load->model('item_model');
    }

    public function functionShutdown($msg_function)
    {
        $filename =   FCPATH . 'assets/logs/log.txt';
        $file = fopen($filename, "a");

        fwrite($file, $msg_function."\n");
        fclose($file);
    }

    public function index() {
        set_time_limit(900);
        ini_set('memory_limit', '4048M');

        $data = [];
        $this->load->model([
            'cad_item_model',
            'empresa_model',
            'item_model',
            'cest_model',
            'empresa_prioridades_model'
        ]);

        // Inicializar dados do processamento para a view
        $data['processamento'] = null;

        $this->load->helper('formatador_helper');
        
        $data['empresas'] = $this->empresa_model->get_all_entries();

        if ($this->input->post('submit')) {
            $saldo = $this->item_model->get_saldo_mes();
            $itens_processados = 0;
            $inicio = microtime(true);
            $upload_path = config_item('upload_tmp_path');
            $id_empresa = sess_user_company();
            
            $config = [
                'upload_path' => $upload_path,
                'allowed_types' => 'zip|xlsx',
                'max_size' => 2147483648
            ];

            $this->load->library('unzip');
            $this->load->service('UploadService');
            $uploadResult = $this->uploadservice->doUpload($config);

            if (!$uploadResult['success']) {
                $processamento = [
                    'status' => 'falha_upload',
                    'message' => 'Não foi possível realizar o upload do arquivo, planilha fora do padrão compatível!',
                    'details' => $uploadResult['errors']
                ];
                $this->session->set_flashdata('processamento', $processamento);
                redirect('/upload/');
                return;
            }
            
            // Continua o processamento em caso de sucesso (sem o else)
            $upload_data = $uploadResult['data'];
            $file_ext = strtolower($upload_data['file_ext']);
            $empresa = $this->empresa_model->get_entry($id_empresa);

            // Processar arquivo XLSX
            if ($file_ext == '.xlsx') {
                $this->load->service([
                    'SpreadsheetService',
                    'ItemService',
                    'LogService',
                    'ReportService'
                ]);

                // Recuperar mapeamento de colunas para processamento da planilha
                $columnMapping = $this->getColumnMapping();

                $logs = [
                    'inseridos' => [],
                    'atualizados' => [],
                    'nao_atualizados' => [],
                    'com_erro' => [],
                    'fotos_atualizadas' => []
                ];

                // Processa a planilha XLSX carregada
                $spreadsheetResult = $this->spreadsheetservice
                    ->processSpreadsheet(
                        $upload_data['full_path'],
                        $columnMapping
                    );

                if (!empty($spreadsheetResult['errors'])) {
                    // Adiciona os erros encontrados ao log de erros
                    $logs['com_erro'] = array_merge(
                        $logs['com_erro'],
                        ['planilha_errors' => $spreadsheetResult['errors']]
                    );
                }

                // Processa os dados da planilha e atualiza os logs
                $logs = $this->_processSpreadsheetData(
                    $spreadsheetResult,
                    $empresa,
                    $saldo,
                    $itens_processados,
                    $logs
                );
          
                // Formata os logs para exibição
                $logResult = $this->logservice->formatLog(
                    $logs['inseridos'],
                    $logs['atualizados'],
                    $logs['nao_atualizados'],
                    $logs['com_erro'],
                    $logs['fotos_atualizadas'],
                    $upload_data['orig_name']
                );
                
                // Gerar relatório detalhado se houver erros
                $report_url = $this->_generateErrorReport($logs, $upload_data);

                // Calcular o total de erros corretamente
                $total_erros = 0;
                foreach ($logs['com_erro'] as $key => $value) {
                    if ($key === 'planilha_errors') {
                        $total_erros += count($value);
                    } else {
                        $total_erros++;
                    }
                }

                // Preparar dados para o modal de resultado
                $total_processado = count($logs['inseridos']) + count($logs['atualizados']) + $total_erros;
                
                $processamento = [
                    'total' => $total_processado,
                    'sucesso' => count($logs['inseridos']) + count($logs['atualizados']),
                    'inseridos' => count($logs['inseridos']),
                    'atualizados' => count($logs['atualizados']),
                    'erro' => $total_erros,
                    'report_url' => $report_url,
                    'message' => $logResult['message'],
                    'type' => $logResult['type']
                ];

                // Salvar na sessão para exibir após o redirect
                $this->session->set_flashdata('processamento', $processamento);

                // Calcula os valores de inseridos e atualizados antes de montar a string
                $inseridos = $logs['inseridos'] > 0 ? count($logs['inseridos']) : '0';
                $atualizados = $logs['atualizados'] > 0 ? count($logs['atualizados']) : '0';

                // Monta a string da mensagem usando as variáveis calculadas
                $mensagem = "Importação finalizada. Sucesso: {$processamento['sucesso']}, Inseridos: {$inseridos}, Atualizados: {$atualizados}, Erros: {$processamento['erro']}.";

                // Define o tipo de mensagem com base no número de erros
                $tipo = $processamento['erro'] > 0 ? 'warning' : 'success';

                $this->message_next_render($mensagem, $tipo);

            // Processar arquivo ZIP
            } elseif ($file_ext == '.zip') {
                $this->_processZipUpload($upload_data, $id_empresa);
            }

            unlink($upload_data['full_path']);
            $fim = microtime(true);
            $msg = "Data: " . date('d/m/Y \à\s H:i:s') . " upload - Tempo de Processamento: " . number_format(($fim - $inicio), 2) . " Empresa: " . sess_user_company() . " Usuario: " . sess_user_id();
            $this->functionShutdown($msg);

            redirect('/upload/');
        }

        $data['show_alert_message'] = $this->show_alert_message();
        $empresa = $this->empresa_model->get_entry(sess_user_company());
        $campos_adicionais = explode("|", $empresa->campos_adicionais);
        $data['arquivo_modelo'] = $this->getArquivoModelo($empresa, $campos_adicionais);

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Enviar mestre de itens', '/upload/');
        $this->render('upload', $data);
    }

    private function getColumnMapping() {
        $data = [
            'part_number' => ['PN', 'Codigo', 'Codigo\s(do\s)?(Item|Produto)', 'Part\sNumber'],
            'estabelecimento' => ['Estab(\.)?', 'Estabelecimento'],
            'descricao' => ['DESCRIÇÃO\sITEM', 'Descricao Item', 'Descricao\sItem'],
            'descricao_global' => ['Desc Global', 'Descricao Global', 'DESCRICAO_GLOBAL', 'DESCRICAO GLOBAL'],
            'item_similar' => ['Item\sSimilar', 'Similar'],
            'ncm_atual' => ['NCM', 'NCM Atual'],
            'ncm_fornecedor' => ['NCM Fornecedor', 'NCM Fornecedor'],
            'cnpj' => ['CNPJ', 'Empresa'],
            'forcar_atualizacao' => ['Forcar', 'Atualizar', 'Forcar\sAtualizacao(\s\(Sim\/Nao\))?',
                'Atualizar(\s\(Sim\/Nao\))?'],
            'tag' => ['Tag'],
            'descricao_proposta_completa' => ['Desc(\.|ricao)?\sProposta\sCompleta'],
            'id_resp_fiscal' => ['Fiscal', 'Resp(\.|onsavel)?\sFiscal'],
            'id_resp_engenharia' => ['(Engenheiro|Tecnico)', 'Resp(\.|onsavel)?\s(Tecnico|Engenharia|Engenheiro)'],
            'cod_cest' => ['CEST', 'Codigo\sCEST', 'CEST\sProposto'],
            'foto_1' => ['Foto\s?1'],
            'foto_2' => ['Foto\s?2'],
            'foto_3' => ['Foto\s?3'],
            'foto_4' => ['Foto\s?4'],
            'foto_5' => ['Foto\s?5'],
            'foto_6' => ['Foto\s?6'],
            'foto_7' => ['Foto\s?7'],
            'foto_8' => ['Foto\s?8'],
            'foto_9' => ['Foto\s?9'],
            'foto_10' => ['Foto\s?10'],
            'status_simplus' => ['Simplus', 'Status Simplus', '(.*?)Simplus(.*?)'],
            'funcao' => ['Função', 'Funcao'],
            'peso' => ['Peso', 'PESO'],
            'prioridade' => ['Prioridade', 'PRIORIDADE'],
            'aplicacao' => ['Aplicacao', 'Aplicação'],
            'marca' => ['Marca'],
            'material_constitutivo' => ['Material', 'Material\sConstitutivo'],
            'evento' => ['Evento'],
            'memoria_classificacao' => ['Memoria\sClassificacao', 'Memoria\sClassificação', 'Memoria\sde\sClassificacao', 'Memoria\sde\sClassificação'],
            'inf_adicionais' => ['Informações Adicionais', 'Informacoes Adicionais'],
            'lista_cliente' => ['FAZ PARTE DA LISTA CLIENTE'],
            'maquina' => ['maquina', 'Maquina'],
            'origem' => ['origem', 'Origem'],
            'cod_owner' => ['Owner', 'cod_owner'],
            'pn_primario_mpn' => ['PN_PRIMARIO'],
            'pn_secundario_ipn' => ['PN_SECUNDARIO'],
            'observacoes' => ['observacoes', 'observacões', 'OBSERVACOES', 'OBSERVACÕES'],
            'gestao_mensal' => ['GESTAO_MENSAL'],
            'importado' => ['IMPORTADO']
            // Adicione outros mapeamentos conforme necessidade
        ];

        if (!customer_has_role('alterar_criticidade',sess_user_id())) {  
            unset($data['prioridade']);
        }

        if (!customer_has_role('alterar_owner',sess_user_id())) {  
            unset($data['cod_owner']);
        }

        return $data;
        
    }

    private function getArquivoModelo($empresa, $campos_adicionais) {
        $hasDescricaoGlobal = in_array('descricao_global', $campos_adicionais);
        $hasOwner = in_array('owner', $campos_adicionais);
        
        // Caso 1: Tem maquina E origem
        if (in_array('maquina', $campos_adicionais) && in_array('origem', $campos_adicionais)) {
            if(in_array('pn_primario_secundario', $campos_adicionais)){
                return base_url('assets/excel_modelo/mestre-itens__(modelo_pn).xlsx');
            } else {
                return base_url('assets/excel_modelo/mestre-itens__(modelo).xlsx');
            }
        } 
        // Caso 2: Tem apenas maquina
        elseif (in_array('maquina', $campos_adicionais)) {
            return base_url('assets/excel_modelo/mestre-itens_.xlsx');
        } 
        // Caso 3: Tem apenas origem
        elseif (in_array('origem', $campos_adicionais)) {
            return base_url('assets/excel_modelo/mestre-itens__.xlsx');
        } 
        // Caso 4: Tem descrição global OU owner
        elseif ($hasDescricaoGlobal || $hasOwner) {
            return base_url('assets/excel_modelo/mestre-itens___.xlsx');
        } 
        // Caso 5: Tem pn_primario_secundario
        elseif (in_array('pn_primario_secundario', $campos_adicionais)) {
            $mercedes = ["MERCEDES-BENZ", "MERCEDES"];
            $regex_mercedes = "/" . implode("|", array_map('preg_quote', $mercedes)) . "/";

            if (preg_match($regex_mercedes, strtoupper($empresa->nome_fantasia))) {
                return base_url('assets/excel_modelo/mestre-itens-mercedes.xlsx');
            } else {
                return base_url('assets/excel_modelo/mestre-itens_pn.xlsx');
            }
        } 
        // Caso 6: Tem permissão de gerenciar_mestre e não é sysadmin
        elseif (has_role('gerenciar_mestre') && !has_role('sysadmin')) {
            return base_url('assets/excel_modelo/mestre-itens-cliente.xlsx');
        }
        // Caso padrão
        else {
            return base_url('assets/excel_modelo/mestre-itens.xlsx');
        }
    }

    public function validar_tag()
    {
        if ($this->input->post('part_number')) {
            $this->load->model('item_model');

            $errors = $success = array();
            $itens = $this->input->post('part_number');

            $itens_tag = $this->input->post('item_tag');
            $id_empresa = $this->input->post('id_empresa');
            $estabelecimento = $this->input->post('estabelecimento');

            foreach ($itens as $i => $part_number) {
                $item = $this->item_model->get_entry($part_number, $id_empresa, $estabelecimento[$i]);
                $nova_tag = $itens_tag[$i];

                $motivo = "";

                if ($nova_tag != $item->tag) {
                    $motivo = "Alteração de TAG: " . $item->tag . " -> <strong>" . $nova_tag . "</strong><br />";
                }

                if (empty($motivo)) {
                    $errors[] = $part_number;
                } else {
                    $success[] = $part_number;
                }

                if (empty($errors)) {
                    $this->item_model->update_item($part_number, $id_empresa, array('tag' => $nova_tag), $motivo, $estabelecimento[$i]);
                }
            }

            if (count($errors) > 0) {
                $message['type'] = 'danger';
                $message['msg']  = '<strong>Erro!</strong> Não foi possível atualizar os seguintes itens: <strong>' . implode(', ', $errors) . '</strong>';
                echo '<div class="alert alert-' . $message['type'] . '">' . $message['msg'] . '</div>';
            }

            if (count($success) > 0) {
                $message['type'] = 'success';
                $message['msg']  = '<strong>Sucesso!</strong> Os seguintes itens foram atualizados com sucesso: <strong>' . implode(', ', $success) . '</strong>';
                echo '<div class="alert alert-' . $message['type'] . '">' . $message['msg'] . '</div>';
            }

            return TRUE;
        }

        $list_arr = $this->session->userdata('validar-tag');

        if (empty($list_arr)) {
            show_404();
        }

        $data['list'] = $list_arr;

        $this->load->view('upload-validar-tag', $data);
    }

    public function check_similar_has_photo($part_number_similar, $id_empresa, $estabelecimento = NULL, $sheet, $idx)
    {
        $this->load->model('item_model');

        $has_photo = FALSE;

        if (!$db_has_photo = $this->item_model->check_item_has_photo($part_number_similar, $id_empresa, $estabelecimento)) {

            foreach ($sheet->getData() as $row) {
                if (preg_match('/' . $row[$idx['item_similar']] . '/i', $part_number_similar)) {
                    $list_fotos = preg_grep('/^foto_(\d+)$/', array_keys($idx));

                    if (count($list_fotos)) {
                        foreach (array_keys($list_fotos) as $fkey) {
                            if (!empty($row[$fkey])) {
                                $has_photo = TRUE;
                                break;
                            }
                        }
                    }

                    break;
                }
            }
        }

        if ($db_has_photo === TRUE || $has_photo === TRUE) {
            return TRUE;
        }

        return FALSE;
    }

    /**
     * Processa os dados extraídos da planilha.
     *
     * @param array $spreadsheetResult Resultado do processamento da planilha.
     * @param object $empresa Objeto da empresa.
     * @param array $saldo Saldo da franquia.
     * @param int &$itens_processados Referência ao contador de itens processados.
     * @param array $logs Array de logs inicial.
     * @return array Logs atualizados.
     */
    private function _processSpreadsheetData($spreadsheetResult, $empresa, $saldo, &$itens_processados, $logs) {
        foreach ($spreadsheetResult['data'] as $itemData) {
            $itemLogs = $this->itemservice
                ->processItem(
                    $itemData,
                    $empresa,
                    $saldo,
                    $itens_processados,
                    $spreadsheetResult['idx']
                );

            foreach ($logs as $key => &$log) {
                $logs[$key] = array_merge($log, $itemLogs[$key]);
            }
        }

        return $logs;
    }

    /**
     * Processa o upload de um arquivo ZIP contendo fotos.
     *
     * @param array $upload_data Dados do upload do CodeIgniter.
     * @param int $id_empresa ID da empresa.
     * @return void
     */
    private function _processZipUpload($upload_data, $id_empresa) {
        $upload_photo_path = config_item('upload_photos_path') . $id_empresa . '/';
        if (!is_dir($upload_photo_path)) {
            mkdir($upload_photo_path, 0777, true); // Adicionado 'true' para criar diretórios recursivamente
        }
        $this->unzip->allow(['jpg', 'JPG']);
        $files = $this->unzip->extract($upload_data['full_path'], $upload_photo_path, FALSE);
        if (empty($this->unzip->error_string(NULL, NULL)) && count($files) > 0) {
            foreach ($files as $file) {
                rename($file, dirname($file) . '/' . strtolower(basename($file)));
            }
            $this->message_next_render("Arquivo ZIP [<b>{$upload_data['orig_name']}</b>] recebido e processado com sucesso!");
        } else {
            $this->message_next_render("<strong>Oops!</strong> Ocorreu um erro ao processar o ZIP [<b>{$upload_data['orig_name']}</b>].", 'error');
        }
     }

    /**
     * Gera relatório de erros e retorna a URL para download
     * 
     * @param array $logs Logs de processamento
     * @param array $upload_data Dados do arquivo enviado
     * @param array $spreadsheetResult Resultado do processamento da planilha
     * @return string URL do relatório ou string vazia em caso de falha
     */
    private function _generateErrorReport($logs, $upload_data)
    {
        if (empty($logs['com_erro'])) {
            return '';
        }
        
        try {
            $report_filename = $this->reportservice->generateErrorReport(
                $logs,
                $upload_data['orig_name']
            );
            
            if (empty($report_filename)) {
                return '';
            }
            
            // Obter o diretório usado para salvar o relatório
            $report_dir = $this->session->userdata('last_report_directory');
            if (empty($report_dir)) {
                log_message('error', 'Diretório do relatório não encontrado na sessão');
                return '';
            }
            
            // Verificar se o diretório é acessível via web
            if ($this->_isWebAccessibleDirectory($report_dir)) {
                $web_path = substr($report_dir, strlen(FCPATH));
                return site_url($web_path . $report_filename);
            }
            
            // Se não for acessível via web, copiar para um diretório acessível
            return $this->_copyReportToAccessibleDirectory($report_dir, $report_filename);
            
        } catch (Exception $e) {
            log_message('error', 'Erro ao gerar relatório: ' . $e->getMessage());
            return '';
        }
    }

    /**
     * Verifica se um diretório é acessível via web
     * 
     * @param string $directory Caminho do diretório
     * @return bool True se o diretório é acessível via web
     */
    private function _isWebAccessibleDirectory($directory)
    {
        return strpos($directory, FCPATH) === 0;
    }

    /**
     * Copia o relatório para um diretório acessível via web
     * 
     * @param string $source_dir Diretório de origem
     * @param string $filename Nome do arquivo
     * @return string URL do relatório ou string vazia em caso de falha
     */
    private function _copyReportToAccessibleDirectory($source_dir, $filename)
    {
        $this->load->helper('file');
        $accessible_dir = FCPATH . 'assets/tmp/';
        
        if (!ensure_directory($accessible_dir)) {
            log_message('error', 'Não foi possível garantir o diretório acessível: ' . $accessible_dir);
            return '';
        }
        
        $accessible_path = $accessible_dir . $filename;
        if (!copy($source_dir . $filename, $accessible_path)) {
            log_message('error', 'Falha ao copiar relatório para diretório acessível');
            return '';
        }
        
        return site_url('assets/tmp/' . $filename);
    }
}
