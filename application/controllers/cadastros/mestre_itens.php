<?php

class Mestre_itens extends MY_Controller
{

    public function __construct()
    {
        parent::__construct();

        $this->load->model('item_model');

        if (!is_logged()) {
            redirect('/login');
        }

        if (!has_role('gerenciar_mestre')) {
            show_permission();
        }

        // Enable profiler para debug
        // $this->output->enable_profiler(true);

        $this->load->library('breadcrumbs');
    }

    public function ativar()
    {
        $this->load->library("Item/Status");
        $this->load->model([
            'cad_item_model',
            'cad_item_homologacao_model',
            'item_model',
            'item_log_model',
            'usuario_model'
        ]);

        $post = $this->input->post();
        $user = $this->usuario_model->get_entry(sess_user_id());
        $items = $post['items'];
        $id_empresa = sess_user_company();
        $motivo = trim($post['motivo']);
        if (empty(trim($motivo)))
        {
            $motivo = 'Outra situação';
        }
        foreach ($items as $item)
        {
            $part_number     = $item['part_number'];
            $estabelecimento = $item['estabelecimento'];
            $item = $this->item_model->get_entry($part_number, $id_empresa, $estabelecimento, true);
 
            if (!empty($item->id_item))
            {
                $item_cad_item_homologacao = $this->cad_item_homologacao_model->get_entry_by_id_item($item->id_item);

                if (!empty($item_cad_item_homologacao))
                {
                    $this->db->where('id_item', $item->id_item);
                    $this->db->where('tipo_homologacao', 2);
                    $this->db->delete('cad_item_homologacao');
                } 
            }

            $this->item_model->motivo($motivo, $item->part_number, $item->estabelecimento, $id_empresa);

            $this->item_model->retorna_status($item->ncm_proposto, $item->part_number, $item->estabelecimento, $item->status_anterior);
 
        
            $log_data = array(
                'part_number'     => $item->part_number,
                'estabelecimento' => $item->estabelecimento,
                'id_empresa'      => $id_empresa,
                'titulo'          => 'ativacao',
                'motivo'          => 'Item ativado: ' . '<strong>Part number:</strong> ' . $item->part_number .  '<br>' . '<strong>Responsável:</strong> ' . $user->nome .   '<br>' .'<strong>Motivo:</strong> ' . $motivo,
                'id_usuario'      => sess_user_id(),
                'criado_em'       => date('Y-m-d H:i:s')
            );

            $this->item_log_model->save($log_data);
        
        }

        return response_json(array('status' => 'success', 'message' => 'Dados salvos com sucesso'));
    }

    public function inativar()
    {
        $this->load->library("Item/Status");
        $this->load->model([
            'cad_item_model',
            'cad_item_homologacao_model',
            'item_model',
            'item_log_model',
            'usuario_model'
        ]);
        $id_usuario = sess_user_id();
        $post = $this->input->post();
        $user = $this->usuario_model->get_entry(sess_user_id());
        $items = $post['items'];
        $id_empresa = sess_user_company();

        $motivo = null;
        if (!empty($post['motivo_list']))
        {
            $motivo = trim($post['motivo_list']);
        }

        if (!empty($post['motivo']))
        {
            $motivo .= ' '.trim($post['motivo']);
        }

        foreach ($items as $item)
        {
            $part_number     = $item['part_number'];
            $estabelecimento = $item['estabelecimento'];
            
            $item = $this->item_model->get_entry($part_number, $id_empresa, $estabelecimento, true);
 
            if (!empty($item->id_item))
            {
                $item_cad_item_homologacao = $this->cad_item_homologacao_model->get_entry_by_id_item($item->id_item);

                if (!empty($item_cad_item_homologacao))
                {
                    $dbdata['homologado'] = $post['homologado'];
                    $dbdata['criado_em']  = date('Y-m-d H:i:s');
                    $dbdata['motivo'] = $motivo;
                    $dbdata['id_usuario'] = $id_usuario;

                    if (has_role('engenheiro') && company_can('homologacao_engenharia')) {
                        $tipo_homologacao = 'Engenharia';
                    } else if (has_role('fiscal') && company_can('homologacao_fiscal')) {
                        $tipo_homologacao = 'Fiscal';
                    } else {
                        return response_json(array('status' => 'error', 'message' => 'Perfil do usuário não tem permissão de Engenheiro ou Fiscal e/ou a empresa não possui esta permissão.'));
                    }

                    $where = array('id_item' => $item->id_item, 'tipo_homologacao' => $tipo_homologacao);
                    $this->db->update('cad_item_homologacao', $dbdata, $where);
                } 
            }

            $this->item_model->motivo($motivo, $item->part_number, $item->estabelecimento, $id_empresa, $item->id_status); 


            $this->status->set_status("obsoleto");
            $this->status->update_item($part_number, $estabelecimento);

            $log_data = array(
                'part_number'     => $item->part_number,
                'estabelecimento' => $item->estabelecimento,
                'id_empresa'      => $id_empresa,
                'titulo'          => 'inativacao',
                'motivo'          => 'Item inativado: ' . '<strong>Part number:</strong> ' . $item->part_number .  '<br>' . '<strong>Responsável:</strong> ' .  '<br>' . $user->nome .   '<br>' .'<strong>Motivo:</strong> ' . $motivo,
                'id_usuario'      => sess_user_id(),
                'criado_em'       => date('Y-m-d H:i:s')
            );

            $this->item_log_model->save($log_data);

        }
        
        return response_json(array('status' => 'success', 'message' => 'Dados salvos com sucesso'));
    
    }

    public function index()
    {
        $this->load->model([
            'cad_item_model',
            'anexo_model',
            'empresa_model',
            'empresa_pais_model',
            'empresa_prioridades_model',
            'usuario_model',
            'motivo_model'
        ]);
        $this->load->library("Item/Atributo");

        $post = $this->input->post();
        $per_page = $this->input->get('per_page');

        $data = array();

        $this->apply_default_filters($post, $per_page);

        $status = $this->input->post('status');

        $owner_filter = get_filter_value('owner', $this->item_model);

        $data['lista_status'] = $this->item_model->get_status();

        $search = $this->input->post('search') ? $this->input->post('search') : null;
        if (!is_null($search)) {
            $this->item_model->set_state("filter.search", $search);
        } else {
            $this->item_model->unset_state("filter.search");
        }

        if ($this->input->post('filtered'))
            $this->load->library('pagination');

        //Outros
        $this->item_model->set_state('filter.ignore_like', TRUE);
        $empresa = $this->empresa_model->get_entry(sess_user_company());
        $data['campos_adicionais'] = explode("|", $empresa->campos_adicionais);
        $data['funcoes_adicionais'] = explode("|", $empresa->funcoes_adicionais);

        $hasStatusTriagemDiana = in_array('status_triagem_diana', $data['funcoes_adicionais']);
        $data['hasStatusTriagemDiana'] = $hasStatusTriagemDiana ? true : false;

        $hasPnPrimarioSecundario = in_array('pn_primario_secundario', $data['campos_adicionais']);
        if ($hasPnPrimarioSecundario) {
            (!is_null($search)) ? $this->item_model->set_state("filter.pn_primario_secundario", TRUE) : NULL;
        } else {
            $this->item_model->unset_state("filter.pn_primario_secundario");
        }

        $data['estabelecimentos'] = $this->empresa_model->get_estabelecimentos(sess_user_company());

        $hasDescricaoGlobal = in_array('descricao_global', $data['campos_adicionais']);
        $data['hasDescricaoGlobal'] = $hasDescricaoGlobal;

        $hasOwner = in_array('owner', $data['campos_adicionais']);
        $data['hasOwner'] = $hasOwner;

        $userData = $this->usuario_model->get_entry(sess_user_id());

        $user = new stdClass();
        $user->id_usuario = sess_user_id();
        $user->id_empresa = $userData->id_empresa;
        $user->id_perfil = $userData->id_perfil;

        $data['user'] = $user;

        $total_entries = 0;
        $data['list'] = [];
        $data['pagination'] = [];
        if ($this->item_model->get_state('filter.filtered')) {

            $this->load->library('pagination');
            $limit = 15;
            $offset = $per_page;
            $total_entries = $this->item_model->get_entries(NULL, NULL, FALSE, TRUE);
            $data['list'] = $this->item_model->get_entries($limit, $offset);


            $search = urlencode($search);
            $status = http_build_query(array('status' => $status));

            if (!is_null($owner_filter)) {
                $owner_filter = http_build_query($owner_filter);
            }
            $config['base_url'] = base_url("cadastros/mestre_itens/");
            $config['uri_segment'] = 3;
            //$config['use_page_numbers'] = TRUE;
            $config['total_rows'] = $total_entries;
            $data['total_rows'] = $total_entries;
            $config['per_page'] = $limit;
            $config['page_query_string'] = TRUE;
            $config['num_links'] = 5;

            $this->pagination->initialize($config);

            $data['pagination'] = $this->pagination->create_links();
        } else {
            $data['itens'] = [];
            $data['query_homolog'] = '';
            $data['total_rows'] = 0;
        }
        $this->motivo_model->set_state('filter.id_perfil', $userData->id_perfil);
        $data['motivos'] = $this->motivo_model->get_entries();
 
        $funcoes_adicionais = explode('|', $empresa->funcoes_adicionais);
        $data['has_status_exportacao'] = in_array('status_exportacao', $funcoes_adicionais) ? true : false;
        $data['empresa_pais'] = $this->empresa_pais_model->get_entries_by_empresa(sess_user_company());
        $data['status_todos_atributos'] =  $this->atributo->get_all_status();

        $this->title = "Mestre de Itens";

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Mestre de itens', '/cadastros/mestre_itens/');

        $this->include_css(array('b3-datetimepicker.min.css', 'bootstrap-select/bootstrap-select.css', 'bootstrap-select/bootstrap-select.min.css', 'sweetalert.css'));
        $this->include_js(array('b3-datetimepicker.min.js', 'bootstrap-select/bootstrap-select.js', 'bootstrap-select/bootstrap-select.min.js', 'sweetalert.min.js', 'animate.js', 'jquery.mask.min.js'));

        $this->render('cadastros/mestre_itens/default', $data);
    }

    public function excluir()
    {
        $return = array();
        $return['status'] = FALSE;

        $error = FALSE;

        $items = $this->input->post('items');
        $id_empresa = sess_user_company();

        foreach ($items as $item) {
            $item = $this->item_model->get_entry($item['part_number'], $id_empresa, $item['estabelecimento']);

            if (!$this->user_permission_becomex($item, TRUE)) {
                $error = TRUE;

                $message = sprintf("<b>Atenção! Nenhum item foi removido.</b> O item [<b>%s</b>] não pode ser removido. Entre em contato com os consultores da Becomex.", $item->part_number, $item->status_formatado);
                $this->message_next_render($message, 'error');
                $return['status'] = TRUE;

                break;
            }

            if ($item->status != 'pendente_revisao' && $item->status != 'pendente_atribuicao') {
                $error = TRUE;

                $message = sprintf("<b>Atenção! Nenhum item foi removido.</b> O item [<b>%s</b>] não pode ser removido devido ao seu estado atual [<b>%s</b>].", $item->part_number, $item->status_formatado);
                $this->message_next_render($message, 'error');
                $return['status'] = TRUE;

                break;
            }
        }

        if ($error == FALSE) {
            if ($this->item_model->remove($items, $id_empresa)) {
                $this->message_next_render('Exclusão realizada com sucesso!');
                $return['status'] = TRUE;
            }
        }

        echo json_encode($return);
    }


    public function verifica_saldo($json = true)
    {
        $this->load->model('item_model');
        $this->load->model('empresa_model');
        $this->empresa_model->mail_settings_finance();
        $saldo = $this->item_model->get_saldo_mes();

        if (
            isset($saldo['habilitar_uso_franquia'])
            && $saldo['habilitar_uso_franquia'] == 1
        ) {
            if ($json) {
                return response_json(array(
                    'habilitar_uso_franquia' => $saldo['habilitar_uso_franquia'],
                    'saldo' => $saldo['franquia_disponivel'],
                    'saldo_sem_excedente' => $saldo['total_sem_excedente'],
                    'habilitar_bloqueio' => $saldo['habilitar_bloqueio']
                ));
            } else {
                return array(
                    'habilitar_uso_franquia' => $saldo['habilitar_uso_franquia'],
                    'saldo' => $saldo['franquia_disponivel'],
                    'saldo_sem_excedente' => $saldo['total_sem_excedente'],
                    'habilitar_bloqueio' => $saldo['habilitar_bloqueio']
                );
            }
        } else {
            if ($json) {
                return response_json(array(
                    'habilitar_uso_franquia' => 0,
                    'saldo' => 0,
                    'saldo_sem_excedente' => 0,
                    'habilitar_bloqueio' => 0
                ));
            } else {
                return array(
                    'habilitar_uso_franquia' => 0,
                    'saldo' => 0,
                    'saldo_sem_excedente' => 0,
                    'habilitar_bloqueio' => 0
                );
            }
        }
    }

    public function novo()
    {

        $this->load->library('form_validation');
        $this->load->helper('formatador_helper');

        $this->load->model(
            array(
                'empresa_model',
                'cad_item_model',
                'grupo_tarifario_model',
                'empresa_pais_model',
                'empresa_prioridades_model'
            )
        );

        $this->title = "Mestre de Itens &gt; Novo";
        $id_empresa = sess_user_company();
        $data = array();


        $saldo = $this->verifica_saldo(false);

        if (
            isset($saldo['habilitar_uso_franquia'])
            && $saldo['habilitar_uso_franquia'] == 1 && $saldo['habilitar_bloqueio'] == 1
        ) {
            if ($saldo['saldo'] == 0) {
                $this->message_next_render('Você não possui saldo disponível para criar novos itens. Entre em contato com o suporte.', 'error');
                redirect('cadastros/mestre_itens');
            }
        }

        $empresa = $this->empresa_model->get_entry(sess_user_company());
        $data['campos_adicionais'] = explode("|", $empresa->campos_adicionais);
        $data['funcoes_adicionais'] = explode("|", $empresa->funcoes_adicionais);

        $owners = $this->empresa_model->get_owners_by_empresa($empresa);
        $data['owners'] = $owners;

        $hasDescricaoGlobal = in_array('descricao_global', $data['campos_adicionais']) ? true : false;
        $data['hasDescricaoGlobal'] = $hasDescricaoGlobal;
        $hasPrioridade = in_array('prioridade', $data['campos_adicionais']) ? true : false;

        $descricao = $this->input->post('descricao');
        $descricao_global = $this->input->post('descricao_global');

        $descricao_usada = $hasDescricaoGlobal && $descricao_global ? $descricao_global : $descricao;

        $data['empresa_prioridades'] =  $this->empresa_prioridades_model->get_entry($empresa->id_empresa);

        // Proteção de arquivos que excedem o tamanho do upload_max_size
        $this->handle_upload_max_size();

        if ($this->input->post('submit')) {

            if ($hasDescricaoGlobal) {
                // se 'descricao_global' estiver presente, adicione a regra de validação personalizada
                $this->form_validation->set_rules('descricao', 'Descrição', 'trim|callback_check_descricao');
            } else {
                // se 'descricao_global' não estiver presente, mantenha a regra de validação original
                $this->form_validation->set_rules('descricao', 'Descrição', 'trim|required');
            }

            $this->form_validation->set_rules('part_number', 'Part Number', 'required');
            $this->form_validation->set_rules('pn_primario_mpn', 'PN Primário-MPN', 'trim');
            $this->form_validation->set_rules('pn_secundario_ipn', 'PN Secundário-IPN', 'trim');
            $this->form_validation->set_rules('part_number_similar', 'Part Number Similar', 'trim');
            $this->form_validation->set_rules('estabelecimento', 'Estabelecimento', 'trim|required');
            $this->form_validation->set_rules('ncm', 'NCM', 'trim');
            $this->form_validation->set_rules('ncm_fornecedor', 'NCM Fornecedor', 'trim');
            $this->form_validation->set_rules('id_grupo_tarifario', 'ID Grupo Tarifário', 'trim');
            $this->form_validation->set_rules('grupo_tarifario', 'Grupo Tarifário', 'trim');
            $this->form_validation->set_rules('tag', 'TAG', 'trim');
            $this->form_validation->set_rules('funcao', 'Função', 'trim');
            $this->form_validation->set_rules('inf_adicionais', 'Informações Adicionais', 'trim');
            $this->form_validation->set_rules('peso', 'Peso', 'trim');
            if ($hasPrioridade) {
                $this->form_validation->set_rules('prioridade', 'Prioridade', 'trim|required');
            }
            $this->form_validation->set_rules('aplicacao', 'Aplicação', 'trim');
            $this->form_validation->set_rules('material_constitutivo', 'Material Constitutivo', 'trim');
            $this->form_validation->set_rules('marca', 'Marca', 'trim');
            $this->form_validation->set_rules('observacoes', 'Observações', 'trim');
            $this->form_validation->set_rules('evento', 'Evento', 'trim');
            $this->form_validation->set_rules('cod_owner', 'Owner', 'trim');

            if ($this->form_validation->run() == TRUE) {
                $this->load->model('item_log_model');

                $can_formatar_texto = company_can("formatar_texto", $data['funcoes_adicionais']);

                $part_number         = $this->input->post('part_number');
                $pn_primario_mpn     = clean_str($this->input->post('pn_primario_mpn'), true);
                $pn_secundario_ipn   = clean_str($this->input->post('pn_secundario_ipn'), true);
                $part_number_similar = clean_str($this->input->post('part_number_similar'), true);
                $estabelecimento     = $this->input->post('estabelecimento');

                $id_empresa = sess_user_company();
                $id_usuario = sess_user_id();

                $empresa = $this->empresa_model->get_entry($id_empresa);

                if ($this->item_model->check_item_exists($part_number, $id_empresa, $estabelecimento) == FALSE) {
                    $file_error = $this->upload_ficha_tecnica(
                        $part_number,
                        $estabelecimento,
                        $id_empresa
                    );

                    if ($file_error == TRUE) {
                        $err  = "<h4>Ops... O seguinte erro aconteceu:</h4><ul>";
                        $err .= "<li>" . $file_error . "</li>";
                        $err .= "</ul>";

                        $this->message_on_render($err, 'error');
                    } else {
                        $data = array(
                            'part_number_similar'   => $part_number_similar,
                            'part_number'           => $part_number,
                            'pn_primario_mpn'       => $pn_primario_mpn,
                            'pn_secundario_ipn'     => $pn_secundario_ipn,
                            'descricao'             => formatar_texto($can_formatar_texto, $this->input->post('descricao')),
                            'descricao_global'      => $hasDescricaoGlobal ? formatar_texto($can_formatar_texto, $this->input->post('descricao_global')) : NULL,
                            'estabelecimento'       => $this->input->post('estabelecimento'),
                            'ncm'                   => $this->input->post('ncm'),
                            'ncm_fornecedor'        => $this->input->post('ncm_fornecedor'),
                            'id_empresa'            => sess_user_company(),
                            'evento'                => formatar_texto($can_formatar_texto, $this->input->post('evento')),
                            'tag'                   => $this->input->post('tag'),
                            'funcao'                => formatar_texto($can_formatar_texto, $this->input->post('funcao')),
                            'inf_adicionais'        => formatar_texto($can_formatar_texto, $this->input->post('inf_adicionais')),
                            'observacoes'           => $this->input->post('observacoes') ? $this->input->post('observacoes') : '',
                            'peso'                  => $this->input->post('peso'),
                            'id_prioridade'         => $this->input->post('prioridade'),
                            'aplicacao'             => formatar_texto($can_formatar_texto, $this->input->post('aplicacao')),
                            'marca'                 => formatar_texto($can_formatar_texto, $this->input->post('marca')),
                            'material_constitutivo' => formatar_texto($can_formatar_texto, $this->input->post('material_constitutivo')),
                            'id_resp_fiscal'        => $this->input->post('id_resp_fiscal'),
                            'id_resp_engenharia'    => $this->input->post('id_resp_engenharia'),
                            'memoria_classificacao' => $this->input->post('memoria_classificacao'),
                            'cod_owner'             => $this->input->post('input-owner'),
                            'recebe_email_alteracao_status' => $this->input->post('recebe_email_alteracao_status') ? 1 : 0,
                            'gestao_mensal'         => $this->input->post('gestao_mensal'),
                            'criado_por'            => $id_usuario,
                            'sistema_origem'        => 'MANUAL'
                        );

                        $data['ncm_fornecedor'] = !empty($data['ncm_fornecedor']) ? str_replace(".", "", $data['ncm_fornecedor']) : '';

                        $this->item_model->save($data);

                        $this->empresa_model->mail_settings_finance();
                        generate_item_log('criacao', array(
                            'part_number'     => $data['part_number'],
                            'estabelecimento' => $data['estabelecimento'],
                            'id_empresa'      => $data['id_empresa']
                        ));

                        // Registrar log quando o owner for atribuído ao item
                        if ($data['cod_owner'] && $data['cod_owner'] != '') {

                            $owners = $this->item_model->get_data_owner($data['cod_owner']);
                            $responsavel_nome = 'Responsável indefinido';

                            foreach ($owners as $owner) {
                                if (isset($owner->responsavel_gestor) && $owner->responsavel_gestor = 1) {
                                    $responsavel_nome = $owner->nome;
                                }
                            }

                            $log_data = array(
                                'part_number'     => $data['part_number'],
                                'estabelecimento' => $data['estabelecimento'],
                                'id_empresa'      => $data['id_empresa'],
                                'titulo'          => 'atualizarowner',
                                'motivo'          => 'Owner atribuído ao item: ' . '<strong>Part number:</strong> ' . $data['part_number'] . '<br>' . '<strong>Owner:</strong> ' . $owners[0]->codigo . ' - ' . $owners[0]->descricao . '<br>' . '<strong>Responsável:</strong> ' . $responsavel_nome,
                                'id_usuario'      => sess_user_id(),
                                'criado_em'       => date('Y-m-d H:i:s')
                            );

                            $this->item_log_model->save($log_data);
                        }



                        // Se tem grupo tarifário, cria item na cad_item
                        if ($this->input->post('id_grupo_tarifario')) {
                            if ($this->cad_item_model->check_item_exists(
                                $data['part_number'],
                                sess_user_company(),
                                $data['estabelecimento']
                            ) === false) {

                                $grupo    = $this->grupo_tarifario_model->get_entry($this->input->post('id_grupo_tarifario'));

                                $item_row = $this->item_model->get_entry(
                                    $data['part_number'],
                                    sess_user_company(),
                                    $data['estabelecimento']
                                );

                                // AQUI JÁ DEVE TER ALTERAÇÃO DE STATUS
                                $id_empresa_logada = sess_user_company();
                                $cad_item = array(
                                    'part_number'          => $data['part_number'],
                                    'id_grupo_tarifario'   => $grupo->id_grupo_tarifario,
                                    'ncm_proposto'         => $grupo->ncm_recomendada,
                                    'id_empresa'           => $id_empresa_logada,
                                    'origem'               => 'atribuicao_manual',
                                    'funcao'               => formatar_texto($can_formatar_texto, $this->input->post('funcao')),
                                    'inf_adicionais'       => formatar_texto($can_formatar_texto, $this->input->post('inf_adicionais')),
                                    'aplicacao'            => formatar_texto($can_formatar_texto, $this->input->post('aplicacao')),
                                    'marca'                => formatar_texto($can_formatar_texto, $this->input->post('marca')),
                                    'material_constitutivo' => formatar_texto($can_formatar_texto, $this->input->post('material_constitutivo')),
                                    'estabelecimento'      => $this->input->post('estabelecimento'),
                                    'id_resp_fiscal'       => $item_row->id_resp_fiscal,
                                    'id_resp_engenharia'   => $item_row->id_resp_engenharia,
                                    'memoria_classificacao' => $this->input->post('memoria_classificacao'),
                                    'caracteristicas'      => (!empty($grupo->caracteristica)     ? $grupo->caracteristica    : null),
                                    'subsidio'             => (!empty($grupo->subsidio)           ? $grupo->subsidio          : null),
                                    'dispositivo_legal'    => (!empty($grupo->dispositivo_legal)  ? $grupo->dispositivo_legal : null),
                                    'solucao_consulta'     => (!empty($grupo->solucao_consulta)   ? $grupo->solucao_consulta  : null)
                                );

                                if (has_role('sysadmin') || has_role('consultor')) {
                                    $cad_item['id_resp_engenharia'] = $this->input->post('id_resp_engenharia');
                                }

                                if ($this->input->post('funcao')) {
                                    $cad_item['houve_funcao_manual'] = 1;
                                }

                                if ($this->input->post('aplicacao')) {
                                    $cad_item['houve_aplicacao_manual'] = 1;
                                }

                                if ($this->input->post('marca')) {
                                    $cad_item['houve_marca_manual'] = 1;
                                }

                                if ($this->input->post('material_constitutivo')) {
                                    $cad_item['houve_material_constitutivo_manual'] = 1;
                                }

                                if ($return = $this->cad_item_model->save($cad_item)) {
                                    $motivo = '<strong>Part Number:</strong> ' . $data['part_number'] . '<br><strong>Estabelecimento: </strong>' . $data['estabelecimento'] . '<br> <strong>Grupo atribuído:</strong> ' . $grupo->descricao . ' <br><small><strong>NCM Recomendada: </strong>' . $grupo->ncm_recomendada . '</small>';

                                    $this->load->library("Item/Status");

                                    $this->status->set_status("revisao");
                                    $this->status->update_item($cad_item['part_number'], $cad_item['estabelecimento']);

                                    if (has_role('sysadmin') || has_role('consultor')) {
                                        if ($this->input->post('id_resp_engenharia')) {
                                            if ($item_row->id_resp_engenharia != $this->input->post('id_resp_engenharia')) {
                                                $resp_engenharia = $this->usuario_model->get_entry($this->input->post('id_resp_engenharia'));
                                                $motivo .= '<br><strong>Responsável Engenharia alterado para:</strong>' . $resp_engenharia->nome;
                                            }
                                        }
                                    }

                                    if ($item_row->id_resp_fiscal != $this->input->post('id_resp_fiscal')) {
                                        if ($this->input->post('id_resp_fiscal')) {
                                            $resp_fiscal = $this->usuario_model->get_entry($this->input->post('id_resp_fiscal'));
                                            $motivo .= '<br><strong>Responsável Fiscal alterado para:</strong>' . $resp_fiscal->nome;
                                        }
                                    }

                                    if ($this->input->post('funcao')) {
                                        $motivo .= '<br><strong>Função:</strong> ' . formatar_texto($can_formatar_texto, $this->input->post('funcao'));
                                    }

                                    if ($this->input->post('peso')) {
                                        $motivo .= '<br><strong>Peso:</strong> ' . $this->input->post('peso');
                                    }
                                    if ($this->input->post('prioridade')) {
                                        $itemPrioridade = $this->empresa_prioridades_model->get_entry_by_id(trim($this->input->post('prioridade')));
                                        $motivo .= '<br><strong>Prioridade:</strong> ' . $itemPrioridade[0]->nome;
                                    }

                                    if ($this->input->post('aplicacao')) {
                                        $motivo .= '<br><strong>Aplicação:</strong> ' . formatar_texto($can_formatar_texto, $this->input->post('aplicacao'));
                                    }

                                    if ($this->input->post('marca')) {
                                        $motivo .= '<br><strong>Marca:</strong> ' . formatar_texto($can_formatar_texto, $this->input->post('marca'));
                                    }

                                    if ($this->input->post('material_constitutivo')) {
                                        $motivo .= '<br><strong>Material Constitutivo:</strong> ' . formatar_texto($can_formatar_texto, $this->input->post('material_constitutivo'));
                                    }

                                    if ($this->input->post('memoria_classificacao')) {
                                        $motivo .= '<br><strong>Memória de Classificação:</strong> ' . $this->input->post('memoria_classificacao');
                                    }

                                    $log_data['titulo']          = 'atribuicaogrupo';
                                    $log_data['motivo']          = $motivo;
                                    $log_data['part_number']     = $data['part_number'];
                                    $log_data['estabelecimento'] = $data['estabelecimento'];
                                    $log_data['id_usuario']      = sess_user_id();
                                    $log_data['id_empresa']      = $id_empresa_logada;
                                    $log_data['criado_em']       = date('Y-m-d H:i:s');

                                    $this->item_log_model->save($log_data);

                                    if (!empty($grupo->ncm_recomendada) && !empty($cad_item)) {
                                        $pn = $cad_item['part_number'];
                                        $estab = $cad_item['estabelecimento'];
                                        $id_emp = $id_empresa_logada;
                                        $this->db->query(" UPDATE item i
                                        SET i.status_attr = '1'
                                            WHERE i.part_number = '{$pn}' 
                                            AND i.estabelecimento = '{$estab}'
                                            AND i.id_empresa = '{$id_emp}' ");
                                    }
                                }
                            }
                        }

                        /**
                         * Manipula o status de importação de um item com base no parâmetro POST 'item_importado_default'.
                         *
                         * - Se 'item_importado_default' estiver definido e for igual a 1, marca o item como importado chamando
                         * define_item_importado() no modelo.
                         * - Se 'item_importado_default' não estiver definido ou estiver definido como 0, remove o status de importação chamando
                         * remove_item_importado() no modelo.
                         *
                         * @param string $part_number_get O número da peça do item.
                         * @param string $estabelecimento_get O identificador do estabelecimento.
                         * @param string $id_empresa_get O identificador da empresa.
                         * @return void
                         */
                        $importado = $this->input->post('item_importado_default');
                        $part_number_get = $this->input->post('part_number');
                        $estabelecimento_get = $this->input->post('estabelecimento');
                        $id_empresa = sess_user_company();
                        if ($importado && $importado == 1) {

                            $this->cad_item_model->define_item_importado($part_number_get, $estabelecimento_get, $id_empresa);
                        } else if (($importado && $importado == 0) || !$importado) {

                            $this->cad_item_model->remove_item_importado($part_number_get, $id_empresa, $estabelecimento_get);
                        }

                        $this->message_next_render('Sucesso! Item [<strong>' . $descricao_usada . '</strong> adicionado]');

                        redirect('cadastros/mestre_itens');
                    }
                } else {
                    $err = "<h4>Ops... O seguinte erro aconteceu:</h4><ul><li> O Part Number inserido já está relacionado a empresa <strong>{$empresa->razao_social}</strong></li></ul>";
                    $this->message_on_render($err, 'error');
                }
            } else {
                $err = "<h4>Ops... alguns erros aconteceram</h4><ul>" . validation_errors('<li>', '</li>') . "</ul>";
                $this->message_on_render($err, 'error');
            }
        }

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Mestre de itens', '/cadastros/mestre_itens/');
        $this->breadcrumbs->push('Novo item', '/cadastros/mestre_itens/novo/');

        $this->include_js(array(
            'bootstrap3-typeahead.min.js',
            'bootstrap-select/bootstrap-select.min.js',
            'bootstrap-select/i18n/defaults-pt_BR.min.js',
            'jquery.mask.min.js'
        ));

        $this->include_css(array(
            'bootstrap-select/bootstrap-select.css',

        ));

        $data['can_ncm_fornecedor'] = customer_can('ncm_fornecedor');
        $data['tags']               = $this->empresa_model->get_tags_view(sess_user_company(), NULL, NULL);
        $data['estabelecimentos']   = $this->empresa_model->get_estabelecimentos(sess_user_company());
        $data['usuarios_fiscal']    = $this->usuario_model->get_entries_by_role('fiscal', sess_user_company());
        $data['usuarios_eng']       = $this->usuario_model->get_entries_by_role('engenheiro', sess_user_company());
        $data['empresa']            = $this->empresa_model->get_entry(sess_user_company());
        $data['importado_default']  = $this->empresa_model->check_imported_item_permission($id_empresa);

        $this->render('cadastros/mestre_itens/novo', $data);
    }

    public function check_part_number_exists($part_number)
    {
        $id_empresa  = sess_user_company();
        $estabelecimento = $this->input->post('estabelecimento');

        if ($part_number) {
            try {
                $this->item_model->get_entry($part_number, $id_empresa, $estabelecimento);
            } catch (Exception $e) {
                $this->form_validation->set_message('check_part_number_exists', 'O %s [' . $part_number . '][' . $estabelecimento . '] não está cadastrado no banco de dados.');
                return FALSE;
            }
        }

        return TRUE;
    }

    public function part_number_similar_has_diff($part_number_similar)
    {
        $part_number = $this->input->post('part_number');
        $id_empresa  = $this->input->post('id_empresa');

        if ($this->item_model->similar_has_diff($part_number, $part_number_similar, $id_empresa)) {
            $this->form_validation->set_message('part_number_similar_has_diff', 'O %s [' . $part_number_similar . '] possui divergência no grupo tarifário.');
            return FALSE;
        } else {
            return TRUE;
        }
    }

    public function check_differs($part_number_similar)
    {
        $campo = $this->input->post('part_number');

        if (!strcmp($part_number_similar, $campo)) {
            $this->form_validation->set_message('check_differs', 'O %s não pode ser o mesmo que o Part Number.');
            return FALSE;
        }

        return TRUE;
    }

    private function user_permission_becomex($item, $return = false)
    {
        $this->load->model('usuario_model');
        $user = $this->usuario_model->get_entry(sess_user_id());

        // TODO: Rever outar maneira para implementar essa validação.
        if (
            (!has_role('sysadmin') && !has_role('consultor')) &&
            in_array($item->status, array('pendente_revisao', 'homologado', 'pendente_hom_fiscal', 'pendente_hom_engenharia'))
        ) {
            if ($return === FALSE) {
                show_error('Sem permissão de acesso', 401);
            }
            return false;
        }
        return true;
    }

    public function salvar_usuarios_seguidores()
    {
        $this->load->model('item_model');

        // $part_number = $this->input->post('part_number');
        // $id_empresa = $this->input->post('id_empresa');
        // $estabelecimento = $this->input->post('estabelecimento');
        $seguidores = $this->input->post('seguidores');

        // Verifica se foram informados os dados obrigatórios
        if (empty($seguidores)) {
            // Retorna uma mensagem de erro
            $this->output
                ->set_status_header(400)
                ->set_content_type('application/json')
                ->set_output(json_encode(array('mensagem' => 'Dados incompletos')));

            return;
        }

        foreach ($seguidores as $seguidor) {
            $part_number = $seguidor['part_number'];
            $id_empresa = $seguidor['id_empresa'];
            $estabelecimento = $seguidor['estabelecimento'];
            $email = $seguidor['email'];

            // Verifica se o usuário já está seguindo o item
            $query = $this->db->get_where('rel_usuarios_seguidores', array(
                'part_number' => $part_number,
                'id_empresa' => $id_empresa,
                'estabelecimento' => $estabelecimento,
                'email_seguidor' => $email
            ));
            $result = $query->row();

            if (!$result) {
                // Insere o usuário como seguidor do item
                $this->db->insert('rel_usuarios_seguidores', array(
                    'part_number' => $part_number,
                    'id_empresa' => $id_empresa,
                    'estabelecimento' => $estabelecimento,
                    'email_seguidor' => $email
                ));
            }
        }

        // Retorna uma mensagem de sucesso
        $this->output
            ->set_status_header(200)
            ->set_content_type('application/json')
            ->set_output(json_encode(array('mensagem' => 'Seguidores salvos com sucesso')));
    }

    public function remover_seguidor()
    {
        $this->load->model('item_model');

        $part_number = $this->input->post('part_number');
        $id_empresa = $this->input->post('id_empresa');
        $estabelecimento = $this->input->post('estabelecimento');
        $email = $this->input->post('email');

        // Verifica se foram informados os dados obrigatórios
        if (empty($part_number) || empty($id_empresa) || empty($estabelecimento) || empty($email)) {
            // Retorna uma mensagem de erro
            $this->output
                ->set_status_header(400)
                ->set_content_type('application/json')
                ->set_output(json_encode(array('mensagem' => 'Dados incompletos')));

            return;
        }

        // Verifica se o usuário já está seguindo o item
        $query = $this->db->get_where('rel_usuarios_seguidores', array(
            'part_number' => $part_number,
            'id_empresa' => $id_empresa,
            'estabelecimento' => $estabelecimento,
            'email_seguidor' => $email
        ));
        $result = $query->row();

        if ($result) {
            // Remove o usuário como seguidor do item
            $this->db->delete('rel_usuarios_seguidores', array(
                'part_number' => $part_number,
                'id_empresa' => $id_empresa,
                'estabelecimento' => $estabelecimento,
                'email_seguidor' => $email
            ));
        }

        // Retorna uma mensagem de sucesso
        $this->output
            ->set_status_header(200)
            ->set_content_type('application/json')
            ->set_output(json_encode(array('mensagem' => 'Seguidor removido com sucesso')));
    }

    public function editar()
    {
        $this->load->library("Item/Status");
        $usuario_logado = sess_user_id();
        // $this->load->library('session');
        $permissao_inativar_pns = customer_has_role('inativar_pns', $usuario_logado);

        $part_number_get     = $this->input->get('part_number');
        $id_empresa_get      = $this->input->get('id_empresa');
        $estabelecimento_get = $this->input->get('estabelecimento');

        $this->load->model(
            array(
                'empresa_model',
                'cad_item_model',
                'grupo_tarifario_model',
                'anexo_model',
                'cad_item_homologacao_model',
                'empresa_pais_model',
                'item_pais_model',
                'empresa_prioridades_model',
                'comex_model',
                'motivo_model'
            )
        );

        $this->load->helper('formatador_helper');

        $data = array();

        $entry_cad_item = "";

        try {
            $entry = $this->item_model->get_entry($part_number_get, $id_empresa_get, $estabelecimento_get);

            $usuarios_seguidores = $this->item_model->get_entry_usuarios_seguidores($part_number_get, $id_empresa_get, $estabelecimento_get);

            $entry->status_formatado = $entry->status_formatado == 'Homologar' ? 'Pendente de Homologação' : $entry->status_formatado;
            $entry->peso = format_peso($entry->peso);

            try {
                $entry_cad_item = $this->cad_item_model->get_entry_by_pn($part_number_get, $id_empresa_get, $estabelecimento_get);
            } catch (Exception $e) {
                if ($e->getMessage() != 'Nenhum item encontrado') {
                    throw new Exception($e->getMessage());
                }
            }

            if (!empty($entry_cad_item)) {
                //     $entry->memoria_classificacao = empty($entry_cad_item->memoria_classificacao) ? $entry->memoria_classificacao :  $entry_cad_item->memoria_classificacao;
                //     $entry->inf_adicionais        = empty($entry_cad_item->inf_adicionais)        ? $entry->inf_adicionais        :  $entry_cad_item->inf_adicionais;

                $entry->id_resp_fiscal = $entry_cad_item->id_resp_fiscal;
                $entry->id_resp_engenharia = $entry_cad_item->id_resp_engenharia;
            }

            $entry->importado = $this->comex_model->check_imported($part_number_get, $id_empresa_get, $estabelecimento_get);
            $data['entry'] = $entry;
            $data['usuarios_seguidores'] = $usuarios_seguidores;

            $empresa = $this->empresa_model->get_entry($id_empresa_get);

            $owners = $this->empresa_model->get_owners_by_empresa($empresa);
            $data['owners'] = $owners;

            $data['empresa_pais'] = $this->empresa_pais_model->get_entries_by_empresa($empresa->id_empresa);

            foreach ($data['empresa_pais'] as $key => $value) {
                $data['empresa_pais'][$key]->item_pais = $this->item_pais_model->get_entries_by_item($part_number_get, $id_empresa_get, $estabelecimento_get, $value->id_pais);
            }
        } catch (Exception $e) {
            show_error($e->getMessage());
        }

        $this->load->model('usuario_model');
        $user = $this->usuario_model->get_entry(sess_user_id());

        if (
            (has_role('sysadmin') || has_role('consultor')) &&
            in_array($entry->status, array('pendente_revisao', 'homologado', 'pendente_hom_fiscal', 'pendente_hom_engenharia'))
        ) {
            $data['show_edit_admin'] = TRUE;
        }

        $this->title = "Mestre de Itens &gt; Editar";

        $empresa = $this->empresa_model->get_entry(sess_user_company());
        $data['campos_adicionais'] = explode("|", $empresa->campos_adicionais);
        $data['funcoes_adicionais'] = explode("|", $empresa->funcoes_adicionais);

        $hasDescricaoGlobal = in_array('descricao_global', $data['campos_adicionais']) ? true : false;
        $data['hasDescricaoGlobal'] = $hasDescricaoGlobal;
        $hasPrioridade = in_array('prioridade', $data['campos_adicionais']) ? true : false;

        $descricao = $this->input->post('descricao');
        $descricao_global = $this->input->post('descricao_global');

        $descricao_usada = $hasDescricaoGlobal && $descricao_global ? $descricao_global : $descricao;

        $data['integracao_simplus'] = $empresa->integracao_simplus;

        $data['empresa_prioridades'] =  $this->empresa_prioridades_model->get_entry($empresa->id_empresa);

        $this->handle_upload_max_size();

        if ($this->input->post('submit')) {
            if ($this->user_permission_becomex($entry, TRUE)) {
                $this->load->library('form_validation');

                if ($hasDescricaoGlobal) {
                    // se 'descricao_global' estiver presente, adicione a regra de validação personalizada
                    $this->form_validation->set_rules('descricao', 'Descrição', 'trim|callback_check_descricao');
                } else {
                    // se 'descricao_global' não estiver presente, mantenha a regra de validação original
                    $this->form_validation->set_rules('descricao', 'Descrição', 'trim|required');
                }

                $this->form_validation->set_rules('part_number', 'Part Number', 'trim');
                $this->form_validation->set_rules('pn_primario_mpn', 'PN Primário-MPN', 'trim');
                $this->form_validation->set_rules('pn_secundario_ipn', 'PN Secundário-IPN', 'trim');
                $this->form_validation->set_rules('part_number_similar', 'Part Number Similar', 'trim');
                // $this->form_validation->set_rules('descricao', 'Descrição', 'trim|required');
                $this->form_validation->set_rules('estabelecimento', 'Estabelecimento', 'trim');
                $this->form_validation->set_rules('ncm', 'NCM', 'trim');
                $this->form_validation->set_rules('ncm_fornecedor', 'NCM Fornecedor', 'trim');
                $this->form_validation->set_rules('evento', 'Evento', 'trim');
                $this->form_validation->set_rules('cod-owner', 'Owner', 'trim');

                if ($hasPrioridade) {
                    $this->form_validation->set_rules('prioridade', 'Prioridade', 'trim|required');
                }
                if ($this->form_validation->run() == TRUE) {
                    $id_empresa          = sess_user_company();
                    $empresa             = $this->empresa_model->get_entry($id_empresa);

                    $can_formatar_texto = company_can("formatar_texto", $data['funcoes_adicionais']);

                    $part_number         = $this->input->post('part_number');
                    $pn_primario_mpn     = clean_str($this->input->post('pn_primario_mpn'), true);
                    $pn_secundario_ipn   = clean_str($this->input->post('pn_secundario_ipn'), true);
                    $estabelecimento     = $this->input->post('estabelecimento');
                    $part_number_similar = $this->input->post('part_number_similar');
                    $id_grupo_tarifario  = $this->input->post('id_grupo_tarifario');

                    if ($this->input->post('remover_anexo') == 1) {
                        $this->anexo_model->delete($part_number, $estabelecimento, $id_empresa);
                    }

                    $file_error = $this->upload_ficha_tecnica(
                        $part_number,
                        $estabelecimento,
                        $id_empresa
                    );

                    if ($file_error == TRUE) {
                        $err  = "<h4>Ops... O seguinte erro aconteceu:</h4><ul>";
                        $err .= "<li>" . $file_error . "</li>";
                        $err .= "</ul>";

                        $this->message_on_render($err, 'error');
                    } else {
                        $item_entry = $this->item_model->get_entry($part_number, $id_empresa, $estabelecimento);

                        // Se tem grupo tarifário, cria/atualiza a cad_item
                        if ($id_grupo_tarifario) {
                            $grupo    = $this->grupo_tarifario_model->get_entry($id_grupo_tarifario);

                            $cad_item = array(
                                'part_number'          => $part_number,
                                'id_grupo_tarifario'   => $grupo->id_grupo_tarifario,
                                'ncm_proposto'         => $grupo->ncm_recomendada,
                                'id_empresa'           => sess_user_company(),
                                'origem'               => 'atribuicao_manual',
                                'funcao'               => formatar_texto($can_formatar_texto, $this->input->post('funcao')),
                                'inf_adicionais'       => formatar_texto($can_formatar_texto, $this->input->post('inf_adicionais')),
                                'aplicacao'            => formatar_texto($can_formatar_texto, $this->input->post('aplicacao')),
                                'marca'                => formatar_texto($can_formatar_texto, $this->input->post('marca')),
                                'material_constitutivo' => formatar_texto($can_formatar_texto, $this->input->post('material_constitutivo')),
                                'memoria_classificacao' => $this->input->post('memoria_classificacao'),
                                'estabelecimento'      => $this->input->post('estabelecimento'),
                                'id_resp_fiscal'       => $this->input->post('id_resp_fiscal'),
                                'id_resp_engenharia'   => has_role('sysadmin') || has_role('consultor') ? $this->input->post('id_resp_engenharia') : sess_user_id()
                            );


                            if ($this->input->post('funcao')) {
                                $cad_item['houve_funcao_manual'] = 1;
                            }

                            if ($this->input->post('inf_adicionais')) {
                                $cad_item['houve_inf_adicionais_manual'] = 1;
                            }

                            if ($this->input->post('aplicacao')) {
                                $cad_item['houve_aplicacao_manual'] = 1;
                            }

                            if ($this->input->post('marca')) {
                                $cad_item['houve_marca_manual'] = 1;
                            }

                            if ($this->input->post('material_constitutivo')) {
                                $cad_item['houve_material_constitutivo_manual'] = 1;
                            }

                            if ($this->input->post('revisao')) {
                                $cad_item['descricao_mercado_local'] = null;
                                $id_status = $item_entry->id_status = $this->status->set_status("revisao");
                                $this->status->update_item($part_number, $estabelecimento);
                            }

                            $log_atribuicao = TRUE;

                            // $existe_cad_item = $this->cad_item_model->check_item_exists($part_number, $id_empresa, $estabelecimento);
                            // if ($existe_cad_item == false && !empty($id_grupo_tarifario))
                            // {
                            //     $this->status->set_status("revisao");
                            //     $this->status->update_item($part_number, $estabelecimento);
                            // } 
                            try {
                                $cad_item_entry = $this->cad_item_model->get_entry_by_pn($part_number, $id_empresa, $estabelecimento);

                                $this->habilitaFStatus($entry->status);
                                $this->cad_item_model->saveCadItem($cad_item);
                                $this->cad_item_model->atualiza_status_exportacao(0, array($cad_item_entry->id_item), sess_user_company(), $part_number, $estabelecimento);
                                $this->desabilitaFStatus($entry->status);

                                if ($cad_item_entry->id_grupo_tarifario != $id_grupo_tarifario) {
                                    $this->cad_item_homologacao_model->drop_item($cad_item_entry->id_item);

                                    $id_status = $item_entry->id_status = $this->status->set_status("homologar");
                                    $this->status->update_item($part_number, $estabelecimento);

                                    $cad_item_extra = array(
                                        'caracteristicas'   => (!empty($grupo->caracteristica)     ? $grupo->caracteristica    : null),
                                        'subsidio'          => (!empty($grupo->subsidio)           ? $grupo->subsidio          : null),
                                        'dispositivo_legal' => (!empty($grupo->dispositivo_legal)  ? $grupo->dispositivo_legal : null),
                                        'solucao_consulta'  => (!empty($grupo->solucao_consulta)   ? $grupo->solucao_consulta  : null)
                                    );

                                    $this->db->query(" UPDATE item i
                                    SET i.status_attr = '1'
                                        WHERE i.part_number = '{$part_number}' 
                                        AND i.estabelecimento = '{$estabelecimento}'
                                        AND i.id_empresa = '{$id_empresa}' ");

                                    if (!empty($cad_item_entry->id_item) && !empty($id_grupo_tarifario)) {
                                        $this->db->where('id_grupo_tarifario', $cad_item_entry->id_grupo_tarifario);
                                        $query = $this->db->get('grupo_tarifario');
                                        $result_grupo_tarifario = $query->row();
                                        $ncm_grupo_tarifario = $result_grupo_tarifario->ncm_recomendada;

                                        $this->db->where('id_grupo_tarifario', $id_grupo_tarifario);
                                        $query = $this->db->get('grupo_tarifario');
                                        $result_grupo_tarifario = $query->row();
                                        $ncm_novo_grupo_tarifario = $result_grupo_tarifario->ncm_recomendada;

                                        if (!empty($ncm_grupo_tarifario) && !empty($ncm_novo_grupo_tarifario) && $ncm_grupo_tarifario != $ncm_novo_grupo_tarifario) {
                                            $this->db->where('id_item', $cad_item_entry->id_item);
                                            $this->db->where("id_grupo_tarifario <> '{$id_grupo_tarifario}'", NULL, FALSE);
                                            $this->db->delete('cad_item_attr');
                                        } else {
                                            $this->db->set('id_grupo_tarifario', $id_grupo_tarifario);
                                            $this->db->from('cad_item_attr');
                                            $this->db->where('id_item', $cad_item_entry->id_item);
                                            $this->db->where("id_grupo_tarifario <> '{$id_grupo_tarifario}'", NULL, FALSE);
                                            $this->db->update('cad_item_attr attr');
                                        }
                                    }

                                    $this->empresa_model->mail_settings_finance();
                                    $this->cad_item_model->limpar_atributos_nao_relacionados($cad_item_entry->id_item, $grupo->ncm_recomendada,  false);
                                    $this->habilitaFStatus($entry->status);
                                    $this->cad_item_model->save($cad_item_extra, array('id_item' => $cad_item_entry->id_item));
                                    $this->desabilitaFStatus($entry->status);
                                } else {
                                    $log_atribuicao = FALSE;
                                }
                            } catch (Exception $e) {
                                $cad_item['id_resp_fiscal']     = $item_entry->id_resp_fiscal;
                                $cad_item['id_resp_engenharia'] = $item_entry->id_resp_engenharia;

                                $this->habilitaFStatus($entry->status);
                                $this->cad_item_model->save($cad_item);
                                $this->desabilitaFStatus($entry->status);
                            }

                            $this->load->model('item_log_model');
                            $motivo = '';
                            if ($log_atribuicao) {

                                $this->update_log_itens($this->input->post(), $cad_item_entry, $part_number, $estabelecimento_get);
                            } else {
                                $this->update_log_itens($this->input->post(), $cad_item_entry, $part_number, $estabelecimento_get);
                            }
                        } else {
                            $this->update_log_itens($this->input->post(), $item_entry, $part_number, $estabelecimento_get);
                        }

                        // if (empty($id_status))
                        // {
                        //     $id_status = empty($id_status) ? $this->status->set_status("revisao") : $id_status;
                        // }

                        $data = array(
                            'part_number_similar'   => $part_number_similar,
                            'pn_primario_mpn'       => $pn_primario_mpn,
                            'pn_secundario_ipn'     => $pn_secundario_ipn,
                            'descricao'             => formatar_texto($can_formatar_texto, $this->input->post('descricao')),
                            'descricao_global'      => $hasDescricaoGlobal ? formatar_texto($can_formatar_texto, $this->input->post('descricao_global')) : NULL,
                            'estabelecimento'       => $estabelecimento,
                            'ncm'                   => $this->input->post('ncm'),
                            'ncm_fornecedor'        => $this->input->post('ncm_fornecedor'),
                            'id_empresa'            => $id_empresa,
                            'evento'                => formatar_texto($can_formatar_texto, $this->input->post('evento')),
                            'tag'                   => $this->input->post('tag'),
                            'funcao'                => formatar_texto($can_formatar_texto, $this->input->post('funcao')),
                            'inf_adicionais'        => formatar_texto($can_formatar_texto, $this->input->post('inf_adicionais')),
                            'peso'                  => $this->input->post('peso'),
                            'id_prioridade'         => $this->input->post('prioridade'),
                            'aplicacao'             => formatar_texto($can_formatar_texto, $this->input->post('aplicacao')),
                            'observacoes'           => $this->input->post('observacoes'),
                            'marca'                 => formatar_texto($can_formatar_texto, $this->input->post('marca')),
                            'material_constitutivo' => formatar_texto($can_formatar_texto, $this->input->post('material_constitutivo')),
                            'memoria_classificacao' => $this->input->post('memoria_classificacao'),
                            'maquina'               => $this->input->post('maquina'),
                            'origem'                => $this->input->post('origem'),
                            'id_resp_fiscal'        => $this->input->post('id_resp_fiscal'),
                            'id_resp_engenharia'    => has_role('sysadmin') || has_role('consultor') ? $this->input->post('id_resp_engenharia') : sess_user_id(),
                            'cod_owner'             => $this->input->post('input-owner'),
                            'recebe_email_alteracao_status' => $this->input->post('recebe_email_alteracao_status'),
                            'gestao_mensal'         => $this->input->post('gestao_mensal'),
                        );

                        $data['ncm_fornecedor'] = !empty($data['ncm_fornecedor']) ? str_replace(".", "", $data['ncm_fornecedor']) : '';
                        $check_change = array(
                            'part_number'                  => $part_number,
                            'estabelecimento'              => $estabelecimento,
                            'descricao'                    => formatar_texto($can_formatar_texto, $this->input->post('descricao')),
                            'id_empresa'                   => $id_empresa,
                            'responsavel_fiscal_atual'     => $this->input->post('id_resp_fiscal'),
                            'responsavel_engenharia_atual' => has_role('sysadmin') || has_role('consultor') ? $this->input->post('id_resp_engenharia') : sess_user_id()
                        );
                        if (!empty($item_entry->id_status)) {
                            $check_change['status_atual'] = $item_entry->id_status;
                        }

                        if ($item_entry->status_formatado == 'Aguardando definição responsável' && !empty($data['cod_owner'])) {
                            $this->load->library("Item/Status");

                            $this->status->set_status("em_analise");
                            $this->status->update_item($part_number, $estabelecimento, $id_empresa);
                        }

                        if ($data['cod_owner'] && $data['cod_owner'] != $item_entry->cod_owner) {

                            $this->load->model('item_log_model');

                            $owners = $this->item_model->get_data_owner($data['cod_owner']);
                            $responsavel_nome = 'Responsável indefinido';

                            foreach ($owners as $owner) {
                                if (isset($owner->responsavel_gestor) && $owner->responsavel_gestor = 1) {
                                    $responsavel_nome = $owner->nome;
                                }
                            }

                            $log_data = array(
                                'part_number'     => $part_number_get,
                                'estabelecimento' => $data['estabelecimento'],
                                'id_empresa'      => $data['id_empresa'],
                                'titulo'          => 'atualizarowner',
                                'motivo'          => 'Owner atribuído ao item: ' . '<strong>Part number:</strong> ' . $part_number_get . '<br>' . '<strong>Owner:</strong> ' . $owners[0]->codigo . ' - ' . $owners[0]->descricao . '<br>' . '<strong>Responsável:</strong> ' . $responsavel_nome,
                                'id_usuario'      => sess_user_id(),
                                'criado_em'       => date('Y-m-d H:i:s')
                            );

                            $this->item_log_model->save($log_data);
                        }

                        if ($hasDescricaoGlobal) {

                            if ($item_entry->id_status == 14 && $data['descricao'] != $item_entry->descricao) {

                                $this->load->library("Item/Status");
                                $this->status->set_status("respondido");
                                $this->status->update_item($part_number, $estabelecimento, $id_empresa);
                            }
                        }

                        $this->item_model->set_change_send_mail($check_change);
                        $this->item_model->save($data, array(
                            'part_number'       => $part_number_get,
                            'id_empresa'        => $id_empresa_get,
                            'estabelecimento'   => $estabelecimento_get
                        ));

                        if ($permissao_inativar_pns)
                        {
                            $inativar_pn = $this->input->post('inativar_pn');
     
                            // ativar
                            if ($entry->id_status == 4 && $inativar_pn != 0 )
                            {
    
                                $this->load->library("Item/Status");
                                $this->load->model([
                                    'cad_item_model',
                                    'cad_item_homologacao_model',
                                    'item_model',
                                    'item_log_model'
                                ]);
                        
                                $post = $this->input->post();
                                $id_empresa = sess_user_company();
                                $motivo = trim($post['motivo_select']);
                        
                                if (empty(trim($motivo)))
                                {
                                    $motivo = 'Outra situação';
                                }
                                $motivo .= ' '.trim($post['motivo']);
 
                                if (!empty($entry->id_item))
                                {
                                    $item_cad_item_homologacao = $this->cad_item_homologacao_model->get_entry_by_id_item($entry->id_item);
                    
                                    if (!empty($item_cad_item_homologacao))
                                    {
                                        $this->db->where('id_item', $entry->id_item);
                                        $this->db->where('tipo_homologacao', 2);
                                        $this->db->delete('cad_item_homologacao');
                                    } 
                                }
                    
                                $this->item_model->motivo($motivo, $entry->part_number, $entry->estabelecimento, $id_empresa);  
                                $this->item_model->retorna_status($entry->ncm_proposto, $entry->part_number, $entry->estabelecimento, $entry->status_anterior);
                    
                                $log_data = array(
                                    'part_number'     => $entry->part_number,
                                    'estabelecimento' => $entry->estabelecimento,
                                    'id_empresa'      => $id_empresa,
                                    'titulo'          => 'ativacao',
                                    'motivo'          => 'Item ativado: ' . '<strong>Part number:</strong> ' . $entry->part_number .  '<br>' .'<strong>Responsável:</strong> ' . $user->nome .   '<br>' .'<strong>Motivo:</strong> ' . $motivo,
                                    'id_usuario'      => sess_user_id(),
                                    'criado_em'       => date('Y-m-d H:i:s')
                                );
    
                                $this->item_log_model->save($log_data);

                                // inativar
                            } else if ($entry->id_status != 4 && $inativar_pn == 0 ){
    
                                $this->load->library("Item/Status");
                                $this->load->model([
                                    'cad_item_model',
                                    'cad_item_homologacao_model',
                                    'item_model',
                                    'item_log_model'
                                ]);
                                $id_usuario = sess_user_id();
                                $post = $this->input->post();
                                $id_empresa = sess_user_company();
                                $motivo = trim($post['motivo_select']).' '.trim($post['motivo']);
                                if (empty(trim($motivo)))
                                {
                                    $motivo = 'Outra situação';
                                }
                                    if (!empty($entry->id_item))
                                    {
                                        $item_cad_item_homologacao = $this->cad_item_homologacao_model->get_entry_by_id_item($entry->id_item);
     
                                        if (!empty($item_cad_item_homologacao))
                                        {
                              
                                            $dbdata['homologado'] = 2;
                                            $dbdata['criado_em']  = date('Y-m-d H:i:s');
                                            $dbdata['motivo'] = $motivo;
                                            $dbdata['id_usuario'] = $id_usuario;
                        
                                            if (has_role('engenheiro') && company_can('homologacao_engenharia')) {
                                                $tipo_homologacao = 'Engenharia';
                                            } else if (has_role('fiscal') && company_can('homologacao_fiscal')) {
                                                $tipo_homologacao = 'Fiscal';
                                            } else {
                                                return response_json(array('status' => 'error', 'message' => 'Perfil do usuário não tem permissão de Engenheiro ou Fiscal e/ou a empresa não possui esta permissão.'));
                                            }
                        
                        
                                            $where = array('id_item' => $entry->id_item, 'tipo_homologacao' => $tipo_homologacao);
                                            $this->db->update('cad_item_homologacao', $dbdata, $where);
                                        } 
                        
                                    }
                  
                                    $this->item_model->motivo($motivo, $entry->part_number, $entry->estabelecimento, $id_empresa, $entry->id_status); 
                                    $this->status->set_status("obsoleto");
                                    $this->status->update_item($entry->part_number, $entry->estabelecimento);

                                    $log_data = array(
                                        'part_number'     => $entry->part_number,
                                        'estabelecimento' => $entry->estabelecimento,
                                        'id_empresa'      => $id_empresa,
                                        'titulo'          => 'inativacao',
                                        'motivo'          => 'Item inativado: ' . '<strong>Part number:</strong> ' . $entry->part_number .  '<br>' . '<strong>Responsável:</strong> ' . $user->nome .  '<br>' . '<strong>Motivo:</strong> ' . $motivo,
                                        'id_usuario'      => sess_user_id(),
                                        'criado_em'       => date('Y-m-d H:i:s')
                                    );
        
                                    $this->item_log_model->save($log_data);
                            }
                        }


                        /**
                         * Manipula o status de importação de um item com base no parâmetro POST 'item_importado_default'.
                         *
                         * - Se 'item_importado_default' estiver definido e for igual a 1, marca o item como importado chamando
                         * define_item_importado() no modelo.
                         * - Se 'item_importado_default' não estiver definido ou estiver definido como 0, remove o status de importação chamando
                         * remove_item_importado() no modelo.
                         *
                         * @param string $part_number_get O número da peça do item.
                         * @param string $estabelecimento_get O identificador do estabelecimento.
                         * @param string $id_empresa_get O identificador da empresa.
                         * @return void
                         */
                        $importado = $this->input->post('item_importado_default');
                        if ($this->input->post('item_importado_default') && $this->input->post('item_importado_default') == 1) {
                            $this->cad_item_model->define_item_importado($part_number_get, $estabelecimento_get, $id_empresa_get);
                        } else if (($importado && $importado == 0) || !$importado) {
                            $this->cad_item_model->remove_item_importado($part_number_get, $id_empresa_get, $estabelecimento_get);
                        }

                        // generate_item_log('atualizacao', array(
                        //     'part_number'       => $part_number_get,
                        //     'estabelecimento'   => $estabelecimento_get,
                        //     'id_empresa'        => $id_empresa_get
                        // ), $item_entry);

                        $this->message_next_render('Sucesso! Item [<strong>' . $descricao_usada . '</strong> atualizado]');

                        redirect('cadastros/mestre_itens');
                    }
                } else {
                    $err = "<h4>Ops... alguns erros aconteceram</h4><ul>" . validation_errors('<li>', '</li>') . "</ul>";
                    $this->message_on_render($err, 'error');
                }
            } else {
                $err = "<h4>Não é possível alterar as informações após salvar a inclusão</h4>
                    <ul><li>Entre em contato com os consultores da Becomex para alteração</li></ul>";
                $this->message_on_render($err, 'error');
            }
        }

        try {
            $cad_item = $this->cad_item_model->get_entry_by_pn($entry->part_number, $entry->id_empresa, $entry->estabelecimento);
            if (isset($cad_item->id_item)) {
                $data['cad_item'] = $cad_item;

                if (!empty($cad_item->id_grupo_tarifario)) {
                    $data['grupo'] = $this->grupo_tarifario_model->get_entry($cad_item->id_grupo_tarifario);
                }
            }
        } catch (Exception $e) {
        }

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Mestre de itens', '/cadastros/mestre_itens/');
        $this->breadcrumbs->push('Editar item', '/cadastros/mestre_itens/editar?part_number=' . $part_number_get . '&id_empresa=' . $id_empresa_get);

        $this->include_js(array(
            'bootstrap3-typeahead.min.js',
            'bootstrap-select/bootstrap-select.min.js',
            'bootstrap-select/i18n/defaults-pt_BR.min.js',
            'sweetalert.min.js',
            'jquery.mask.min.js'
        ));

        $this->include_css(array(
            'bootstrap-select/bootstrap-select.css',
            'sweetalert.css',
            'autocomplete.css'
        ));

        $data['can_ncm_fornecedor'] = customer_can('ncm_fornecedor');
        $data['tags'] = $this->empresa_model->get_tags_view(sess_user_company(), NULL, NULL);
        $data['estabelecimentos']  = $this->empresa_model->get_estabelecimentos(sess_user_company());
        $data['usuarios_fiscal']   = $this->usuario_model->get_entries_by_role('fiscal', sess_user_company(), false, $entry->id_resp_fiscal);
        $data['usuarios_eng']      = $this->usuario_model->get_entries_by_role('engenheiro', sess_user_company(), false, $entry->id_resp_engenharia);

        $this->motivo_model->set_state('filter.id_perfil', $user->id_perfil);
        $data['motivos'] = $this->motivo_model->get_entries();
        
        $this->anexo_model->set_state('filter.tipo_anexo', 'ficha_tecnica');
        $data['ficha_tecnica'] = $this->anexo_model->get_entries($entry->part_number, $entry->estabelecimento, $entry->id_empresa);

        $this->render('cadastros/mestre_itens/editar', $data);
    }

    private function upload_ficha_tecnica($part_number, $estabelecimento, $id_empresa)
    {
        // Upload de Ficha Técnica
        $file_error = FALSE;

        if ($_FILES['anexo'] && $_FILES['anexo']['size'] > 0) {
            $config['upload_path']          = config_item('upload_anexos_path');
            $config['allowed_types']        = 'zip|rar|pdf|doc|docx|xls|xlsx|xlsb|jpg|gif|png|bmp|tiff|tif';
            $config['max_size']             = 20000;
            $config['encrypt_name']         = TRUE;
            $config['mod_mime_fix']         = FALSE;

            $this->load->library('upload', $config);

            if (!$this->upload->do_upload('anexo')) {
                $file_error = $this->upload->display_errors();
            } else {
                $file_data = $this->upload->data();

                $file_name = pathinfo($file_data['client_name'], PATHINFO_FILENAME);
                $file_ext = substr($file_data['file_ext'], 1);
                $file_size = ($file_data['file_size'] * 1024); // kb to bytes

                $dbdata = array(
                    'id_empresa'        => $id_empresa,
                    'part_number'       => $part_number,
                    'estabelecimento'   => $estabelecimento,
                    'tipo_anexo'        => 'ficha_tecnica',
                    'nome_arquivo'      => $file_name,
                    'extensao'          => $file_ext,
                    'nome_hash'         => $file_data['raw_name'],
                    'tamanho'           => $file_size
                );

                $this->db->insert('anexo', $dbdata);
            }
        }

        return $file_error;
    }

    private function handle_upload_max_size()
    {
        if (
            (isset($_SERVER['CONTENT_LENGTH']) && $_SERVER['REQUEST_METHOD'] == 'POST' && empty($_POST)) || (!empty($_FILES['anexo']['name']) && $_FILES['anexo']['size'] === 0)
        ) {
            $err  = "<h4>Ops... O seguinte erro aconteceu:</h4><ul>";
            $err .= "<li>O tamanho do arquivo enviado excede o limite máximo permitido.</li>";
            $err .= "</ul>";

            $this->message_next_render($err, 'error');

            redirect('cadastros/mestre_itens/novo');
        }
    }

    public function json_lista_grupos()
    {
        $this->load->model('grupo_tarifario_model');

        $tag    = $this->input->get('tag');

        $gts = $this->grupo_tarifario_model->get_entries_by_desc(false, $tag, array(array('order' => 'descricao', 'by' => 'asc')));

        $data = array();
        if (!empty($gts)) {
            foreach ($gts as $item) {
                $data[] = array(
                    'id' => $item->id_grupo_tarifario,
                    'name' => $item->descricao,
                    'ncm_recomendada' => $item->ncm_recomendada
                );
            }
        }

        response_json($data);
    }

    public function json_lista_tags()
    {
        $this->load->model('empresa_model');

        $search = $this->input->get('search');

        $id_empresa = sess_user_company();

        $tags = $this->empresa_model->get_tags_view($id_empresa, $search, 5);

        $data = array();

        if (!empty($tags)) {
            foreach ($tags as $item) {
                $data[] = $item->tag;
            }
        }

        response_json($data);
    }

    public function json_lista_itens()
    {
        $this->load->model('cad_item_model');
        $this->load->model('grupo_tarifario_model');

        $search = $this->input->get('search');

        if ($estabelecimento = $this->input->get('estabelecimento')) {
            $this->item_model->set_state('filter.estabelecimento', $estabelecimento);
        }

        if ($pn = $this->input->get('pn')) {
            $this->item_model->set_state('filter.exclude_part_number', $pn);
        }

        $this->item_model->set_state('filter.id_empresa', sess_user_company());

        $data['lista_status'] = $this->item_model->get_status();

        (!empty($search)) ? $this->item_model->set_search($search) : NULL;
        $list = $this->item_model->get_entries(5);

        $data = array();
        if (!empty($list)) {
            foreach ($list as $item) {
                $cad_item_fields = null;

                try {
                    $cad_item_fields = $this->cad_item_model->get_entry_by_pn($item->part_number, $item->id_empresa, $item->estabelecimento);

                    if (!empty($cad_item_fields->id_grupo_tarifario)) {
                        $grupo_tarifario = $this->grupo_tarifario_model->get_entry($cad_item_fields->id_grupo_tarifario);
                    }
                } catch (Exception $e) {
                    // do nothing
                }

                $newitem = array(
                    "name" => $item->part_number,
                    "id" => $item->descricao,
                    "descricao" => $item->descricao,
                    "ncm" => $item->ncm,
                    "estabelecimento" => $item->estabelecimento,
                    "tag" => $item->tag,
                    "campos_adicionais" => $item->campos_adicionais
                );

                if (isset($cad_item_fields->id_item)) {

                    if (isset($grupo_tarifario->id_grupo_tarifario)) {
                        $newitem["id_grupo_tarifario"] = $grupo_tarifario->id_grupo_tarifario;
                        $newitem["grupo_tarifario"] = $grupo_tarifario->descricao;
                    }

                    $newitem["funcao"] = $cad_item_fields->funcao;
                    $newitem['inf_adicionais'] = $cad_item_fields->inf_adicionais;
                    $newitem["aplicacao"] = $cad_item_fields->aplicacao;
                    $newitem["material_constitutivo"] = $cad_item_fields->material_constitutivo;
                    $newitem["marca"] = $cad_item_fields->marca;
                }

                $data[] = $newitem;
            }
        }

        response_json($data);
    }

    private function update_log_itens($post, $item, $part_number, $estabelecimento_get)
    {
        $this->load->model('usuario_model');
        $this->load->model('empresa_model');
        $this->load->model('empresa_prioridades_model');

        $empresa = $this->empresa_model->get_entry(sess_user_company());
        $data['campos_adicionais'] = explode("|", $empresa->campos_adicionais);
        $data['funcoes_adicionais'] = explode("|", $empresa->funcoes_adicionais);

        $can_formatar_texto = company_can("formatar_texto", $data['funcoes_adicionais']);

        if (isset($item->part_number) && !empty($item->part_number) && isset($item->id_empresa) && !empty($item->id_empresa)) {
            $item = $this->item_model->get_entry($item->part_number, $item->id_empresa, $item->estabelecimento);
        } else {

            return false;
        }

        $motivo = null;

        if (isset($post['part_number_similar']) && !empty($post['part_number_similar']) && isset($item->part_number_similar) && trim($post['part_number_similar']) != trim($item->part_number_similar)) {
            $motivo .= '<strong>Part Number Similar:</strong> ' . "<em>{$item->part_number_similar}</em> &rarr;" . $post['part_number_similar'] . '<br>';
        }

        if (isset($post['descricao']) && isset($item->descricao) && trim($post['descricao']) != trim($item->descricao)) {
            $motivo .= '<strong>Descrição:</strong> ' . "<em>{$item->descricao}</em> &rarr;" . $post['descricao'] . '<br>';
        }

        if (isset($post['ncm']) && isset($item->ncm) && trim($post['ncm']) != trim($item->ncm)) {
            $motivo .= '<strong>Ncm:</strong> ' . "<em>{$item->ncm}</em> &rarr;" . $post['ncm'] . '<br>';
        }

        if (isset($post['ncm_fornecedor']) && isset($item->ncm_fornecedor) && trim($post['ncm_fornecedor']) != trim($item->ncm_fornecedor)) {
            $motivo .= '<strong>Ncm do Fornecedor:</strong> ' . "<em>{$item->ncm_fornecedor}</em> &rarr;" . $post['ncm_fornecedor'] . '<br>';
        }

        if (isset($post['evento']) && isset($item->evento) && trim($post['evento']) != trim($item->evento)) {
            $motivo .= '<strong>Evento:</strong> ' . "<em>{$item->evento}</em> &rarr;" . $post['evento'] . '<br>';
        }

        if (isset($post['funcao']) && isset($item->funcao) && $post['funcao'] && trim($post['funcao']) != trim($item->funcao)) {
            $motivo .= '<br><strong>Função:</strong> ' . formatar_texto($can_formatar_texto, $post['funcao']) . '<br>';
        }

        if (isset($post['inf_adicionais']) && isset($item->inf_adicionais) && trim($post['inf_adicionais']) != trim($item->inf_adicionais)) {
            $motivo .= '<br><strong>Informações Adicionais:</strong> ' . formatar_texto($can_formatar_texto, $post['inf_adicionais']) . '<br>';
        }

        if (isset($post['aplicacao']) && isset($item->aplicacao) &&  trim($post['aplicacao']) != trim($item->aplicacao)) {
            $motivo .= '<br><strong>Aplicação:</strong> ' . formatar_texto($can_formatar_texto, $post['aplicacao']) . '<br>';
        }

        if (isset($post['marca']) && isset($item->marca) && trim($post['marca']) != trim($item->marca)) {
            $motivo .= '<br><strong>Marca:</strong> ' . formatar_texto($can_formatar_texto, $post['marca']) . '<br>';
        }

        if (isset($post['material_constitutivo']) && isset($item->material_constitutivo) &&  trim($post['material_constitutivo']) != trim($item->material_constitutivo)) {
            $motivo .= '<br><strong>Material Constitutivo:</strong> ' . formatar_texto($can_formatar_texto, $post['material_constitutivo']) . '<br>';
        }

        if (isset($post['peso']) && isset($item->peso) && trim($post['peso']) != trim($item->peso) && trim($post['peso']) != '0,000') {
            $motivo .= '<strong>Peso:</strong> ' . "<em>{$item->peso}</em> &rarr;" . $post['peso'] . '<br>';
        }

        $empresa_prioridades = $this->empresa_prioridades_model->get_entry($item->id_empresa);
        $old_prioridade = 'INDEFINIDO';
        $new_prioridade = 'INDEFINIDO';
        foreach ($empresa_prioridades as $p) {

            if (!empty($item->id_prioridade)) {
                if ($item->id_prioridade == $p->id_prioridade) {
                    $old_prioridade = $p->nome;
                }

                if ($post['prioridade'] == $p->id_prioridade) {
                    $new_prioridade = $p->nome;
                }
            }
        }
        if (trim($old_prioridade) != trim($new_prioridade)) {
            $motivo .= '<strong>Prioridade:</strong> ' . "<em>{$old_prioridade}</em> &rarr;" . $new_prioridade . '<br>';
        }

        if (isset($post['maquina']) && isset($item->maquina) && trim($post['maquina']) != trim($item->maquina)) {
            $motivo .= '<strong>Maquina:</strong> ' . "<em>{$item->maquina}</em> &rarr;" . $post['maquina'] . '<br>';
        }

        if (isset($post['origem']) && isset($item->origem) && trim($post['origem']) != trim($item->origem)) {
            $motivo .= '<strong>Origem:</strong> ' . "<em>{$item->origem}</em> &rarr;" . $post['origem'] . '<br>';
        }

        if (isset($post['observacoes']) && isset($item->observacoes) && trim($post['observacoes']) != trim($item->observacoes)) {
            $motivo .= '<strong>Observações:</strong> ' . "<em>{$item->observacoes}</em> &rarr;" . $post['observacoes'] . '<br>';
        }

        if (isset($post['memoria_classificacao']) && isset($item->memoria_classificacao) && trim($post['memoria_classificacao']) != trim($item->memoria_classificacao)) {
            $motivo .= '<strong>Memória de Classificação:</strong>' . "<em>{$item->memoria_classificacao}</em> &rarr;" . $post['memoria_classificacao'] . '<br>';
        }

        if (has_role('sysadmin') || has_role('cadastro_itens')) {
            if (isset($post['id_resp_engenharia']) && !empty($post['id_resp_engenharia'])  && isset($item->id_resp_engenharia) && $item->id_resp_engenharia != $post['id_resp_engenharia']) {
                $resp_engenharia = $this->usuario_model->get_entry($post['id_resp_engenharia']);
                $motivo .= '<br><strong>Responsável Engenharia alterado para:</strong>' . $resp_engenharia->nome . '<br>';
            }
        }

        if (isset($post['id_resp_fiscal']) && !empty($post['id_resp_fiscal'])  && isset($item->id_resp_fiscal) && $item->id_resp_fiscal != $post['id_resp_fiscal']) {
            $resp_fiscal = $this->usuario_model->get_entry($post['id_resp_fiscal']);
            $motivo .= '<br><strong>Responsável Fiscal alterado para:</strong>' . $resp_fiscal->nome . '<br>';
        }

        if (isset($post['id_grupo_tarifario']) && !empty($post['id_grupo_tarifario'])  && isset($item->id_grupo_tarifario) && trim($post['id_grupo_tarifario']) != trim($item->id_grupo_tarifario)) {
            $value = $post['id_grupo_tarifario'];
            $grupo = $this->grupo_tarifario_model->get_entry($value);

            $motivo .= "<br><strong>Grupo Tarifário:</strong> <em>{$item->grupo_tarifario_desc}</em> &rarr; {$grupo->descricao}<br> 
                        <strong>NCM Proposto:</strong> <em>{$item->ncm_proposto}</em> &rarr; {$grupo->ncm_recomendada}" . '<br>';
        }

        if (!empty($motivo)) {
            $this->load->model('item_log_model');

            $log_data['titulo']          = 'atualizacao';
            $log_data['motivo']          = $motivo;
            $log_data['part_number']     = $part_number;
            $log_data['estabelecimento'] = $estabelecimento_get;
            $log_data['id_usuario']      = sess_user_id();
            $log_data['id_empresa']      = sess_user_company();
            $log_data['criado_em']       = date('Y-m-d H:i:s');

            $this->item_log_model->save($log_data);
        }
    }

    public function habilitaFStatus($status)
    {
        if ($status == 'reprovado') {
            $this->db->query("SET @user_update_mestre_itens_status_reprovado = TRUE");
        }
    }

    public function desabilitaFStatus($status)
    {
        if ($status == 'reprovado') {
            $this->db->query("SET @user_update_mestre_itens_status_reprovado = NULL");
        }
    }

    public function update_item_ja_homologado()
    {

        $this->load->model('item_model');

        $result = $this->item_model->update_item_ja_homologado();

        if ($result) {
            echo "A atualização foi realizada com sucesso!";
        } else {
            echo "Não foi possível realizar a atualização!";
        }
    }

    public function check_descricao($str)
    {

        $descricao_global = $this->input->post('descricao_global');

        if ($str == '' && $descricao_global == '') {
            // se ambos 'descricao' e 'descricao_global' estiverem vazios, defina a mensagem de erro
            $this->form_validation->set_message('check_descricao', 'O campo %s ou Descrição Global tem que ser preenchido.');
            return FALSE;
        } else {
            return TRUE;
        }
    }

    public function ajax_get_dados_modal_paises()
    {

        $this->load->model(
            array(
                'empresa_model',
                'empresa_pais_model',
                'item_pais_model'
            )
        );

        $part_number_get     = $this->input->post('part_number');
        $id_empresa_get      = $this->input->post('id_empresa');
        $estabelecimento_get = $this->input->post('estabelecimento');

        $empresa             = $this->empresa_model->get_entry($id_empresa_get);

        $entry = $this->item_model->get_entry($part_number_get, $id_empresa_get, $estabelecimento_get);

        $data['entry']    =  $entry;

        $data['empresa_pais']    = $this->empresa_pais_model->get_entries_by_empresa($empresa->id_empresa);

        foreach ($data['empresa_pais'] as $key => $value) {
            $data['empresa_pais'][$key]->item_pais = $this->item_pais_model->get_entries_by_item($part_number_get, $id_empresa_get, $estabelecimento_get, $value->id_pais);
        }

        return response_json($data);
    }

    public function ajax_save_data_multi_paises()
    {
        $this->load->model('item_pais_model');

        $itens = $this->input->post('itens');
        $part_number = $this->input->post('partnumber');
        $estabelecimento = $this->input->post('estabelecimento');

        // Validar se os campos part_number e estabelecimento vierem vazios
        if (empty($part_number) || empty($estabelecimento)) {
            return response_json(array('status' => 'error', 'message' => 'Part Number e Estabelecimento são obrigatórios'));
        }

        $id_empresa = sess_user_company();

        //Exclui todos os regirstros existentes 
        $this->item_pais_model->delete_all_part_number($part_number, $estabelecimento, $id_empresa);

        // Cadastrar cada registro
        foreach ($itens as $item) {
            $this->item_pais_model->save($item);
        }

        return response_json(array('status' => 'success', 'message' => 'Dados salvos com sucesso'));
    }

    private function apply_default_filters($post = null, $per_page = null)
    {
        if ($post === null) {
            $post = [];
        }

        $this->handle_filter_reset();

        $filters_boolean = [
            'triagem_diana_falha' => ['is_checkbox_checked']
        ];

        foreach ($filters_boolean as $filter => $conditions) {
            $this->handle_boolean_filter($filter, $conditions);
        }

        $filters = [
            'evento' => ['is_not_empty', 'no_events'],
            'status' => ['is_not_empty'],
            'owner' => ['is_array', 'not_contains_negative_one'],
            'prioridade' => ['is_array', 'not_contains_negative_one'],
            'estabelecimento_modal' => ['is_array', 'not_contains_negative_one'],
            'ncm_proposta_modal' => ['is_array', 'not_contains_negative_one'],
            'sistema_origem_modal' => ['is_array', 'not_contains_negative_one'],
            'novo_status_atributos' => ['is_array', 'not_contains_negative_one'],
            'ex_ipi_modal' => ['is_array', 'not_contains_negative_one'],
            'ex_ii_modal' => ['is_array', 'not_contains_negative_one']
        ];

        foreach ($filters as $filter => $conditions) {
            $this->handle_array_filter($filter, $post, $per_page, $conditions);
        }

        $date_filters = [
            'data_ini',
            'data_fim',
            'data_inicio_modificacao_modal',
            'data_fim_modificacao_modal',
            'data_inicio_homologacao_modal',
            'data_fim_homologacao_modal',
            'data_inicio_importado_modal',
            'data_fim_importado_modal'
        ];

        foreach ($date_filters as $date_filter) {
            $this->handle_date_filter($date_filter);
        }

        $simple_filters = [
            'statusExportacao' => ['is_not_empty', 'not_equals_negative_one'],
            'novo_material_modal' => ['is_not_empty', 'not_equals_negative_one'],
            'novo_importado' => ['is_not_empty', 'not_equals_negative_one'],
            'descricao_completa_modal' => ['is_not_empty'],
            'descricao_global_modal' => ['is_not_empty'],
        ];

        foreach ($simple_filters as $filter => $conditions) {
            $this->handle_simple_filter($filter, $conditions);

            $filter_value = get_filter_value($filter, $this->item_model);
            if ($filter_value === null) {
                $this->item_model->unset_state("filter.{$filter}");
            }
        }

        $this->handle_filtered_state();
        $this->item_model->set_state('filter.id_empresa', sess_user_company());

        return [];
    }

    private function handle_filter_reset()
    {
        if ($this->input->is_set('reset_filters')) {
            $this->item_model->set_state_store_session(TRUE);
            $this->item_model->clear_states();
        } else {
            $this->item_model->set_state_store_session(TRUE);
            $this->item_model->restore_state_from_session('filter.', 'post');
        }
    }

    private function handle_array_filter($filter, $post, $per_page, $conditions)
    {
        $filter_value = get_filter_value($filter, $this->item_model);

        if ($filter === 'evento' && is_array($filter_value) && empty($filter_value[0])) {
            $this->item_model->unset_state("filter.{$filter}");
            return;
        }

        if (
            !empty($post[$filter]) &&
            $this->check_conditions($filter_value, $conditions)
        ) {
            $this->item_model->set_state("filter.{$filter}", $filter_value);
        } elseif (!$per_page && $post && empty($post[$filter])) {
            $this->item_model->unset_state("filter.{$filter}");
        } elseif (isset($post[$filter]) && is_array($post[$filter]) && in_array(-1, $post[$filter])) {
            $this->item_model->unset_state("filter.{$filter}");
        }
    }

    private function handle_date_filter($filter)
    {
        $date_str = get_filter_value($filter, $this->item_model);
        if (!empty($date_str)) {
            $date = date('Y-m-d', strtotime(str_replace('/', '-', $date_str)));
            $this->item_model->set_state("filter.{$filter}", $date);
        } else {
            $this->item_model->unset_state("filter.{$filter}");
        }
    }

    private function handle_simple_filter($filter, $conditions)
    {
        $filter_value = get_filter_value($filter, $this->item_model);

        if ($filter_value === null) {
            $this->item_model->unset_state("filter.{$filter}");
        } else if ($this->check_conditions($filter_value, $conditions)) {
            $this->item_model->set_state("filter.{$filter}", $filter_value);
        } else {
            $this->item_model->unset_state("filter.{$filter}");
        }
    }

    private function handle_boolean_filter($filter, $conditions)
    {
        // Para checkboxes, precisamos verificar se o formulário foi submetido
        // Se foi submetido mas o checkbox não está presente no POST, significa que foi desmarcado
        $is_form_submitted = $this->input->post('filtered') !== false;
        $post_value = $this->input->post($filter);

        if ($is_form_submitted) {
            // Formulário foi submetido
            // dd($post_value, $conditions);
            if ($this->check_conditions($post_value, $conditions)) {
                // Checkbox marcado e passou nas condições
                $this->item_model->set_state("filter.{$filter}", $post_value);
            } else {
                // Checkbox desmarcado ou não passou nas condições
                $this->item_model->unset_state("filter.{$filter}");
            }
        } else {
            // Formulário não foi submetido, manter estado anterior se existir
            $filter_value = $this->item_model->get_state("filter.{$filter}");
            if ($filter_value && $this->check_conditions($filter_value, $conditions)) {
                $this->item_model->set_state("filter.{$filter}", $filter_value);
            } else {
                $this->item_model->unset_state("filter.{$filter}");
            }
        }
    }

    private function check_conditions($value, $conditions)
    {
        foreach ($conditions as $condition) {
            if (!$this->$condition($value)) {
                return false;
            }
        }
        return true;
    }

    private function is_array($value)
    {
        return is_array($value);
    }

    private function not_contains_negative_one($value)
    {
        return !in_array(-1, $value);
    }

    private function is_not_empty($value)
    {
        return !empty($value);
    }

    private function not_equals_negative_one($value)
    {
        return $value != -1;
    }

    private function no_events($value)
    {
        return $value !== 'sem_evento';
    }

    private function is_checkbox_checked($value)
    {
        return $value == '1' || $value === 1 || $value === true;
    }

    private function handle_filtered_state()
    {
        if ($this->input->is_set('filtered')) {
            $this->item_model->set_state('filter.filtered', $this->input->post('filtered'));
        } else {
            $this->item_model->set_state('filter.filtered', 0);
        }
    }

    public function get_list_eventos()
    {
        try {
            $this->load->model([
                'cad_item_model',
            ]);

            $response = $this->cad_item_model->getPacoteEventos();

            if (!$response) {
                $response = [];
            }

            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($response));
        } catch (Exception $e) {
            $this->output
                ->set_status_header(500)
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'error' => true,
                    'message' => 'Erro ao carregar eventos: ' . $e->getMessage()
                ]));
        }
    }

    public function get_list_sistemas_origens()
    {
        try {

            $response = $this->item_model->get_list_sistemas_origens(sess_user_company());

            if (!$response) {
                $response = [];
            }

            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($response));
        } catch (Exception $e) {
            $this->output
                ->set_status_header(500)
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'error' => true,
                    'message' => 'Erro ao carregar sistemas de origem: ' . $e->getMessage()
                ]));
        }
    }

    public function get_list_ex_ipi_by_empresa()
    {
        try {
            $this->load->model([
                'cad_item_model',
            ]);

            $response = $this->cad_item_model->get_list_ex_ipi_by_empresa(sess_user_company());

            if (!$response) {
                $response = [];
            }

            $formatted_response = array_map(function ($item) {
                return [
                    'num_ex_ipi' => trim($item->num_ex_ipi),
                    'cod_ncm' => $item->cod_ncm,
                    'dat_vigencia_ini' => $item->dat_vigencia_ini,
                    'titulo_ex' => $item->titulo_ex
                ];
            }, $response);

            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($formatted_response));
        } catch (Exception $e) {
            $this->output
                ->set_status_header(500)
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'error' => true,
                    'message' => 'Erro ao carregar ex ipi: ' . $e->getMessage()
                ]));
        }
    }

    public function get_list_ex_ii_by_empresa()
    {
        try {
            $this->load->model([
                'cad_item_model',
            ]);

            $response = $this->cad_item_model->get_list_ex_ii_by_empresa(sess_user_company());

            if (!$response) {
                $response = [];
            }

            $formatted_response = array_map(function ($item) {
                return [
                    'num_ex_ii' => trim($item->num_ex_ii),
                    'cod_ncm' => $item->cod_ncm,
                    'dat_vigencia_ini' => $item->dat_vigencia_ini,
                    'titulo_ex' => $item->titulo_ex
                ];
            }, $response);

            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($formatted_response));
        } catch (Exception $e) {
            $this->output
                ->set_status_header(500)
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'error' => true,
                    'message' => 'Erro ao carregar ex ii: ' . $e->getMessage()
                ]));
        }
    }

    public function get_list_prioridades_by_empresa()
    {
        try {
            $this->load->model([
                'empresa_prioridades_model',
            ]);

            $response = $this->empresa_prioridades_model->get_entry(sess_user_company());

            if (!$response) {
                $response = [];
            }

            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($response));
        } catch (Exception $e) {
            $this->output
                ->set_status_header(500)
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'error' => true,
                    'message' => 'Erro ao carregar prioridades: ' . $e->getMessage()
                ]));
        }
    }

    public function get_list_ncms_by_empresa()
    {
        try {
            $this->load->model([
                'cad_item_model',
            ]);

            $response = $this->cad_item_model->get_list_ncm_proposto(sess_user_company());

            if (!$response) {
                $response = [];
            }

            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($response));
        } catch (Exception $e) {
            $this->output
                ->set_status_header(500)
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'error' => true,
                    'message' => 'Erro ao carregar NCMS: ' . $e->getMessage()
                ]));
        }
    }

    public function get_list_owners_by_empresa()
    {
        try {
            $this->load->model([
                'empresa_model',
            ]);

            $empresa = $this->empresa_model->get_entry(sess_user_company());

            $response = $this->empresa_model->get_owners_by_empresa($empresa);

            if (!$response) {
                $response = [];
            }

            $response = array_map(function ($owner) {
                return [
                    'codigo' => $owner->codigo,
                    'descricao' => $owner->descricao,
                    'nomes' => $owner->nomes
                ];
            }, $response);

            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($response));
        } catch (Exception $e) {
            $this->output
                ->set_status_header(500)
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'error' => true,
                    'message' => 'Erro ao carregar owners: ' . $e->getMessage()
                ]));
        }
    }
}
