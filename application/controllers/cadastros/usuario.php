<?php

class Usuario extends MY_Controller
{

    public function __construct()
    {
        parent::__construct();

        $this->load->model('usuario_model');

        if (!is_logged()) {
            redirect('/login');
        }

        if (!has_role('gerenciar_usuarios') && !has_role('cliente_pmo') && !has_role('consultor')) {
            show_permission();
        }

        $this->load->library('breadcrumbs');
    }

    public function index()
    {
        $data = array();
        $this->load->model('perfil_permissao_model');
        $this->usuario_model->set_state_store_session(TRUE);
        $this->usuario_model->restore_state_from_session();
        $entry_user = $this->usuario_model->get_entry(sess_user_id());
        $checkMultiplasEmpresas = $this->perfil_permissao_model->check_perfil_permissao_exists_slug($entry_user->id_perfil,  'permitir_multiplas_empresas');

        $query_str = '';

        $this->load->library('pagination');

        $data['nome'] = NULL;
        $data['id_empresa'] = sess_user_company();
        $data['checkMultiplasEmpresas'] = !empty($checkMultiplasEmpresas) ? TRUE : FALSE;
        if (has_role('sysadmin') || has_role("consultor") || ($checkMultiplasEmpresas)) {
            $this->load->model('empresa_model');
            $data['empresas'] = $this->empresa_model->get_all_entries();

            if ($this->input->is_set('id_empresa')) {
                $id_empresa = $this->input->get('id_empresa');

                if ($id_empresa) {
                    $this->usuario_model->set_state('filter.id_empresa', $id_empresa);
                } else {
                    $this->usuario_model->unset_state('filter.id_empresa');
                }

                $data['id_empresa'] = $this->usuario_model->get_state('filter.id_empresa');
            }
        }

        if ($this->input->is_set('nome') && $this->input->get('nome') != '') {
            $this->usuario_model->set_state('filter.nome', $this->input->get('nome'));
            $data['nome'] = $this->input->get('nome');
        } else {
            $data['nome'] = NULL;
            $this->usuario_model->unset_state('filter.nome');
        }

        if ($this->input->is_set('reset_filter')) {
            $this->usuario_model->clear_states();
        }

        $this->usuario_model->set_state('filter.id_empresa', $data['id_empresa']);

        $page = $this->input->get('per_page');
        $limit = 15;

        $total_entries = $this->usuario_model->get_total_entries();

        if (!has_role('sysadmin') && !has_role('consultor') && !has_role('visualizar_usuarios_adm')) {
            $this->usuario_model->set_state('filter.perfil', array(2, 3));
        }

        $data['list'] = $this->usuario_model->get_entries($limit, ($page > 0 ? $page - 1 : 0) * $limit);

        $config['base_url'] = base_url("cadastros/usuario");
        $config['use_page_numbers'] = TRUE;
        $config['total_rows'] = $total_entries;
        $config['per_page'] = $limit;
        $config['page_query_string'] = TRUE;
        $config['num_links'] = 5;

        $this->pagination->initialize($config);

        $data['pagination'] = $this->pagination->create_links();

        $this->title = "Usuários";

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Usuários', '/cadastros/usuario/');

        $this->render('cadastros/usuario/default', $data);
    }

    public function excluir()
    {
        if (!has_role('gerenciar_usuarios') && !has_role('cliente_pmo')) {
            show_permission();
        }

        $id_list = $this->input->post('id_list');
        $return = array();
        $return['status'] = FALSE;

        if ($this->usuario_model->remove($id_list)) {
            $return['status'] = TRUE;
            $this->message_next_render('Exclusão realizada com sucesso!');
        }

        echo json_encode($return);
    }

    public function exportar($id_empresa)
    {
        if (empty($id_empresa))
        {
            return;
        }
        
        $this->usuario_model->exportar($id_empresa);
    }

    public function novo()
    {
        $this->load->model(array('usuario_model', 'perfil_permissao_model'));

        if (!has_role('gerenciar_usuarios') && !has_role('cliente_pmo')) {
            show_permission();
        }

        $this->load->model(array('empresa_model', 'perfil_model'));
        $this->load->helper('common_helper');

        $this->title = "Usuários &gt; Novo";

        $data = array();
        $entry_user = $this->usuario_model->get_entry(sess_user_id());
        $checkMultiplasEmpresas = $this->perfil_permissao_model->check_perfil_permissao_exists_slug($entry_user->id_perfil,  'permitir_multiplas_empresas');

        if (has_role('sysadmin') || ($checkMultiplasEmpresas)) {
            $data['empresas'] = $this->empresa_model->get_all_entries();
        } else {
            $data['empresas'] = $this->usuario_model->get_empresas_by_user(sess_user_id());
        }

        $data['perfis'] = $this->perfil_model->get_all_entries();

        if ($this->input->post('submit')) {
            $this->load->library('form_validation');

            $this->form_validation->set_rules('nome', 'Nome', 'trim|required');
            $this->form_validation->set_rules('email', 'E-mail', 'trim|required|min_length[5]|is_unique[usuario.email]');
            // $this->form_validation->set_rules('senha', 'Senha', 'trim|required|min_length[3]');
            $this->form_validation->set_rules('id_perfil', 'Perfil', 'trim|required');

            if (has_role('sysadmin')) {
                $this->form_validation->set_rules('id_empresa', 'Empresa', 'required');
            }

            if ($this->form_validation->run() == TRUE) {
                $id_empresa = $this->input->post('id_empresa') ? $this->input->post('id_empresa') : sess_user_company();
                $perfil = $this->input->post('id_perfil');

                // $senha_hash = $this->usuario_model->password_hash($this->input->post('senha'));

                $data = array(
                    'nome' => remove_special_char_str($this->input->post('nome')),
                    'email' => $this->input->post('email'),
                    // 'senha' => $senha_hash,
                    'id_perfil'  => $this->input->post('id_perfil'),
                    'recebe_email_pendencias' => $this->input->post('recebe_email_pendencias'),
                    'criado_em' => date('Y-m-d H:i:s'),
                    'criado_por' => sess_user_id()
                );

                if (is_array($id_empresa)) {
                    $data['id_empresa'] = $id_empresa[0];
                } else {
                    $data['id_empresa'] = $id_empresa;
                }

                $id_usuario = $this->usuario_model->save($data);

                if (customer_has_role('engenheiro', $id_usuario) || customer_has_role('fiscal', $id_usuario)) {
                    $this->usuario_model->save_usuario_empresas($id_usuario, $id_empresa);
                }

                if ($this->input->is_set('gerente_de_projetos')) {
                    //Se perfil Cliente-PMO
                    if (customer_has_role('cliente_pmo', $id_usuario)) {
                        $this->empresa_model->save_gp($id_usuario, $id_empresa);
                    }
                }

                $this->message_next_render('Sucesso! Usuário [<strong>' . $this->input->post('email') . '</strong> adicionado]');

                redirect('cadastros/usuario');
            } else {
                $err = "<h4>Ops... alguns erros aconteceram</h4><ul>" . validation_errors('<li>', '</li>') . "</ul>";
                $this->message_on_render($err, 'error');
            }
        }

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Usuários', '/cadastros/usuario/');
        $this->breadcrumbs->push('Novo usuário', '/cadastros/usuario/novo/');

        $this->include_js('bootstrap-select/bootstrap-select.js');
        $this->include_css('bootstrap-select/bootstrap-select.css');

        $this->render('cadastros/usuario/novo', $data);
    }

    public function get_mail_becomex()
    {
        $id_empresa = $this->input->post('id_empresa');

        $lists = $this->usuario_model->get_mail($id_empresa);

        $result = [];
        foreach ($lists as $k => $list)
        {
            
            $result[] = $list->email;
        }
      
        return response_json($result);
    }

    public function save_config_mail()
    {
        $post = $this->input->post();
        $data = [];
        $count = 0;
        $id_empresa = $post['id_empresa'];
        
        $lista_email     = explode(",",$post['analise_mail']);
        $estabelecimento = $post['analise_estabelecimento'];
        $notificar_resp  = $post['analise_enviar_usu'];
       
        $status          = 'em_analise';

        foreach ($lista_email as $email)
        {
            if (empty($email))
            {
                continue;
            }
            $count++;

            $data[$count]['id_empresa']      = $id_empresa;
            $data[$count]['status']          = $status;
            $data[$count]['notificar_resp']  = $notificar_resp;
            $data[$count]['estabelecimento'] = $estabelecimento;
            $data[$count]['email']           = $email;
            $data[$count]['id_usuario']      = $this->usuario_model->get_usuario_mail($email)->id_usuario;
        }

        $lista_email     = explode(",",$post['p_info_mail']);
        $estabelecimento = $post['p_info_estabelecimento'];
        $notificar_resp  = $post['p_info_enviar_usu'];
        $status          = 'pendente_duvidas';

        foreach ($lista_email as $email)
        {
            if (empty($email))
            {
                continue;
            }

            $count++;

            $data[$count]['id_empresa']      = $id_empresa;
            $data[$count]['status']          = $status;
            $data[$count]['notificar_resp']  = $notificar_resp;
            $data[$count]['estabelecimento'] = $estabelecimento;
            $data[$count]['email']           = $email;
            $data[$count]['id_usuario']      = $this->usuario_model->get_usuario_mail($email)->id_usuario;
        }

        $lista_email     = explode(",",$post['homologado_mail']);
        $estabelecimento = $post['homologado_estabelecimento'];
        $notificar_resp  = $post['homologado_enviar_usu'];
        $status          = 'homologado';

        foreach ($lista_email as $email)
        {
            if (empty($email))
            {
                continue;
            }

            $count++;

            $data[$count]['id_empresa']      = $id_empresa;
            $data[$count]['status']          = $status;
            $data[$count]['notificar_resp']  = $notificar_resp;
            $data[$count]['estabelecimento'] = $estabelecimento;
            $data[$count]['email']           = $email;
            $data[$count]['id_usuario']      = $this->usuario_model->get_usuario_mail($email)->id_usuario;
        }

        $lista_email     = explode(",",$post['reprovado_mail']);
        $estabelecimento = $post['reprovado_estabelecimento'];
        $notificar_resp  = $post['reprovado_enviar_usu'];
        $status          = 'nao_homologado';

        foreach ($lista_email as $email)
        {
            if (empty($email))
            {
                continue;
            }

            $count++;

            $data[$count]['id_empresa']      = $id_empresa;
            $data[$count]['status']          = $status;
            $data[$count]['notificar_resp']  = $notificar_resp;
            $data[$count]['estabelecimento'] = $estabelecimento;
            $data[$count]['email']           = $email;
            $data[$count]['id_usuario']      = $this->usuario_model->get_usuario_mail($email)->id_usuario;
        }


        $result = $this->usuario_model->save_config_mail($data, $id_empresa);

        return response_json($result);
    }

    public function editar($id_usuario)
    {
        if (!has_role('gerenciar_usuarios') && !has_role('cliente_pmo')) {
            show_permission();
        }
        
        $this->load->model(array('empresa_model', 'perfil_model', 'perfil_permissao_model'));
        $this->load->helper('common_helper');

        $data = array();

        $this->title = "Usuários &gt; Novo";

        $entry_user = $this->usuario_model->get_entry(sess_user_id());

        try {
            $empresas_logado = $empresas_usuario = array();

            $entry = $this->usuario_model->get_entry($id_usuario);
            $data['entry'] = $entry;

            if (!has_role('sysadmin')) {
                $acesso_empresas_logado = $this->usuario_model->get_empresas_by_user(sess_user_id());
                $acesso_empresas_usuario = $this->usuario_model->get_empresas_by_user($entry->id_usuario);

                foreach ($acesso_empresas_logado as $acesso_empresa_logado) {
                    $empresas_logado[] = $acesso_empresa_logado->id_empresa;
                }

                foreach ($acesso_empresas_usuario as $acesso_empresa_usuario) {
                    $empresas_usuario[] = $acesso_empresa_usuario->id_empresa;
                }

                $checkPerfilPermissao = $this->perfil_permissao_model->check_perfil_permissao_exists_slug($entry_user->id_perfil,  'gerenciar_usuarios');

               // if (!in_array($entry->id_perfil, array(2, 3)) || !array_intersect($empresas_logado, $empresas_usuario)) {
                if (!$checkPerfilPermissao) {

                    $this->message_next_render('Você não possui permissões suficientes.', 'error');
                    redirect('cadastros/usuario');
                }
            }
        } catch (Exception $e) {
            show_error($e->getMessage());
        }

        $checkMultiplasEmpresas = $this->perfil_permissao_model->check_perfil_permissao_exists_slug($entry_user->id_perfil,  'permitir_multiplas_empresas');

        if (has_role('sysadmin') || ($checkMultiplasEmpresas)) {
            $data['empresas'] = $this->empresa_model->get_all_entries();
        } else {
            $data['empresas'] = $this->usuario_model->get_empresas_by_user(sess_user_id());
        }

        $data['perfis'] = $this->perfil_model->get_all_entries();

        $usuario = $this->usuario_model->get_entry($id_usuario);
        $empresa = $this->empresa_model->get_entry($usuario->id_empresa);

        if ($this->input->post('submit')) {
            $this->load->library('form_validation');

            $this->form_validation->set_rules('nome', 'Nome', 'trim|required');
            $unique = '';

            if ($entry->email != $this->input->post('email')) {
                $unique = "|is_unique[usuario.email]";
            }

            $this->form_validation->set_rules('email', 'E-mail', 'trim|required|min_length[5]' . $unique);
            $this->form_validation->set_rules('id_perfil', 'Perfil', 'trim|required');

            if (has_role('sysadmin')) {
                $this->form_validation->set_rules('id_empresa', 'Empresa', 'required');
            }

            if ($this->form_validation->run() == TRUE) {
                $id_empresa = $this->input->post('id_empresa') ? $this->input->post('id_empresa') : sess_user_company();
                $perfil = $this->input->post('id_perfil');
                $recebe_email_alteracao_status = !empty($this->input->post('recebe_email_alteracao_status')) ? 1 : 0;
                $data = array(
                    'nome' => remove_special_char_str($this->input->post('nome')),
                    'email' => $this->input->post('email'),
                    'id_perfil'  => $perfil,
                    'recebe_email_pendencias' => $this->input->post('recebe_email_pendencias'),
                    'recebe_email_alteracao_status' => $recebe_email_alteracao_status,
                    'atualizado_por' => sess_user_id(),
                    'atualizado_em' => date('Y-m-d H:i:s')
                );

                if (is_array($id_empresa)) {
                    $data['id_empresa'] = $id_empresa[0];
                } else {
                    $data['id_empresa'] = $id_empresa;
                }

                $this->usuario_model->save($data, array('id_usuario' => $id_usuario));
                $this->usuario_model->remove_usuario_empresas($id_usuario, $empresas_logado);

                if (
                    customer_has_role('engenheiro', $id_usuario) || 
                    customer_has_role('fiscal', $id_usuario) || 
                    customer_has_role('permitir_multiplas_empresas', $id_usuario)
                ) {
                    $this->usuario_model->save_usuario_empresas($id_usuario, $id_empresa);
                }

                if ($this->input->is_set('gerente_de_projetos')) {
                    //Se perfil Cliente-PMO
                    if (customer_has_role('cliente_pmo', $id_usuario)) {
                        $this->empresa_model->save_gp($id_usuario, $id_empresa);
                    } else {
                        if ($empresa->id_gerente_de_projetos == $id_usuario) {
                            $this->empresa_model->save_gp(NULL, $id_empresa);
                        }
                    }
                } else if ($empresa->id_gerente_de_projetos == $id_usuario) {
                    $this->empresa_model->save_gp(NULL, $id_empresa);
                }


                $this->message_next_render('Sucesso! Usuário [<strong>' . $this->input->post('email') . '</strong> atualizado]', 'success');

                redirect('cadastros/usuario');
            } else {
                $err = "<h4>Ops... alguns erros aconteceram</h4><ul>" . validation_errors('<li>', '</li>') . "</ul>";
                $this->message_on_render($err, 'error');
            }
        }

        $data['id_gerente_de_projetos'] = $empresa->id_gerente_de_projetos;

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Usuários', '/cadastros/usuario/');
        $this->breadcrumbs->push('Editar usuário', '/cadastros/usuario/editar/' . $id_usuario);

        if (!$data['usuario_empresas'] = $this->usuario_model->get_empresas_by_user($id_usuario, TRUE)) {
            $data['usuario_empresas'][] = $data['entry']->id_empresa;
        }

        $this->include_js('bootstrap-select/bootstrap-select.js');
        $this->include_css('bootstrap-select/bootstrap-select.css');

        $this->render('cadastros/usuario/editar', $data);
    }

    public function verificarPermissaoPerfil() {
        $idPerfil = $this->input->post('idPerfil');
        $permissao = $this->input->post('permissao');

        $this->load->model('perfil_permissao_model');

        echo json_encode(array(
            'hasPermissao' => $this->perfil_permissao_model->check_perfil_permissao_exists_slug($idPerfil, $permissao)
        ));

        exit;
    }
}
