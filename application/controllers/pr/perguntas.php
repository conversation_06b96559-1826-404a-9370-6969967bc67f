<?php if (!defined('BASEPATH')) exit('No direct script access allowed');

class Perguntas extends MY_Controller
{

    public function __construct()
    {
        parent::__construct();

        if (!is_logged()) {
            $this->unsetUsuarioBloqueadorItens();
            redirect('/login');
        }

        $this->load->library('breadcrumbs');

        $this->load->model(array(
            'ctr_pergunta_model',
            'ctr_grupo_model',
            'item_model',
            'usuario_model'
        ));

        $this->load->helper('common_helper');

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Perguntas', '/pr/perguntas');

        $this->ctr_grupo_model->set_state_store_session(TRUE);
    }

    public function index()
    {
        $data = array();

        $this->render('atribuir_grupo/index', $data);
    }

    public function permissaoDeletarPerguntas()
    {
       return customer_has_role('excluir_perguntas_indevidas', sess_user_id());
    }

    public function permissaoGrupoPerguntas()
    {
       return customer_has_role('selecionar_grupo_perguntas', sess_user_id()) ? true : false;
    }

    public function getPerguntasGrupo($idGrupo = null)
    {
        $this->load->model("ctr_anexo_pergunta_model");

        try {
            if (empty($idGrupo)) {
                return response_json(array(
                    'data' => array(),
                    'msg' => 'Nenhum grupo informado',
                    'error' => true
                ), 400);
            }

            $this->ctr_grupo_model->set_state('filter.id_grupo', $idGrupo);

            $perguntas = $this->ctr_grupo_model->getPerguntas();

            foreach ($perguntas as $pergunta) {
                $pergunta->arquivos = $this->ctr_anexo_pergunta_model->getEntriesByPergunta($pergunta->id);
            }

            return response_json(array(
                'data' => $perguntas,
                'msg' => 'Lista de perguntas relacionadas ao grupo',
                'error' => false
            ), 200);
        } catch (Exception $e) {
            return response_json(array(
                'data' => array(),
                'msg' => 'Erro ao buscar as perguntas',
                'error' => true
            ), 400);
        }
    }

    public function getGrupos() 
    {
        try {

            $this->ctr_grupo_model->set_state('filter.search', $this->input->get('search'));
            $this->ctr_grupo_model->unset_state('filter.id_grupo');

            $perguntasGrupo = $this->ctr_grupo_model->getEntries();

            return response_json(array(
                'msg' => "Lista de grupos pelo filtro {$this->input->get('search')}",
                'data' => $perguntasGrupo
            ), 200);
        } catch (Exception $e) {
            return response_json(array(
                'data' => array(),
                'msg' => 'Erro ao pesquisar os grupos',
                'error' => true
            ), 400);
        }
    }

    public function getPartnumbers()
    {

        $data =  get_axios_post();
        $searchArray = !empty($data['searchArray']) ? $data['searchArray'] : null;
        $searchArrayEstabelecimentos = !empty($data['searchArrayEstabelecimentos']) ? $data['searchArrayEstabelecimentos'] : null;
 
        try {
            $this->item_model->set_state('filter.id_empresa', sess_user_company());
            $this->item_model->set_state('filter.search', $this->input->get('search'));
            $this->item_model->set_state('filter.searchArray', $searchArray);
            $this->item_model->set_state('filter.searchArrayEstabelecimentos', $searchArrayEstabelecimentos);

            $limit =  !empty($searchArray) ? null : 50;
            $partnumbers = $this->item_model->get_entries_pendente_atribuicao($limit);

            return response_json(array(
                'msg' => "Lista de partnumbers da empresa",
                'data' => $partnumbers
            ), 200);
        } catch (Exception $e) {
            return response_json(array(
                'data' => array(),
                'msg' => 'Erro ao pesquisar os grupos',
                'error' => true
            ), 400);
        }
    }

    public function getResponsaveis()
    {
        try {
            $this->usuario_model->set_state('filter.id_empresa', sess_user_company());
            $this->usuario_model->set_state('filter.search', $this->input->get('search'));

            $usuarios = $this->usuario_model->get_entries();

            return response_json(array(
                'msg' => "Lista de usuários da empresa",
                'data' => $usuarios
            ), 200);
        } catch (Exception $e) {
            return response_json(array(
                'data' => array(),
                'msg' => 'Erro ao pesquisar os usuários',
                'error' => true
            ), 400);
        }
    }

    public function getResponsavelPartnumber()
    {
        $data =  get_axios_post();
        $partnumbers = !empty($data['partnumbers']) ? $data['partnumbers'] : null;
        $estabelecimentos = !empty($data['estabelecimentos']) ? $data['estabelecimentos'] : null;
        try {
            $this->item_model->set_state('filter.id_empresa', sess_user_company());
            $this->item_model->set_state('filter.part_numbers', $partnumbers);
            $this->item_model->set_state('filter.estabelecimentos',  $estabelecimentos);

            $usuarioResponsavel = $this->item_model->get_usuario_responsavel_pn();
            
            $itens_bloqueados_terceiro = $this->item_model->set_usuario_bloqueador_itens(sess_user_company(), $partnumbers, $estabelecimentos);

            if($itens_bloqueados_terceiro[0]['status'] == 'itens_bloqueados_terceiro'){
                $msg = '';
                foreach($itens_bloqueados_terceiro[0]['itens'] as $item){
                    $msg .= 'O item: "'. $item['part_number'] . '" está bloqueado pelo usuário<br/> <strong>'.$item['nome'].'</strong><br/>';
                }
                $msg .= "Qualquer necessidade, entrar em contato com o usuário correspondente.";

                return response_json(array(
                    'data' => $itens_bloqueados_terceiro[0],
                    'msg' => $msg,
                    'error' => true
                ), 200);
            }else{
                return response_json(array(
                    'data' => $usuarioResponsavel,
                    'msg' => 'Primeiro usuário encontrado responsável pelos PARTNUMBERS informados',
                    'error' => false
                ), 200);
            }

            
        } catch (Exception $e) {
            return response_json(array(
                'data' => array(),
                'msg' => 'Erro ao buscar responsável',
                'error' => true
            ), 400);
        }
    }

    public function retrocederStatus($part_number_array,$item)
    {
        $id_empresa = sess_user_company();
        $this->load->model('item_model');
        foreach ($part_number_array as $pn)
        {
            $iteb_db = $this->item_model->check_item_status($pn,$item['estabelecimento'],$id_empresa);

            if ($iteb_db->slug == 'respondido')
            {
                $this->load->library("Item/Status");
                $this->status->set_status("pendente_duvidas");
                $this->status->update_item($iteb_db->part_number,$item['estabelecimento'],$id_empresa);
            }

        }

        return true;
    }

    public function deletarPergunta($item_delete = null)
    {
        $this->load->model('item_model');
        $id_empresa = sess_user_company();
        $item_delete =  $this->input->get('partnumbers');

        if (empty($item_delete))
            return false;

        $item_delete_utf8 = mb_convert_encoding($item_delete, 'UTF-8');

        if (is_array($item_delete_utf8)) {
            $item = (object) $item_delete_utf8;
        } else {
            $item = json_decode($item_delete_utf8);
        }
        
        if (isset($item->ids) && !empty($item->ids))
        {
            $ids_array = explode(",", $item->ids);
        }

        $pergunta = $item->pergunta;
        $db_delete = ['id_pergunta'=> $item->id_pergunta, 'part_number'=> $item->part_number, 'estabelecimento'=> $item->estabelecimento];
        $log = [];

        $part_number_array = explode(",", $db_delete['part_number']);
        $pn = !empty($part_number_array) ? reset($part_number_array) : $item->part_number;
        $item_db = $this->item_model->get_entry($pn, $id_empresa, $item->estabelecimento);

        if ($item->tipo_exclusao == 'Resposta')
        {
            $this->db->where_in('part_number' ,$part_number_array);
            $result =  $this->db->delete( 'ctr_resposta', ['estabelecimento' => $db_delete['estabelecimento'], 'id_pergunta' => $db_delete['id_pergunta'] ] );
            $log['delete'] = $result;

            $this->db->where_in('part_number' ,$part_number_array);
            if (empty($db_delete['estabelecimento']))
            {
                $this->db->where("('estabelecimento' IS NULL OR estabelecimento = '')",null,false);
            } else {
                $this->db->where('estabelecimento' ,$db_delete['estabelecimento']);
            }
            $this->db->where('id' ,$db_delete['id_pergunta']);
           // $this->db->where(['estabelecimento' => $db_delete['estabelecimento'], 'id' => $db_delete['id_pergunta'] ]);
            $result = $this->db->update('ctr_pendencias_pergunta', array('pendente' => 1));
            $log['update'] = $result;
            $log['email'][] =  $this->notificarPerguntaPendente($part_number_array,$db_delete,$pergunta);

            $this->retrocederStatus($part_number_array,$db_delete);

        } else {
            if (isset($ids_array) && !empty($ids_array))
            {
                $this->db->where_in('id' ,$ids_array);
                $result =  $this->db->delete('ctr_pendencias_pergunta' );
                $log['delete_db1'][] = $result;
            }
            
            // $this->db->where_in('part_number' ,$part_number_array);
            // $result =  $this->db->delete( 'ctr_resposta', ['estabelecimento' => $db_delete['estabelecimento'], 'id_pergunta' => $db_delete['id_pergunta'] ] );
            // $log['delete_db2'][] = $this->db->last_query();
        }

        $this->load->library("Item/Status");
        foreach ($part_number_array as $pn_unico)
        {
            $this->status->check_status_item_perguntas(
                            $pn_unico,
                            $item->estabelecimento,
                            $item_db->id_status,
                            true // Incluído identificador de exclusão de pergunta
                        );
        }
       // $this->status->check_status_item_perguntas($item->part_number, $item->estabelecimento, $item_db->id_status );

        $log['tipo'] = $item->tipo_exclusao;

        return response_json($log);
    }

    public function notificarPerguntaPendente($part_number_array,$db_delete,$pergunta)
    {
        $this->load->model('usuario_model');
        $this->load->model('empresa_model');

        $this->db->select('rel.id_responsavel,ctr.id_empresa');
        $this->db->where_in('ctr.part_number',$part_number_array);
        $this->db->where(['ctr.estabelecimento' => $db_delete['estabelecimento'], 'ctr.id' => $db_delete['id_pergunta'] ]);
        $this->db->join('rel_perguntas_responsavel rel', 'rel.id_ctr_pendencias = ctr.id', 'inner');

        $query = $this->db->get('ctr_pendencias_pergunta ctr',1);
        $query->row();       

         $db_result =  $query->row();

        if (empty($db_result->id_responsavel))
            return;

        $empresa = $this->empresa_model->get_entry($db_result->id_empresa);
        $usu = $this->usuario_model->get_entry($db_result->id_responsavel);

        $data_mail['userName'] = $usu->nome;
        $data_mail['base_url'] = config_item('online_url') . '/';
        $data_mail['part_number'] = $db_delete['part_number'];
        $data_mail['estabelecimento'] = $db_delete['estabelecimento'];
        $data_mail['pergunta'] = $pergunta;
        $data_mail['empresaNome'] = $empresa->nome_fantasia;

        $this->load->library('email');

        $body = $this->load->view('templates/notificacao_pergunta_sem_resposta', $data_mail, true);

        $this->email->from(config_item('mail_from_addr'));
        $this->email->to($usu->email);

        $this->email->subject('[Gestão Tarifária] - Favor Responder novamente o item '.$data_mail['part_number']);
        $this->email->message($body);
       return $this->email->send();

    }

    public function getPermissao()
    {
      return response_json(customer_has_role('excluir_perguntas_respostas',sess_user_id()) ? 1 : 0);
    }

    public function salvarPerguntas()
    {
        try {
            $post = get_axios_post();
            
            $responsaveis = $post['responsavel'];
            $partnumbers = $post['partnumbers'];
            $perguntas = $post['perguntas'];
            $grupoPerguntas = $post['grupoPerguntas'];
            // var_dump($grupoPerguntas['key'], 'teste'); exit;
            $owners = $post['owner'];
            $tipoResponsavel = $post['tipoResponsavel'];
            // var_dump($tipoResponsavel); exit;

            // var_dump($owners); exit;

            $this->load->model('ctr_pendencias_pergunta_model', 'item_model');

            $resp = $this->ctr_pendencias_pergunta_model->salvarPendenciasPerguntas(
                $partnumbers, 
                $perguntas, 
                $responsaveis,
                $owners,
                $tipoResponsavel,
                $grupoPerguntas
            );

            if (isset($responsaveis[0]))
                $responsaveis = $responsaveis[0];

            if ($resp->status) {
                $this->ctr_pendencias_pergunta_model->gerarEmailPendenciasPerguntas($partnumbers, $responsaveis);
            }

            $this->item_model->unset_usuario_bloqueador_itens();

            return response_json(array(
                'msg' => $resp ? 'As perguntas foram enviadas ao usuário responsável' : 'Por favor, verifique os campos enviados',
                'error' => !$resp,
                'errors' => $resp->errors
            ), 200);
        
        } catch (Exception $e) {
            return response_json(array(
                'msg' => $e->getMessage(),
                'error' => true
            ), 400);
        }
    }
    
    public function getAvailableInfPartnumbers()
    {
        try {
            $this->load->model(array(
                'info_partnumber_model'
            ));

            return response_json(array(
                'msg' => 'Informações configuradas para adicionar ao PART NUMBER',
                'error' => false,
                'data' => $this->info_partnumber_model->getAvailableInfPartnumbers()
            ), 200);
        
        } catch (Exception $e) {
            return response_json(array(
                'msg' => 'Ocorreu um erro ao buscar as informações',
                'error' => true
            ), 400);
        }
    }

    public function getPerguntasPendentesAgrupadasPn()
    {
        try {
            $pns_get = $this->input->get('partnumbers');
            $pns = json_decode($pns_get);

            if (!(json_last_error() === JSON_ERROR_NONE)) {
                $pns = new stdClass();
                $pns->item = $pns_get;
            }
            if (empty($pns->item)) {
                return response_json(array(
                    'msg' => 'É obrigatório informar ao menos um Part Number',
                    'error' => true
                ), 400);
            }

            if (count($pns->item) > 1)
            {
                $variosItens = true;
            } else {
                $variosItens = false;
            }

            $this->load->model(array('ctr_pendencias_pergunta_model', 'ctr_anexo_pergunta_model'));
            $this->ctr_pendencias_pergunta_model->set_state_store_session(TRUE);
            $this->ctr_pendencias_pergunta_model->set_state('filter.partnumbers', $pns->item);
            $this->ctr_pendencias_pergunta_model->set_state('filter.estabelecimento', $pns->estabelecimento);
            // if ($variosItens == true)
            // {
            //     $perguntas = $this->ctr_pendencias_pergunta_model->getPerguntasPendentesAgrupadasPn();
            // } else {
            //     $perguntas = $this->ctr_pendencias_pergunta_model->getPerguntasPendentesAgrupadasPnUnico();
            // }
            $perguntas = $this->ctr_pendencias_pergunta_model->getPerguntasPendentesAgrupadasPn(NULL,NULL,FALSE,TRUE);
            foreach ($perguntas as $pergunta) {
                $pergunta->arquivos = $this->ctr_anexo_pergunta_model->getEntriesByPergunta($pergunta->id_pergunta);
            }

            return response_json(array(
                'msg' => 'Lista de perguntas agrupadas',
                'error' => false,
                'data' => $perguntas
            ), 200);
        
        } catch (Exception $e) {
            return response_json(array(
                'msg' => 'Ocorreu um erro ao salvar as informações',
                'error' => true
            ), 400);
        }
    }

    public function getHistorico()
    {
        $this->load->model('ctr_anexo_resposta_model');
        
        try {
            $partnumber = $this->input->post('partnumber');

            $this->ctr_pendencias_pergunta_model->set_state('filter.partnumbers', $partnumber);
            $perguntas = $this->ctr_pendencias_pergunta_model->getHistoricoPerguntas();

            foreach ($perguntas as $k => $pergunta) {
                $pergunta->arquivo = $this->ctr_anexo_resposta_model->getEntriesByResposta($pergunta->id_resposta);
            }

            $this->load->view('controle_pendencias/historico-pergunta', array('perguntas' => $perguntas));
        } catch (Exception $e) {
            
        }
    }

    public function getOwners()
    {
        try {
            $this->load->model('empresa_model');

            $empresa = $this->empresa_model->get_entry(sess_user_company());

            $owners = $this->empresa_model->get_owners_by_empresa($empresa);

            return response_json(array(
                'msg' => 'Lista de owners',
                'error' => false,
                'data' => $owners
            ), 200);
        
        } catch (Exception $e) {
            return response_json(array(
                'msg' => 'Ocorreu um erro ao buscar as informações',
                'error' => true
            ), 400);
        }
    }

    public function getOwnerPartnumber()
    {
        $data =  get_axios_post();
        $partnumbers = !empty($data['partnumbers']) ? $data['partnumbers'] : null;
        try {
            $this->load->model('owner_model');
            $part_number = $partnumbers;
            // var_dump($part_number[0]);exit();

            $ownerPartNumber = $this->owner_model->getOwnerOfItem($part_number[0]);

            // var_dump($ownerPartNumber);exit();

            return response_json(array(
                'msg' => 'Owner do item 1',
                'error' => false,
                'data' => $ownerPartNumber
            ), 200);
        
        } catch (Exception $e) {
            return response_json(array(
                'msg' => 'Ocorreu um erro ao buscar as informações',
                'error' => true
            ), 400);
        }
    }

    public function getOwnerOfUserLogged()
    {
        try {
            $this->load->model('owner_model');
            // $id_usuario = $this->input->get('id_usuario');
            $id_user_logado = sess_user_id();

            // var_dump($id_user_logado);exit();

            $ownerOfUser = $this->owner_model->getOwnerOfUserLogged($id_user_logado);

            // var_dump($ownerOfUser);exit();

            return response_json(array(
                'msg' => 'Owner do usuário',
                'error' => false,
                'data' => $ownerOfUser
            ), 200);
        
        } catch (Exception $e) {
            return response_json(array(
                'msg' => 'Ocorreu um erro ao buscar as informações',
                'error' => true
            ), 400);
        }
    }

    public function setUsuarioBloqueadorItens()
    {
        try {
            $this->load->model(array(
                'item_model'
            ));

            $post = $this->input->post();
            $itens = $post['itens'];

            $partnumbers = [];
            $estabelecimentos = [];

            if (!empty($itens))
            {
                foreach($itens as  $item) {  

                    $partnumbers[] = $item['part_number'];
                    $estabelecimentos[] =  $item['estabelecimento'];

                }

                return response_json(array(
                    'msg' => 'Itens Bloqueados com sucesso!',
                    'error' => false,
                    'data' => $this->item_model->set_usuario_bloqueador_itens(sess_user_company(), $partnumbers , $estabelecimentos)
                ), 200);
            }
            return false;
        
        } catch (Exception $e) {
            return response_json(array(
                'msg' => 'Ocorreu um erro ao desboquear itens',
                'error' => true
            ), 400);
        }
    }

    public function unsetUsuarioBloqueadorItens()
    {
        try {
            $this->load->model(array(
                'item_model'
            ));

            return response_json(array(
                'msg' => 'Itens Desbloqueados com sucesso!',
                'error' => false,
                'data' => $this->item_model->unset_usuario_bloqueador_itens()
            ), 200);
        
        } catch (Exception $e) {
            return response_json(array(
                'msg' => 'Ocorreu um erro ao desboquear itens',
                'error' => true
            ), 400);
        }
    }

    public function getUsuarioBloqueador()
    {
        try {
            $part_number = $this->input->get('part_number');
            $estabelecimento = $this->input->get('estabelecimento');
      
            if (empty($part_number)) {
                return response_json(array(
                    'msg' => 'É obrigatório informar ao menos um Part Number',
                    'error' => true
                ), 400);
            }

            $this->load->model(array('item_model'));
          
            //Verifica se o item está bloqueado por algum terceiro
            $itens_bloqueados_terceiro = $this->item_model->get_usuario_bloqueador_item($part_number ,sess_user_company(), $estabelecimento);

            if($itens_bloqueados_terceiro[0]['status'] == 'itens_bloqueados_terceiro'){

                $msg = '';
                foreach($itens_bloqueados_terceiro[0]['itens'] as $item){
                    $msg .= 'O item: "'. $item['part_number'] . '" está bloqueado pelo usuário<br/> <strong>'.$item['nome'].'</strong><br/>';
                }
                $msg .= "Qualquer necessidade, entrar em contato com o usuário correspondente.";

                return response_json(array(
                    'data' => $itens_bloqueados_terceiro,
                    'msg' => $msg,
                    'error' => true
                ), 200);
            }

            return response_json(array(
                'msg' => 'Sem impedimentos!',
                'error' => false,
                'data' =>  null
            ), 200);
        
        } catch (Exception $e) {
            return response_json(array(
                'msg' => 'Ocorreu um erro ao salvar as informações',
                'error' => true
            ), 400);
        }
    }

}
