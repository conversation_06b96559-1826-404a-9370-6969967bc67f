<?php

/**
 * @property MY_Input $input
 * @property CI_Loader $load
 * @property Cad_item_wf_atributo_model $cad_item_wf_atributo_model
 * @property Produto_model $produto_model
 * @property Cad_item_attr_model $cad_item_attr_model
 * @property Empresa_model $empresa_model
 * @property Usuario_model $usuario_model
 * @property Cad_item_model $cad_item_model
 * @property Item_model $item_model
 * @property Ncm_model $ncm_model
 * @property Email_queue_model $email_queue_model
 * @property Log_wf_atributos_model $log_wf_atributos_model
 * @property Empresa_prioridades_model $empresa_prioridades_model
 * @property CI_Breadcrumbs $breadcrumbs
 * @property CI_Output $output
 * @property CI_Form_validation $form_validation
 * @property CI_Pagination $pagination
 * @property Atributo $atributo
 * 
 */
class Atributos extends MY_Controller
{

    public $ncm = 0;
    public $title = '';
    public function __construct()
    {
        parent::__construct();

        $this->load->model('cad_item_wf_atributo_model');

        if (!is_logged()) {
            redirect('/login');
        }

        // if (!has_role('gerenciar_atributos')) {
        //     show_permission();
        // }

        // enable profiler para debug
        // $this->output->enable_profiler(true);

        $this->load->library('breadcrumbs');
        $this->load->helper('formatador_helper');
        $this->load->model('item_model');
        $this->load->model('empresa_prioridades_model');
    }

    private function get_post_values()
    {
        $post = \json_decode(file_get_contents("php://input"), true);

        if (\json_last_error()) {
            return false;
        }

        return $post;
    }

    public function get_data_attr()
    {
        $this->load->model('catalogo/produto_model');
        $this->load->model('cad_item_attr_model');
        $this->load->model('empresa_model');
        $this->load->model('cad_item_model');


        $id_usuario = sess_user_id();
        $id_empresa = sess_user_company();
        $movimentar = false;
        $editar = false;
        $homologar = false;

        if (customer_has_role('preencher_atributos_workflow', $id_usuario) ) {
            $editar = true;
        } 
        if (customer_has_role('homologar_atributos_workflow', $id_usuario) ) {
            $homologar = true;
        } 
        if (customer_has_role('movimentar_itens_workflow', $id_usuario) ) {
            $movimentar = true;
        }

        $empresa = $this->empresa_model->get_entry($id_empresa);
        $funcoes_adicionais = explode('|', $empresa->funcoes_adicionais);

        $diana_atributos = false;
        if (in_array("diana_atributos", $funcoes_adicionais)) {
            $diana_atributos = true;
        }

        //$permissionType = 'fill_homolog_view';
        $post = $this->get_post_values();
        $offset = null;
        if (!empty($post['ncm'])) {
            $this->ncm = $post['ncm'];
            if (!empty($post['per_page'])) {
                $offset = $post['per_page'];
            }
        }

        $ncm_item = $this->get_required_attrs($this->produto_model->get_attr_ncm($this->ncm));

        // Usar limite dinâmico baseado no parâmetro items_per_page, com fallback para 10
        $limit = !empty($post['items_per_page']) ? (int)$post['items_per_page'] : 10;

        $itens = $this->cad_item_wf_atributo_model->get_selected_itens_ncm($this->ncm, $limit, $offset);
        if (!empty($itens['array_itens'])) {
            foreach ($itens['array_itens'] as $k => $item) {
                $perguntasRespostas = "";

                $historicoItem = $this->cad_item_wf_atributo_model->getHistoricoItem($item->part_number, $item->estabelecimento);

                if (!empty($historicoItem)) {
                    foreach ($historicoItem as $h => $historico) {
                        $perguntasRespostas .= ($h + 1) . " - {$historico->pergunta}\n";
                        $perguntasRespostas .= "R: " . (!empty($historico->resposta) ? "$historico->resposta \n\n" : " \n\n");
                    }
                }
                $itens['array_itens'][$k]->perguntasRespostas = $perguntasRespostas;
            }
        }

        $id_itens = $itens['id_itens'];
        $lista = [];
        $all_default_attrs = $this->cad_item_attr_model->get_attr_multiple($id_itens);

        if (empty($all_default_attrs) || isset($post['per_page'])) {
            foreach ($id_itens as $id_item) {
                $grupo_tarifario_item = $this->cad_item_model->get_grp_tarif_item($id_item);

                if (!empty($grupo_tarifario_item)) {
                    $this->load->model('produto_model');

                    $ncm_item["assocAttrs"] = $this->create_assoc_attrs_structure($ncm_item);

                    $this->cad_item_model->verificar_atributos_default($ncm_item["assocAttrs"], $id_item, $grupo_tarifario_item);
                }
            }

            $all_default_attrs = $this->cad_item_attr_model->get_attr_multiple($id_itens);
        }

        $ncm_item["defaultAttrs"] = $all_default_attrs; //$this->cad_item_attr_model->get_attr('1426979');
        $lista = $this->create_assoc_attrs_structure($ncm_item);

        if ($itens['permissao'] != 'importado') {
            $homologar = false;
        }
        $permissionType = null;
        if ($homologar && $editar && $movimentar) {
            $permissionType = 'fill_homolog_view';
        } elseif ($editar && $movimentar) {
            $permissionType = 'homolog_view';
        } elseif ($editar || $movimentar) {
            $permissionType = 'fill_view';
        }

        return response_json(
            [
                "lista"                 => $lista,
                "itens"                 => $itens['array_itens'],
                "permissionType"        => $permissionType,
                "homologar_atributos"   => $homologar,
                "preencher_atributos"   => $editar,
                "movimentar_itens"      => $movimentar,
                "diana_atributos"     => $diana_atributos
            ],
            200
        );
    }

    private function create_assoc_attrs_structure($ncm_item)
    {
        if (empty($ncm_item)) {
            throw new \Exception("Item NCM nao informado ou invalido");
        }

        $ncm_item = \json_decode(\json_encode($ncm_item), TRUE); // Força a conversão para array.

        $arr_dbdata = isset($ncm_item["defaultAttrs"]) ? $ncm_item["defaultAttrs"] : [];   // Os dados do banco.
        $arr_attr   = $ncm_item["listaAtributos"]; // Os atributos do JSON.

        $this->assoc_recursively($arr_dbdata, $arr_attr, NULL); // Executa a associação.

        return $arr_attr;
    }

    private function assoc_recursively($arr_dbdata, &$arr_attr, $parent_attr = NULL)
    {
        if (empty($arr_attr)) {
            throw new \Exception("Dados invalidos para associacao");
        }

        if (empty($arr_dbdata)) {
            $arr_dbdata = [];
        }

        foreach ($arr_attr as &$attr) {
            $attr_template = !empty($attr["atributo"]) ? $attr["atributo"] : $attr; // Cria o template com base no atributo alvo.

            foreach ($arr_dbdata as $dbdata) {

                //  $attr_template["dbdata"][$dbdata['id_item']] = [ "codigo" => "" ]; // Por padrão adiciona a propiedade dbdata e codigo.
                if (
                    !empty($parent_attr)
                    && !empty($dbdata["atributo_pai"])
                    && $dbdata["atributo_pai"] == $parent_attr["codigo"]
                    && $dbdata["atributo"] == $attr_template["codigo"]
                ) {
                    $attr_template["dbdata"][$dbdata['id_item']] = $dbdata;
                } else if ($dbdata["atributo"] == $attr_template["codigo"]) {
                    $attr_template["dbdata"][$dbdata['id_item']] = $dbdata;
                }
            }

            if ($attr_template["atributoCondicionante"] && !empty($attr_template["condicionados"])) {
                $this->assoc_recursively($arr_dbdata, $attr_template["condicionados"], $attr_template);
            } else if (\strtoupper($attr_template["formaPreenchimento"]) == "COMPOSTO" && !empty($attr_template["listaSubatributos"])) {
                $this->assoc_recursively($arr_dbdata, $attr_template["listaSubatributos"], $attr_template);
            }

            if (!empty($attr["atributo"])) {
                $attr["atributo"] = $attr_template;
            } else {
                $attr = $attr_template;
            }
        }
    }

    private function get_required_attrs($listaAtributos)
    {
        if (empty($listaAtributos)) {
            throw new \Exception("Item NCM nao informado ou invalido");
        }

        $listaAtributos = \json_decode(\json_encode($listaAtributos), TRUE); // Força a conversão para array.

        $ncm_item = [];
        $ncm_item['listaAtributos'] = $listaAtributos;

        \array_multisort($ncm_item["listaAtributos"]);

        return $ncm_item;
    }

    public function fake_data()
    {
        $data = $this->cad_item_wf_atributo_model->fake_data(18);
        return response_json([
            "data"    => $data
        ], 200);
    }

    public function save_attrs()
    {
        $this->load->model(array('cad_item_model'));
        $this->cad_item_model->_attr_default_item = [];
        $post = $this->get_post_values();

        if (!empty($post['raw_attrs'])) {
            $this->cad_item_wf_atributo_model->save_attrs($post['raw_attrs'], $post['itens']);
            return response_json([
                "data"    => 'ok'
            ], 200);
        }
    }

    public function get_data_company()
    {
        $id_usuario = sess_user_id();
        $id_empresa = sess_user_company();
        $this->load->model('empresa_model');
        $this->load->model('usuario_model');
        $dados_usuario = $this->usuario_model->get_entry($id_usuario);

        $email_usuario = $dados_usuario->email ?? null;

        $funcoes_adicionais = $this->empresa_model->get_funcoes_adicionais(null, $id_empresa);
        if (in_array('diana_atributos', $funcoes_adicionais)) {
            return response_json([
                "integra_diana_company"    => true,
                "email_usuario"            => $email_usuario
            ], 200);
        } else {
            return response_json([
                "integra_diana_company"    => false,
                "email_usuario"            => $email_usuario
            ], 200);
        }
    }
    public function requestDiana()
    {

        header('Content-Type: application/json');
        $post = \json_decode(file_get_contents("php://input"), true);
        $descricao = $post['descricao'] ?? null;
        $ncm = $post['ncm'] ?? null;
        $codigo = $post['codigo'] ?? null;
        $checar_info_na_desc = $post['checar_info_na_desc'] ?? false;
        $busca_trechos = $post['busca_trechos'] ?? false;
        $busca_razao = $post['busca_razao'] ?? false;
        $request_email = $post['request_email'] ?? null;

        $url = 'http://172.21.128.120/buscar_valor_atributo_codigo';
        $data = [
            "codigo" => $codigo,
            "descricao" => $descricao,
            "checar_info_na_desc" => $checar_info_na_desc,
            "busca_trechos" => $busca_trechos,
            "busca_razao" => $busca_razao,
            "request_email" => $request_email
        ];

        $payload = json_encode($data);

        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            "Accept: application/json",
            "Content-Type: application/json"
        ]);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);

        $response = curl_exec($ch);

        if (curl_errno($ch)) {
            echo 'Erro: ' . curl_error($ch);
        } else {
            echo $response;
        }

        curl_close($ch);
    }

    private function handle_filter_reset()
    {
        if ($this->input->is_set('reset_filters')) {
            $this->cad_item_wf_atributo_model->set_state_store_session(true);
            $this->cad_item_wf_atributo_model->clear_states();
        } else {
            $this->cad_item_wf_atributo_model->set_state_store_session(true);
            $this->cad_item_wf_atributo_model->restore_state_from_session('filter.', 'post');
        }
    }

    public function index()
    {
        $this->load->model(array('cad_item_model', 'empresa_model'));

        $post       = $this->input->post();
        $per_page   = $this->input->get('per_page');

        $exclui_status = [1, 7];

        $this->handle_filter_reset();

        $data = [];

        if ($this->input->post('filtered')) {
            $this->load->library('pagination');
        }

        //Status de classificação fiscal
        if (!empty($post['status_classificacao_fiscal'])) {
            $status_classificacao_fiscal = $post['status_classificacao_fiscal'];
            if (is_array($status_classificacao_fiscal) && $status_classificacao_fiscal[0] != "") {
                $this->cad_item_wf_atributo_model->set_state('filter.status_classificacao_fiscal', $status_classificacao_fiscal);
            } else {
                $this->cad_item_wf_atributo_model->unset_state('filter.status_classificacao_fiscal');
            }
        } elseif (!$per_page && $post && empty($post['status_classificacao_fiscal'])) {
            $this->cad_item_wf_atributo_model->unset_state('filter.status_classificacao_fiscal');
        }

        //Evento
        if (!empty($post['evento'])) {
            $evento = $this->input->post('evento');
            if (is_array($evento) && $evento[0] != "") {
                $this->cad_item_wf_atributo_model->set_state('filter.evento', $evento);
            } else {
                $this->cad_item_wf_atributo_model->unset_state('filter.evento');
            }
        } elseif (!$per_page && $post && empty($post['evento'])) {
            $this->cad_item_wf_atributo_model->unset_state('filter.evento');
        }

        //Prioridade
        $empresa_prioridade_filter = [];
        $empresa_prioridade_filter = $post['prioridade'] ?? [];
        if (!is_array($empresa_prioridade_filter)) {
            $empresa_prioridade_filter = [$empresa_prioridade_filter];
        }
        if (!empty($empresa_prioridade_filter) && !in_array(-1, $empresa_prioridade_filter)) {
            $this->cad_item_wf_atributo_model->set_state('filter.prioridade', $empresa_prioridade_filter);
        } elseif (!$per_page && $post && empty($post['prioridade'])) {
            $this->cad_item_wf_atributo_model->unset_state('filter.prioridade');
        } else {
            $this->cad_item_wf_atributo_model->unset_state('filter.prioridade');
        }

        //NCM Proposto
        $ncm_proposta = $post['ncm_proposta'] ?? [];
        if (!is_array($ncm_proposta)) {
            $ncm_proposta = [$ncm_proposta];
        }
        if (!empty($ncm_proposta) && !in_array(-1, $ncm_proposta)) {
            $this->cad_item_wf_atributo_model->set_state('filter.ncm_proposta', $ncm_proposta);
        } elseif (!$per_page && $post && empty($post['ncm_proposta'])) {
            $this->cad_item_wf_atributo_model->unset_state('filter.ncm_proposta');
        }

        //Status Atributos
        if (!empty($post['status_atributos'])) {
            $status_atributos = $this->input->post('status_atributos');
            if (is_array($status_atributos) && $status_atributos[0] != "") {
                $this->cad_item_wf_atributo_model->set_state('filter.status_atributos', $status_atributos);
            } else {
                $this->cad_item_wf_atributo_model->unset_state('filter.status_atributos');
            }
        } elseif (!$per_page && $post && empty($post['status_atributos'])) {
            $this->cad_item_wf_atributo_model->unset_state('filter.status_atributos');
        }

        //Status de Integração
        if (!empty($post['status_integracao'])) {
            $status_integracao = $this->input->post('status_integracao');
            if (is_array($status_integracao) && $status_integracao[0] != "") {
                $this->cad_item_wf_atributo_model->set_state('filter.status_integracao', $status_integracao);
            } else {
                $this->cad_item_wf_atributo_model->unset_state('filter.status_integracao');
            }
        } elseif (!$per_page && $post && empty($post['status_integracao'])) {
            $this->cad_item_wf_atributo_model->unset_state('filter.status_integracao');
        }

        //Status de Preenchimento
        if (!empty($post['status_preenchimento'])) {
            $status_preenchimento = $this->input->post('status_preenchimento');
            if (is_array($status_preenchimento) && $status_preenchimento[0] != "") {
                $this->cad_item_wf_atributo_model->set_state('filter.status_preenchimento', $status_preenchimento);
            } else {
                $this->cad_item_wf_atributo_model->unset_state('filter.status_preenchimento');
            }
        } elseif (!$per_page && $post && empty($post['status_preenchimento'])) {
            $this->cad_item_wf_atributo_model->unset_state('filter.status_preenchimento');
        }

        //Owner
        $owner_filter = [];
        $owner_filter = $post['owner'] ?? [];
        if (!is_array($owner_filter)) {
            $owner_filter = [$owner_filter];
        }
        // Se o owner for uma string vazia, remove o filtro
        elseif (is_string($owner_filter) && empty($owner_filter)) {
            $this->cad_item_wf_atributo_model->unset_state('filter.owner');
        }
        if (!empty($owner_filter) && !in_array(-1, $owner_filter)) {
            $this->cad_item_wf_atributo_model->set_state('filter.owner', $owner_filter);
        } elseif (!$per_page && $post && empty($post['owner'])) {
            $this->cad_item_wf_atributo_model->unset_state('filter.owner');
        }

        //Responsável
        $responsavel_filter = [];
        $responsavel_filter = $post['responsavel'] ?? [];
        if (!is_array($responsavel_filter)) {
            $responsavel_filter = [$responsavel_filter];
        }
        if (!empty($responsavel_filter) && !in_array(-1, $responsavel_filter)) {
            $this->cad_item_wf_atributo_model->set_state('filter.responsavel', $responsavel_filter);
        } elseif (!$per_page && $post && empty($post['responsavel'])) {
            $this->cad_item_wf_atributo_model->unset_state('filter.responsavel');
        }

        //Estabelecimento
        $estabelecimento = $post['estabelecimento'] ?? [];
        if (!is_array($estabelecimento)) {
            $estabelecimento = [$estabelecimento];
        }
        if (!empty($estabelecimento) && !in_array(-1, $estabelecimento)) {
            $this->cad_item_wf_atributo_model->set_state('filter.estabelecimento', $estabelecimento);
        } elseif (!$per_page && $post && empty($post['estabelecimento'])) {
            $this->cad_item_wf_atributo_model->unset_state('filter.estabelecimento');
        }

        //Objetivos
        $objetivos_filter = $post['objetivos']  ?? [];
        if (!is_array($objetivos_filter)) {
            $objetivos_filter = [$objetivos_filter];
        }
        if (!empty($objetivos_filter) && !in_array(-1, $objetivos_filter)) {
            $this->cad_item_wf_atributo_model->set_state('filter.objetivos', $objetivos_filter);
        } elseif (!$per_page && $post && empty($post['objetivos'])) {
            $this->cad_item_wf_atributo_model->unset_state('filter.objetivos');
        }

        //Pesquisar
        $search = $post['search'];
        if (!empty($search)) {
            $this->cad_item_wf_atributo_model->set_state("filter.search", $search);
            $data['search'] = $search; // valor original, htmlentities só na view
        } elseif (!$per_page && $post && empty($post['search'])) {
            $this->cad_item_wf_atributo_model->unset_state("filter.search");
            $data['search'] = '';
        }

        $data_inicio_importado_modal = $post['data_inicio_importado_modal'];
        if (!empty($data_inicio_importado_modal)) {
            $this->cad_item_wf_atributo_model->set_state("filter.data_inicio_importado_modal", $data_inicio_importado_modal);
            $data['data_inicio_importado_modal'] = $data_inicio_importado_modal; 
        } elseif (!$per_page && $post && empty($post['data_inicio_importado_modal'])) {
            $this->cad_item_wf_atributo_model->unset_state("filter.data_inicio_importado_modal");
            $data['data_inicio_importado_modal'] = '';
        }
        
        $data_fim_importado_modal = $post['data_fim_importado_modal'];
        if (!empty($data_fim_importado_modal)) {
            $this->cad_item_wf_atributo_model->set_state("filter.data_fim_importado_modal", $data_fim_importado_modal);
            $data['data_fim_importado_modal'] = $data_fim_importado_modal; // valor original, htmlentities só na view
        } elseif (!$per_page && $post && empty($post['data_fim_importado_modal'])) {
            $this->cad_item_wf_atributo_model->unset_state("filter.data_fim_importado_modal");
            $data['data_fim_importado_modal'] = '';
        }

        //Filtered
        if ($this->input->is_set('filtered')) {
            $this->cad_item_wf_atributo_model->set_state('filter.filtered', $this->input->post('filtered'));
        } else {
            $this->cad_item_wf_atributo_model->set_state('filter.filtered', 0);
        }

        //tipo_item
        if ($this->input->is_set('tipo_item')) {
            $this->cad_item_wf_atributo_model->set_state('filter.tipo_item', $this->input->post('tipo_item'));
        } elseif (!$per_page && $post && empty($post['tipo_item'])) {
            $this->cad_item_wf_atributo_model->set_state('filter.tipo_item', 'importado');
        }

        $this->title = "Análise de Atributos";

        $total_entries = 0;
        $data['list'] = [];
        $data['pagination'] = [];
        $data['total_ncms'] = 0;
        $data['total_itens'] = 0;
        if ($this->cad_item_wf_atributo_model->get_state('filter.filtered')) {

            $this->load->library('pagination');
            $limit = 15;
            $offset = $per_page;
            $total_entries = $this->cad_item_wf_atributo_model->get_entries(null, null, false, true, $exclui_status);
            $data['list'] = $this->cad_item_wf_atributo_model->get_entries($limit, $offset, false, null, $exclui_status);

            // Verifica se o usuário pode homologar itens
            $can_homologar = customer_has_role('homologar_atributos_workflow', sess_user_id());
            if ($this->cad_item_wf_atributo_model->get_state('filter.tipo_item') != 'importado') {
                $can_homologar = false;
            }

            $data['can_homologar'] = $can_homologar ? 1 : 0;

            $search = urlencode($search);

            if (!is_null($owner_filter)) {
                $owner_filter = http_build_query($owner_filter);
            }
            $config['base_url'] = base_url("wf/atributos");
            $config['uri_segment'] = 3;
            //$config['use_page_numbers'] = TRUE;
            $config['total_rows'] = $total_entries['num_rows'];
            $data['total_rows'] = $total_entries['num_rows'];
            $config['per_page'] = $limit;
            $config['page_query_string'] = true;
            $config['num_links'] = 5;
            $this->pagination->initialize($config);

            $data['pagination'] = $this->pagination->create_links();

            $data['total_ncms'] = $total_entries['num_rows'];
            $data['total_itens'] = $total_entries['total_itens'];
        } else {
            $data['itens'] = [];
            $data['query_homolog'] = '';
            $data['total_rows'] = 0;
        }

        // Puxar os status da Biblioteca Atributos criada
        $this->load->library("Item/Atributo");
        $id_empresa_sess = sess_user_company();

        if ($this->cad_item_wf_atributo_model->get_state('filter.tipo_item') == 'nacional') {
            $status_class_fiscais = $this->item_model->get_status();
            //   $data['eventos'] =  $this->cad_item_wf_atributo_model->get_list_eventos_nacionais($id_empresa_sess);
            // $data['ncm_propostos'] = $this->cad_item_wf_atributo_model->get_list_ncm_proposto_nacionais($id_empresa_sess);
            // $data['empresa_prioridades'] = $this->cad_item_wf_atributo_model->get_list_empresa_prioridades_nacionais($id_empresa_sess);
            //   $data['responsavel_todos'] = $this->cad_item_wf_atributo_model->get_list_responsavel_nacionais($id_empresa_sess);
        } else {
            $status_class_fiscais = $this->item_model->get_status();
            // $data['eventos'] =  $this->cad_item_wf_atributo_model->get_list_eventos($id_empresa_sess);
            //  $data['ncm_propostos'] = $this->cad_item_wf_atributo_model->get_list_ncm_proposto($id_empresa_sess);
            // $data['empresa_prioridades'] = $this->cad_item_wf_atributo_model->get_list_empresa_prioridades($id_empresa_sess);
            //  $data['responsavel_todos'] = $this->cad_item_wf_atributo_model->get_list_responsavel($id_empresa_sess);
        }

        foreach ($status_class_fiscais as $key => $value) {
            $status = $status_class_fiscais[$key]['status'];
            $status_formatado = $status_class_fiscais[$key]['status_formatado'];
            $status_class_fiscais[$key]['status'] = $status_formatado;
            $status_class_fiscais[$key]['slug'] = $status;

            // Retirar se o status_formatado for o slug Inativo
            if ($status_formatado == 'Inativo') {
                unset($status_class_fiscais[$key]);
            }
        }
        $data['status_class_fiscais'] =  $status_class_fiscais;

        $data['objetivos'] =  $this->cad_item_wf_atributo_model->get_list_objetivos();
        $data['status_todos_atributos'] =  $this->atributo->get_all_status();
        $data['status_todos_integracoes'] =  $this->atributo->get_all_status_integracao();
        $data['estabelecimentos'] = $this->empresa_model->get_estabelecimentos($id_empresa_sess);


        $empresa = $this->empresa_model->get_entry($id_empresa_sess);
        $data['campos_adicionais'] = explode("|", $empresa->campos_adicionais);

        $owners = $this->empresa_model->get_owners_by_empresa($empresa);
        $data['owners'] = $owners;

        $hasDescricaoGlobal = in_array('descricao_global', $data['campos_adicionais']);
        $data['hasDescricaoGlobal'] = $hasDescricaoGlobal;

        $hasOwner = in_array('owner', $data['campos_adicionais']);
        $data['hasOwner'] = $hasOwner;

        $funcoes_adicionais = explode('|', $empresa->funcoes_adicionais);

        $data['has_vinculacao_atr'] = in_array('vinculacao_atr', $funcoes_adicionais) ? true : false;

        $data['has_flag_missing_mandatory'] = in_array('homologacao_atributos_incompletos', $funcoes_adicionais)
            ? 1
            : 0;

        // var_dump($data['has_flag_missing_mandatory']); die;

        // // Se o filtro model estiver vazio
        // if (empty($this->cad_item_wf_atributo_model->get_state('filter.status_atributos'))) {
        //     // Se o usuario tiver permissao correta
        //     if (customer_has_role('movimentar_itens', sess_user_id()) == 1) {
        //         // Inicia o campo com os seguintes valores // Análise de atributos - Fiscal //Revisão //Revisão PUCOMEX
        //         $array_start = array(0 => 2, 1 => 5, 2 => 6);
        //         $this->cad_item_wf_atributo_model->set_state('filter.status_atributos', $array_start); 
        //     }
        // }

        $userData = $this->usuario_model->get_entry(sess_user_id());
        $user = new stdClass();
        $user->id_usuario = sess_user_id();
        $user->id_empresa = $userData->id_empresa;
        $user->id_perfil = $userData->id_perfil;

        $data['user'] = $user;


        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Análise de Atributos', '/wf/atributos');

        $this->include_css(array('b3-datetimepicker.min.css', 'bootstrap-select/bootstrap-select.css', 'bootstrap-select/bootstrap-select.min.css', 'sweetalert.css', 'animate.css'));
        $this->include_js(array('b3-datetimepicker.min.js', 'bootstrap-select/bootstrap-select.js', 'bootstrap-notify.min.js', 'jquery.cookie.js', 'bootstrap-select/bootstrap-select.min.js', 'sweetalert.min.js', 'jquery.mask.min.js'));

        $this->render('wf/default', $data);
    }


    public function get_list_responsaveis()
    {
        $id_empresa_sess = sess_user_company();
        $response = $this->cad_item_wf_atributo_model->get_list_responsavel_nacionais($id_empresa_sess);

        // Define o header corretamente
        header('Content-Type: application/json');

        // Impede que qualquer whitespace ou output antes/fora do JSON seja enviado
        echo json_encode($response);
        exit;
    }

    public function get_list_prioridades()
    {
        $id_empresa_sess = sess_user_company();
        $response = $this->empresa_prioridades_model->get_entry($id_empresa_sess);
        // Define o header corretamente
        header('Content-Type: application/json');

        // Impede que qualquer whitespace ou output antes/fora do JSON seja enviado
        echo json_encode($response);
        exit;
    }

    public function get_list_eventos()
    {
        $id_empresa_sess = sess_user_company();
        $response = $this->cad_item_wf_atributo_model->get_list_eventos($id_empresa_sess);

        // Define o header corretamente
        header('Content-Type: application/json');

        // Impede que qualquer whitespace ou output antes/fora do JSON seja enviado
        echo json_encode($response);
        exit;
    }

    public function get_list_ncms()
    {
        $id_empresa_sess = sess_user_company();
        $response = $this->cad_item_wf_atributo_model->get_list_ncm_proposto($id_empresa_sess);

        // Define o header corretamente
        header('Content-Type: application/json');

        // Impede que qualquer whitespace ou output antes/fora do JSON seja enviado
        echo json_encode($response);
        exit;
    }

    /**
     * Busca todas as NCMs com dados completos (incluindo total de itens e IDs)
     * respeitando os filtros aplicados, para seleção em massa
     */
    public function get_all_ncms_for_bulk_selection()
    {
        $this->load->model(array('cad_item_model', 'empresa_model'));

        // Restaura os filtros da sessão para manter consistência com a tela
        $this->cad_item_wf_atributo_model->set_state_store_session(true);
        $this->cad_item_wf_atributo_model->restore_state_from_session('filter.', 'post');

        $id_empresa_sess = sess_user_company();
        $exclui_status = [1, 7]; // Mesmos status excluídos da tela principal

        try {
            // Log para debug
            log_message('debug', 'get_all_ncms_for_bulk_selection: Iniciando busca para empresa ' . $id_empresa_sess);

            // Busca todas as NCMs sem paginação (limit = null, offset = null)
            $all_ncms = $this->cad_item_wf_atributo_model->get_entries(null, null, false, null, $exclui_status);

            // Log para debug
            log_message('debug', 'get_all_ncms_for_bulk_selection: Encontradas ' . count($all_ncms) . ' NCMs');

            // Calcula total de itens para debug
            $total_itens = 0;
            foreach ($all_ncms as $ncm) {
                $total_itens += intval($ncm->total_itens);
            }
            log_message('debug', 'get_all_ncms_for_bulk_selection: Total de itens: ' . $total_itens);

            return response_json([
                'err' => 0,
                'data' => $all_ncms,
                'total_ncms' => count($all_ncms),
                'total_itens_debug' => $total_itens,
                'msg' => 'Dados carregados com sucesso.'
            ], 200);
        } catch (Exception $e) {
            log_message('error', 'get_all_ncms_for_bulk_selection: ' . $e->getMessage());
            return response_json([
                'err' => 1,
                'data' => [],
                'msg' => 'Erro ao carregar dados: ' . $e->getMessage()
            ], 500);
        }
    }


    public function ajax_get_historico()
    {

        $data = array();

        $post = $this->get_post_values();

        if (!empty($post)) {
            $part_number = $post['part_number'];
            $estabelecimento = $post['estabelecimento'];
        }

        $id_empresa = sess_user_company();

        if (!isset($part_number) || $part_number == '' || empty($part_number)) {
            return response_json([
                "data"    => $data,
                "msg"     => 'Dados inconsistentes! Informe o Part_number corretamente.'
            ], 203);
        }

        if (!isset($estabelecimento) || $estabelecimento == '' || empty($estabelecimento)) {
            return response_json([
                "data"    => $data,
                "msg"     => 'Dados inconsistentes! Informe o estabelecimento corretamente.'
            ], 203);
        }

        $data = $this->cad_item_wf_atributo_model->get_historico($part_number, $estabelecimento, $id_empresa);

        return response_json([
            "data"    => $data,
            "msg"     => 'Dados OK.'
        ], 200);
    }


    public function ajax_get_lista_itens_status()
    {
        $this->load->model('cad_item_model');
        $this->load->model('ncm_model');
        if ($this->get_post_values()) {
            $this->cad_item_wf_atributo_model->set_state_store_session(true);
            $this->cad_item_wf_atributo_model->restore_state_from_session('filter.', 'post');

            $post = $this->get_post_values();
            $ncm = isset($post['ncm']) ? $post['ncm'] : null;
            $idItens = isset($post['idItem']) ? $post['idItem'] : null;

            if (empty($ncm)) {
                $item = reset($idItens);
                $ncm_proposto = $this->cad_item_model->get_ncm_proposto($item);
                $ncm = $this->ncm_model->get_entry($ncm_proposto);
            }

            if (empty($idItens)) {
                return response_json([
                    "data"    => [],
                    "msg"     => 'Dados OK.'
                ], 200);
            }
            $data = $this->cad_item_wf_atributo_model->get_itens_validados_movimentacao($ncm, null, null, $idItens, false);

            return response_json([
                "data"    => $data,
                "msg"     => 'Dados OK.'
            ], 200);
        }
    }

    public function ajax_get_itens_validados()
    {
        $post = $this->get_post_values();

        $ncm =  isset($post['ncm']) ? $post['ncm'] : null;
        $part_numbers = isset($post['part_numbers']) ? $post['part_numbers'] : null;
        $estabelecimentos = isset($post['estabelecimentos']) ? $post['estabelecimentos'] : null;
        $status_novo = $post['status_novo'];
        $id_item = isset($post['id_item']) ? $post['id_item'] : null;

        // $ncm = '84249090';
        // $part_numbers = array('10441290', '82035111', '87379318');
        // $estabelecimentos = array('CU01', 'SA02', 'SA02');
        // $status_novo = 4;

        $id_empresa = sess_user_company();

        $data = array();
        if (!isset($ncm) || $ncm == '' || empty($ncm)) {
            return response_json([
                "data"    => $data,
                "msg"     => 'Dados inconsistentes! Informe o ncm corretamente.'
            ], 203);
        }

        $data = $this->cad_item_wf_atributo_model->get_itens_validados($status_novo, $id_item, $part_numbers, $estabelecimentos, $id_empresa, $ncm);

        return response_json([
            "data"    => $data,
            "msg"     => 'Dados OK.'
        ], 200);
    }

    public function filter_ncm($ncm)
    {
        return trim(str_replace('.', '', $ncm));
    }

    public function ajax_set_status()
    {

        $this->load->model(array('cad_item_wf_atributo_model', 'log_wf_atributos_model'));
        $this->load->model('cad_item_model');
        $this->load->model('ncm_model');

        $data = array();

        $post = $this->get_post_values();

        $ncm =  isset($post['ncm']) ? $post['ncm'] : null;
        $part_numbers = isset($post['partNumber']) ? $post['partNumber'] : null;
        $estabelecimentos = isset($post['estabelecimento']) ? $post['estabelecimento'] : null;
        $status_novo = isset($post['status']) ? $post['status'] : null;
        $homologado = isset($post['homolog_response']) ? $post['homolog_response'] : null;
        $id_item = isset($post['idItem']) ? $post['idItem'] : null;
        $justificativa  =  isset($post['justification']) ? $post['justification'] : '';
        $modal = isset($post['modal']) ? $post['modal'] : null;

        $id_empresa = sess_user_company();
        $id_usuario = sess_user_id();
        $this->load->model('empresa_model');
        $empresa = $this->empresa_model->get_entry($id_empresa);
        // $data = $this->cad_item_wf_atributo_model->set_status($part_numbers, $estabelecimentos, $id_empresa, $status_novo);

        if (is_Array($post['idItem'])) {
            $itens = [];
            foreach ($post['idItem'] as  $item) {
                if (!empty($item['id_item'])) {
                    $itens[] = $item['id_item'];
                }
            }
            if (!empty($itens)) {
                $id_item = $itens;
            } else {
                return;
            }
        }

        if (!empty($homologado)) {
            if ($homologado == 1) {
                $status_novo = 'homologados';
            }
            if ($homologado == 2) {
                $status_novo = 'analise_de_atributos_fiscal';
            }
        }

        if (!empty($modal) && $modal == 'history') {
            $status = $this->get_status_by_desc($status_novo);
        } else {
            $status = $this->get_status_by_id($status_novo, true);
        }

        if (!empty($homologado) && $homologado == 1 && !empty($id_item) && empty($ncm)) {

            $ncm_proposto = $this->cad_item_model->get_ncm_proposto($post['idItem']);
            $ncm = $this->filter_ncm($ncm_proposto);

            $valida_dados =  $this->cad_item_wf_atributo_model->get_itens_validados_movimentacao($ncm, null, null, $id_item);

            if (empty($valida_dados[0])) {
                return response_json([
                    "data"    => [],
                    "err"    => 1,
                    "msg"     => 'Não disponível para homologação.',
                    "itens"   => 0
                ], 200);
            }
        }

        // Processamento assíncrono via fila
        $this->processarAlteracaoStatusAssincrono(
            $status,
            $id_item,
            $part_numbers,
            $estabelecimentos,
            $id_empresa,
            $ncm,
            $id_usuario,
            $justificativa,
            $itens
        );

        if (!empty($itens)) {
            $total_itens = count($itens);
        } else {
            if (!empty($id_item)) {
                $total_itens = count($id_item);
            } else {
                $total_itens = 0;
            }
        }

        $msg = $this->geraMensagemHomologacaoFila($total_itens, 0, $status);

        return response_json([
            "data"    => $data,
            "err"    => 0,
            "msg"     => $msg,
            "itens"   => $total_itens
        ], 200);
    }

    /*
    * Atualiza o status de vários itens de uma vez
    * @param array $ids_item IDs dos itens a serem atualizados
    * @param int $homolog_response Resposta de homologação (1 para homologado, 2 para análise de atributos fiscal)
    * @param string $justification Justificativa para a mudança de status
    * @return JSON
    * @throws Exception
    */
    public function ajax_set_status_mass()
    {
        $this->load->model([
            'log_wf_atributos_model',
            'cad_item_model',
            'ncm_model',
            'email_queue_model',
            'usuario_model'
        ]);

        $id_empresa = sess_user_company();
        $id_usuario = sess_user_id();
        $post = $this->get_post_values();

        $item_ids = $post['ids_item'] ?? [];
        $homolog_response = $post['homolog_response'] ?? null;
        $justificativa = $post['justification'] ?? '';

        if (!$this->validateMassParams($item_ids, $homolog_response)) {
            return response_json(['err' => 1, 'msg' => 'Parâmetros insuficientes.'], 400);
        }

        $status_novo = $this->getStatusNovo($homolog_response);
        $itens_validados = $this->getItensValidados($id_empresa, $item_ids);

        $ids_validos = array_column($itens_validados, 'id_item');
        $ids_invalidos = array_diff($item_ids, $ids_validos);

        $total_sucesso = count($ids_validos);
        $total_falha = count($ids_invalidos);

        if ($total_sucesso > 0) {
            /** Retirado o envio de email com aviso para o usuário logado, a pedido de Douglas. */
            $usuario_data = $this->usuario_model->get_entry($id_usuario);
            $user_email = $usuario_data->email ?? null;
            $user_nome = $usuario_data->nome ?? 'Usuário';

            if (!$user_email) {
                return response_json(['err' => 1, 'msg' => 'Email do usuário não encontrado.'], 400);
            }

            $this->registrarHomologacaoNaFila(
                $status_novo,
                $ids_validos,
                $id_empresa,
                $id_usuario,
                $justificativa,
                $user_email,
                $user_nome
            );
        }

        $msg = $this->geraMensagemHomologacaoFila($total_sucesso, $total_falha);

        return response_json([
            'err' => 0,
            'msg' => $msg,
            'itens_sucesso' => $total_sucesso,
            'itens_falha' => $total_falha,
        ], 200);
    }

    private function validateMassParams($item_ids, $homolog_response)
    {
        return !empty($item_ids) && !empty($homolog_response);
    }

    private function getStatusNovo($homolog_response)
    {
        $desc = $homolog_response == 1 ? 'homologados' : 'Análise de atributos - Fiscal';

        return $this->get_status_by_desc($desc);
    }

    private function getItensValidados($id_empresa, $item_ids)
    {
        $itens = $this->cad_item_wf_atributo_model
            ->get_itens_validados_movimentacao(null, null, $id_empresa, $item_ids, true, true);

        return is_array($itens) ? $itens : [];
    }

    private function processaItensValidos($status_novo, $ids_validos, $id_empresa, $id_usuario, $justificativa)
    {
        $this->cad_item_wf_atributo_model->set_status(
            $status_novo->slug,
            $ids_validos,
            null,
            null,
            $id_empresa,
            null
        );

        $this->log_wf_atributos_model->registrar_log(
            $ids_validos,
            null,
            null,
            $id_empresa,
            $status_novo->id,
            'movimentacao_em_massa',
            $id_usuario,
            $justificativa
        );

        $email_payload = [
            'type' => 'alteracao_status_massa',
            'item_ids' => $ids_validos,
            'id_empresa' => $id_empresa,
            'status_id' => $status_novo->id
        ];
        $this->email_queue_model->add_to_queue($email_payload);
    }

    private function geraMensagemResposta($total_sucesso, $total_falha)
    {
        return "<ul class='list-unstyled' style='list-style-type: none; padding-left: 0; text-align: center;'>" .
            "<li style='color: green;'>Itens processados: {$total_sucesso}</li>" .
            "<li style='color: orange;'>Itens com pendências: {$total_falha}</li>" .
            "</ul>";
    }

    /**
     * Registra a homologação em massa na fila para processamento assíncrono.
     * @param object $status_novo Status novo para os itens
     * @param array $ids_validos IDs dos itens válidos
     * @param int $id_empresa ID da empresa
     * @param int $id_usuario ID do usuário
     * @param string $justificativa Justificativa para a mudança de status
     * @param string $user_email Email do usuário
     * @param string $user_nome Nome do usuário
     * @return void
     */
    private function registrarHomologacaoNaFila(
        $status_novo,
        $ids_validos,
        $id_empresa,
        $id_usuario,
        $justificativa,
        $user_email,
        $user_nome
    ) {
        // Payload de homologação para fila
        $payload = [
            'type' => 'homologacao_massa',
            'ids_validos' => $ids_validos,
            'status_novo' => [
                'id' => $status_novo->id,
                'slug' => $status_novo->slug,
                'desc' => $status_novo->status
            ],
            'id_empresa' => $id_empresa,
            'id_usuario' => $id_usuario,
            'justificativa' => $justificativa,
            'user_email' => $user_email,
            'user_nome' => $user_nome
        ];

        $this->email_queue_model->add_to_queue($payload);

        // Payload de email para alteração de status para fila
        $email_payload = [
            'type' => 'alteracao_status_massa',
            'item_ids' => $ids_validos,
            'id_empresa' => $id_empresa,
            'status_id' => $status_novo->id
        ];

        $this->email_queue_model->add_to_queue($email_payload);
    }

    /**
     * Gera mensagem de resposta para homologação em fila.
     */
    private function geraMensagemHomologacaoFila($total_sucesso, $total_falha, $status = null)
    {
        $msg_base = "<div style='text-align: center;'>";

        if ($status && $status->id == 7) {
            $msg_base .= "<p><strong>Solicitação de homologação registrada! Os itens serão processados em breve.</strong></p>";
        } else {
            $msg_base .= "<p><strong>Solicitação de alteração registrada! Os itens serão processados em breve.</strong></p>";
        }

        if ($total_sucesso > 0) {
            $msg_base .= "<p style='color: green;'>✓ {$total_sucesso} itens serão processados.</p>";
        }

        if ($total_falha > 0) {
            $msg_base .= "<p style='color: orange;'>⚠ {$total_falha} itens com pendências (não processados).</p>";
        }

        $msg_base .= "<p style='color: #286090;'>Será enviado um email para o responsável quando o processamento for concluído, comunicando a alteração de status.</p>";
        $msg_base .= "</div>";

        return $msg_base;
    }

    public function get_status_by_desc($status_desc)
    {
        $this->load->library("Item/Atributo");

        return $this->atributo->get_status_by_desc($status_desc);
    }

    public function get_status_by_id($id, $all_result = false)
    {
        $this->load->library("Item/Atributo");

        return $this->atributo->get_status($id, $all_result);
    }



    public function ajax_get_all_status()
    {
        $this->load->library("Item/Atributo");

        if ($this->get_post_values()) {
            $post = $this->get_post_values();
            $status_pretendidos = $post['status'];
        }

        //$status_prtendidos = array(0 => 2, 1 => 5, 2 => 6);

        //$status_prtendidos = [];

        // Obtém todos os status
        $todos_status = $this->atributo->get_all_status();
        $status_filtrados = [];

        // Se houver status armazenados para filtrar
        if ($this->get_post_values() && $status_pretendidos) {
            foreach ($todos_status as $status) {
                if (in_array($status->id, $status_pretendidos)) {
                    $status_filtrados[] = $status;
                }
            }
        } else {
            // Se não houver filtro, retorna todos os status
            $status_filtrados = $todos_status;
        }

        return response_json([
            "data" => $status_filtrados
        ], 200);
    }


    public function ajax_get_all_status_integracao()
    {
        $this->load->library("Item/Atributo");

        return response_json([
            "data"  => $this->atributo->get_all_status_integracao()
        ], 200);
    }

    /**
     * Busca todos os itens de uma NCM específica (para seleção em massa)
     * @param string $ncm_id ID da NCM
     * @return JSON
     */
    public function ajax_get_all_items_from_ncm($ncm_id = null)
    {
        try {
            if (empty($ncm_id)) {
                return response_json([
                    "err" => 1,
                    "msg" => "NCM não informada"
                ], 400);
            }

            $id_empresa = sess_user_company();

            // Buscar todos os itens da NCM (sem paginação)
            $all_items = $this->cad_item_wf_atributo_model->get_all_items_from_ncm($ncm_id, $id_empresa);

            return response_json([
                "err" => 0,
                "items" => $all_items,
                "total" => count($all_items),
                "msg" => "Itens carregados com sucesso"
            ], 200);
        } catch (Exception $e) {
            log_message('error', 'Erro ao buscar todos os itens da NCM: ' . $e->getMessage());
            return response_json([
                "err" => 1,
                "msg" => "Erro interno do servidor"
            ], 500);
        }
    }

    /**
     * Processa alteração de status de forma síncrona (comportamento atual)
     */
    private function processarAlteracaoStatusSincrono($status, $id_item, $part_numbers, $estabelecimentos, $id_empresa, $ncm, $id_usuario, $justificativa, $itens)
    {
        // Executar set_status
        $this->cad_item_wf_atributo_model->set_status($status->slug, $id_item, $part_numbers, $estabelecimentos, $id_empresa, $ncm);

        // Processar itens por NCM se necessário
        if (!empty($ncm) && empty($id_item)) {
            $this->cad_item_wf_atributo_model->set_state_store_session(true);
            $this->cad_item_wf_atributo_model->restore_state_from_session('filter.', 'post');

            $empresa = $this->empresa_model->get_entry($id_empresa);
            $itens = $this->cad_item_wf_atributo_model->get_itens_validados_movimentacao($ncm, $empresa, $id_empresa);

            $part_numbers = [];
            $estabelecimentos = [];
            foreach ($itens as $item) {
                $part_numbers[] = $item->part_number;
                $estabelecimentos[] = $item->estabelecimento;
            }
        }

        // Registrar log
        $this->log_wf_atributos_model->registrar_log($id_item, $part_numbers, $estabelecimentos, $id_empresa, $status->id, 'movimentacao_manual', $id_usuario, $justificativa);

        if (!is_array($id_item)) {
            $id_item = [$id_item];
        }

        // Envia e-mail de notificação via fila
        $this->load->model('email_queue_model');
        $email_payload = [
            'type' => 'alteracao_status_by_ncm',
            'item_ids' => $id_item,
            'id_empresa' => $id_empresa,
            'status_id' => $status->id
        ];
        $this->email_queue_model->add_to_queue($email_payload);
    }

    /**
     * Processa alteração de status de forma assíncrona via fila
     */
    private function processarAlteracaoStatusAssincrono(
        $status,
        $id_item,
        $part_numbers,
        $estabelecimentos,
        $id_empresa,
        $ncm,
        $id_usuario,
        $justificativa,
        $itens
    ) {
        // Obter email do usuário para notificação
        $this->load->model('usuario_model');
        $usuario_data = $this->usuario_model->get_entry($id_usuario);
        $user_email = $usuario_data->email ?? null;

        if (!$user_email) {
            // Se não tem email, usar processamento síncrono como fallback
            $this->processarAlteracaoStatusSincrono($status, $id_item, $part_numbers, $estabelecimentos, $id_empresa, $ncm, $id_usuario, $justificativa, $itens);
            return;
        }

        // Registrar na fila para processamento assíncrono
        $this->load->model('email_queue_model');
        $payload = [
            'type' => 'homologacao_por_ncm',
            'status' => [
                'id' => $status->id,
                'slug' => $status->slug,
                'desc' => $status->desc ?? $status->slug
            ],
            'id_item' => $id_item,
            'part_numbers' => $part_numbers,
            'estabelecimentos' => $estabelecimentos,
            'id_empresa' => $id_empresa,
            'ncm' => $ncm,
            'id_usuario' => $id_usuario,
            'user_email' => $user_email,
            'user_nome' => $usuario_data->nome ?? 'Usuário',
            'justificativa' => $justificativa,
            'itens_existentes' => $itens // Para casos onde já temos os itens carregados
        ];

        $this->email_queue_model->add_to_queue($payload);
    }
}
