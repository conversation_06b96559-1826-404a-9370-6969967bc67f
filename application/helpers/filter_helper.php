<?php
if (!defined('BASEPATH')) {
    exit('No direct script access allowed');
}

if (!function_exists('get_filter_value')) {
    function get_filter_value($key, $model, $default = null)
    {
        $CI =& get_instance();
        $post_value = $CI->input->post($key);

        if ($post_value === '' || (is_array($post_value) && empty($post_value))) {
            return null;
        }
        
        // Se o valor do POST não for false (ou seja, existe), retornamos ele
        if ($post_value !== false) {
            return $post_value;
        }
        
        // <PERSON>aso contrário, obtemos o valor do estado do modelo
        $state_value = $model->get_state("filter.$key");
        
        // Se o valor do estado for uma string vazia, retornamos null
        if ($state_value === '') {
            return null;
        }
        
        // Se o valor do estado existir, retornamos ele
        if ($state_value !== false && $state_value !== null) {
            return $state_value;
        }
        
        // Se chegamos aqui, retornamos o valor padrão
        return $default;
    }
}

if (!function_exists('set_filter_value')) {
    function set_filter_value($key, $value, $model)
    {
        $CI =& get_instance();
        $model->set_state("filter.$key", $value);
    }
}

if (!function_exists('get_filter_order')) {
    function get_filter_order($model)
    {
        $CI =& get_instance();
        return $CI->input->post('order') ?: $model->get_state('filter.order');
    }
}

if (!function_exists('set_filter_order')) {
    function set_filter_order($value, $model)
    {
        $CI =& get_instance();
        $model->set_state('filter.order', $value);
    }
}

/* End of file filter_helper.php */
