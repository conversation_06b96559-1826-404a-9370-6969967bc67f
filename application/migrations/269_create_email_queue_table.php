<?php if ( ! defined('BASEPATH')) {
    exit('No direct script access allowed');
}
/**
 * Migration to create the email queue table.
 *
 * This migration creates a table to manage email jobs in the system.
 * The table includes fields for job payload, status, attempts, error messages,
 * and timestamps for creation and processing.
 */
class Migration_Create_Email_Queue_Table extends CI_Migration {

    public function up()
    {
        $this->load->dbforge();
        
         // Define os campos da tabela
        $fields = array(
            'id' => array(
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true
            ),
            'payload' => array(
                'type' => 'JSON',
            ),
            'status' => array(
                'type' => "ENUM('pending','processing','sent','failed')",
                'default' => 'pending',
                'null' => false,
            ),
            'attempts' => array(
                'type' => 'TINYINT',
                'constraint' => 4,
                'default' => 0,
                'null' => false,
            ),
            'error_message' => array(
                'type' => 'TEXT',
                'null' => true,
            ),
            'created_at' => array(
                'type' => 'TIMESTAMP',
                'null' => false
            ),
            'processed_at' => array(
                'type' => 'TIMESTAMP',
                'null' => true,
            ),
        );

        $this->dbforge->add_field($fields);

        // Define a chave primária
        $this->dbforge->add_key('id', true);

        // Define outras chaves/índices
        $this->dbforge->add_key(['status', 'attempts']);

        // Cria a tabela
        $this->dbforge->create_table('email_queue', true); // O segundo parâmetro TRUE adiciona o "IF NOT EXISTS"

        $this->db->query("ALTER TABLE email_queue MODIFY created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL");
    }

    public function down()
    {
        if ($this->db->table_exists('email_queue')) {
            $this->db->query("DROP TABLE `email_queue`");
        }
    }

}
