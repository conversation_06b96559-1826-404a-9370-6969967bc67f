<?php echo form_open("wf/atributos"); ?>
<input type="hidden" name="filtered" value="1" />

<div class="row">
    <div class="col-sm-12">
        <div class="form-group row">
            <?php $tipo_item = $this->cad_item_wf_atributo_model->get_state('filter.tipo_item'); ?>

            <div class="col-sm-2">
                <div class="radio">
                    <label>
                        <input type="radio" name="tipo_item" id="importado" value="importado" <?php echo ($tipo_item == 'importado' || empty($tipo_item)) ? 'checked' : '' ?>> Itens Importados
                    </label>
                </div>
            </div>
            <div class="col-sm-2">
                <div class="radio">
                    <label>
                        <input type="radio" name="tipo_item" id="nacional" value="nacional" <?php echo ($tipo_item == 'nacional') ? 'checked' : '' ?>> Itens Nacionais
                    </label>
                </div>
            </div>
            <div class="col-sm-8"></div>
        </div>
    </div>

    <div class="col-sm-4">
        <label for="sistema_origem_modal">Status Atributos</label>
        <div class="form-group" id="select_atributos" style="width: 100%">
            <select class="selectpicker form-control" style="width: 100%" name="status_atributos[]" id="status_atributos" data-live-search="true" multiple data-selected-text-format="count > 1" data-count-selected-text="Status Atributos({0})" title="Selecione o status do atributo">
                <?php $status_atributossel = $this->cad_item_wf_atributo_model->get_state('filter.status_atributos') ?: []; ?>
                <option <?php echo empty($status_atributossel) ? 'selected' : '' ?> value="">Todos</option>
                <?php foreach ($status_todos_atributos as $status_atributos) :  if ($status_atributos->status == 'Item nacional') continue; ?>
                    <option <?php echo in_array($status_atributos->id, $status_atributossel) ? 'selected' : '' ?>
                        value="<?php echo $status_atributos->id ?>">
                        <?php echo $status_atributos->status ?>
                    </option>
                <?php endforeach; ?>
            </select>
        </div>
    </div>

    <div class="col-sm-4">
        <label for="status_classificacao_fiscal">Status de classificação fiscal</label>
        <div class="form-group">
            <select class="form-control selectpicker" multiple title="Todos os Status" data-count-selected-text="Status de Classificação Fiscal ({0})" data-selected-text-format="count > 1" data-actions-box="true" data-select-all-text="Todos" data-deselect-all-text="Nenhum" data-live-search="true" name="status_classificacao_fiscal[]" id="status_classificacao_fiscal" data-width="100%">
                <?php $status_classificacao_fiscalsel = $this->cad_item_wf_atributo_model->get_state('filter.status_classificacao_fiscal') ?: [];
                ?>
                <option <?php echo empty($status_classificacao_fiscalsel) ? 'selected' : '' ?> value="">Todos</option>

                <?php foreach ($status_class_fiscais as $status_classificacao_fiscal) : ?>
                    <option <?php echo in_array($status_classificacao_fiscal['id'], $status_classificacao_fiscalsel) ? 'selected' : '' ?>
                        value="<?php echo $status_classificacao_fiscal['id'] ?>">
                        <?php echo $status_classificacao_fiscal['status'] ?>
                    </option>
                <?php endforeach; ?>
            </select>
        </div>
    </div>

    <div class="col-sm-4">
        <label for="sistema_origem_modal">Status de Preenchimento</label>
        <div class="form-group" style="width: 100%">
            <select class="selectpicker form-control" style="width: 100%" name="status_preenchimento[]" id="status_preenchimento" data-live-search="true" multiple data-selected-text-format="count > 1" data-count-selected-text="Status de Preenchimento({0})" title="Selecione o status de preenchimento">
                <?php $status_preenchimento = (array) $this->cad_item_wf_atributo_model->get_state('filter.status_preenchimento') ?: []; ?>
                <option <?php echo in_array(1, $status_preenchimento) ? "selected" : "" ?> value="1">Sem preenchimento</option>
                <option <?php echo in_array(2, $status_preenchimento) ? "selected" : "" ?> value="2">Atributos obrigatórios não preenchidos</option>
                <option <?php echo in_array(3, $status_preenchimento) ? "selected" : "" ?> value="3">Atributos opcionais não preenchidos</option>
                <option <?php echo in_array(4, $status_preenchimento) ? "selected" : "" ?> value="4">Totalmente preenchidos</option>
                <option <?php echo in_array(5, $status_preenchimento) ? "selected" : "" ?> value="5">NCM sem atributos</option>
            </select>
        </div>
    </div>


    <div class="col-sm-4">
        <label for="evento">Evento</label>
        <div class="form-group" style="width: 100%">
            <select class="form-control selectpicker" multiple title="Todos os Eventos" data-count-selected-text="Eventos ({0})" data-selected-text-format="count > 1" data-actions-box="true" data-select-all-text="Todos" data-deselect-all-text="Nenhum" data-live-search="true" name="evento[]" id="evento" data-width="100%">
                <option value="">Todos</option>
            </select>
        </div>
    </div>


    <div class="col-sm-4">
        <label for="prioridade">Prioridade</label>
        <div class="form-group" style="width: 100%">
            <select class="selectpicker form-control" style="width: 100%" name="prioridade[]" id="prioridade" data-live-search="true" multiple data-selected-text-format="count > 1" data-count-selected-text="Prioridade ({0})" title="Selecione a Prioridade">
                <option value="-1">Todas as prioridades</option>
            </select>
        </div>
    </div>

    <div class="col-sm-4">
        <label for="ncm_proposta">NCM Proposto</label>
        <div class="form-group" style="width: 100%">
            <select class="selectpicker form-control"
                style="width: 100%"
                name="ncm_proposta[]"
                id="ncm_proposta"
                data-live-search="true"
                multiple
                data-selected-text-format="count > 1"
                data-count-selected-text="NCM-Proposto({0})"
                title="Selecione o NCM">
                <option value="-1">Todos os ncms propostos</option>
            </select>
        </div>
    </div>


    <div class="col-sm-12">
        <label for="search" style="width: 100%">Pesquisar:</label>
        <div class="form-group" style="width: 100%">
            <?php $search = $this->cad_item_wf_atributo_model->get_state('filter.search'); ?>
            <textarea style="width: 100%" class="form-control" rows="3" placeholder="Pesquisar..." name="search" id="search" value="<?php echo !empty($search) ? $search : '' ?>"><?php echo !empty($search) ? $search : '' ?></textarea>
        </div>
    </div>
    <div class="col-sm-12">
    <div class="panel-group" id="accordionx" role="tablist" aria-multiselectable="true">
        <div class="panel panel-default">
            <div class="panel-heading" role="tab" id="headingOne">
                <h4 class="panel-title">
                    <a role="button" data-toggle="collapse" data-parent="#accordionx" href="#collapseOnex" aria-expanded="false" aria-controls="collapseOnex">
                        <span class="glyphicon glyphicon-filter" aria-hidden="true"></span> Filtros Avançados
                    </a>
                </h4>
            </div>
            <div id="collapseOnex" class="panel-collapse collapse" role="tabpanel" aria-labelledby="headingOne" aria-expanded="false">
                <div class="panel-body">
                    <div class="row">

                        <div class="col-sm-4">
                            <label for="sistema_origem_modal">Status de Integração</label>
                            <div class="form-group" id="select_integracao" style="width: 100%">
                                <select class="selectpicker form-control" style="width: 100%" name="status_integracao[]" id="status_integracao" data-live-search="true" multiple data-selected-text-format="count > 1" data-count-selected-text="Status de Integração({0})" title="Selecione o status de integração">

                                    <?php $status_integracaosel = $this->cad_item_wf_atributo_model->get_state('filter.status_integracao') ?: []; ?>

                                    <option <?php echo (empty($status_integracaosel)) ? 'selected' : '' ?> value="">
                                        Todos os status de integração
                                    </option>
                                    <?php foreach ($status_todos_integracoes as $status_integracao) : ?>
                                        <option <?php echo in_array($status_integracao->id, $status_integracaosel) ? 'selected' : '' ?> value="<?php echo $status_integracao->id ?>">
                                            <?php echo $status_integracao->status ?>
                                        <?php endforeach; ?>
                                </select>
                            </div>
                        </div>


                        <?php if (in_array('owner', $campos_adicionais)) : ?>
                            <div class="col-sm-4">
                                <label for="owner">Owner</label>
                                <div class="form-group" style="width: 100%">
                                    <select class="selectpicker form-control" style="width: 100%" name="owner[]" id="owner" data-live-search="true" multiple data-selected-text-format="count > 1" data-count-selected-text="Owner ({0})" title="Selecione Owner">
                                        <?php $ownersel = $this->cad_item_wf_atributo_model->get_state('filter.owner') ?: []; ?>
                                        <option <?php echo (empty($ownersel) || in_array('-1', $ownersel)) ? 'selected' : '' ?> value="-1">Todos os owners</option>
                                        <?php foreach ($owners as $owner) : ?>
                                            <option <?php echo in_array($owner->codigo, $ownersel) ? 'selected' : '' ?> value="<?php echo $owner->codigo ?>">
                                                <?php echo $owner->codigo; ?> - <?php echo $owner->descricao; ?> - <?php echo $owner->nomes; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                        <?php endif; ?>
                        <div class="col-sm-4">
                            <label for="responsavel">Responsável</label>
                            <div class="form-group" style="width: 100%">
                                <select class="selectpicker form-control" style="width: 100%" name="responsavel[]" id="responsavel" data-live-search="true" multiple data-selected-text-format="count > 1" data-count-selected-text="Responsável ({0})" title="Selecione o Responsável">
                                    <option value="-1">Todos os responsáveis</option>
                                </select>
                            </div>
                        </div>

                        <div class="col-sm-4">
                            <label for="estabelecimento">Estabelecimento</label>
                            <div class="form-group" style="width: 100%">
                                <select class="selectpicker form-control" style="width: 100%" name="estabelecimento[]" id="estabelecimento" data-live-search="true" multiple data-selected-text-format="count > 1" data-count-selected-text="Estabelecimento ({0})" title="Selecione o estabelecimento">
                                    <?php $estabelecimentosel = $this->cad_item_wf_atributo_model->get_state('filter.estabelecimento') ?: []; ?>
                                    <option <?php echo (empty($estabelecimentosel) || in_array('-1', $estabelecimentosel)) ? 'selected' : '' ?> value="-1">
                                        Todos os estabelecimentos
                                    </option>
                                    <?php foreach ($estabelecimentos as $estabelecimento) : ?>
                                        <option <?php echo in_array($estabelecimento, $estabelecimentosel) ? 'selected' : '' ?>
                                            value="<?php echo $estabelecimento; ?>">
                                            <?php echo $estabelecimento; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>

                        <div class="col-sm-4">
                            <label for="objetivos">Objetivos</label>
                            <div class="form-group" style="width: 100%">
                                <select class="selectpicker form-control" style="width: 100%" name="objetivos[]" id="objetivos" data-live-search="true" multiple data-selected-text-format="count > 1" data-count-selected-text="Objetivos ({0})" title="Selecione os objetivos">
                                    <?php $objetivosel = $this->cad_item_wf_atributo_model->get_state('filter.objetivos') ?: []; ?>
                                    <option <?php echo (empty($objetivosel) || in_array('-1', $objetivosel)) ? 'selected' : '' ?> value="-1">
                                        Todos os objetivos
                                    </option>
                                    <?php foreach ($objetivos as $objetivo) : ?>
                                        <option <?php echo in_array($objetivo, $objetivosel) ? 'selected' : '' ?> value="<?php echo $objetivo; ?>">
                                            <?php echo $objetivo; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>

                        <div class="col-sm-4">
                            <label for="data_inicio_importado_modal">Data Inicial Tornou-se Importado:</label>
 
                            <div class="form-group" style="width: 100%">
                                <div class="input-group date datetimepicker" style="width: 100%" id="datepicker_ini">
                                    <?php if ($this->input->post('data_inicio_importado_modal') !== false) :
                                    ?>
                                        <input type="text" class="form-control datetimepicker" name="data_inicio_importado_modal" id="data_inicio_importado_modal" value="<?php echo $this->input->post('data_inicio_importado_modal') ?>" placeholder="Data inicial" />
                                    <?php else : ?>
                                        <input type="text" class="form-control datetimepicker" name="data_inicio_importado_modal" id="data_inicio_importado_modal" value="<?php echo $this->input->post('data_inicio_importado_modal') ?>" placeholder="Data inicial" />
                                    <?php endif; ?>
                                    <span class="input-group-addon">
                                        <span class="glyphicon glyphicon-calendar"></span>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <label for="data_fim_importado_modal">Data Final Tornou-se Importado:</label>
                            <div class="form-group" style="width: 100%">
                                <div class="input-group date datetimepicker" style="width: 100%" id="datepicker_ini">
                                    <?php if ($this->input->post('data_fim_importado_modal') !== false) :
                                    ?>
                                        <input type="text" class="form-control datetimepicker" name="data_fim_importado_modal" id="data_fim_importado_modal" value="<?php echo $this->input->post('data_fim_importado_modal') ?>" placeholder="Data inicial" />
                                    <?php else : ?>
                                        <input type="text" class="form-control datetimepicker" name="data_fim_importado_modal" id="data_fim_importado_modal" value="<?php echo $this->input->post('data_fim_importado_modal') ?>" placeholder="Data inicial" />
                                    <?php endif; ?>
                                    <span class="input-group-addon">
                                        <span class="glyphicon glyphicon-calendar"></span>
                                    </span>
                                </div>
                            </div>
                        </div>

                    </div>

                    <div class="row">
                        <div class="col-sm-3 text-left">

                            <!-- <div class="btn-group">
                    <label class=" disabled arvore-opcao">
                        <input type="radio" name="opcao_texto" id="opcao_partnumber" <?php // echo $this->input->post('opcao_texto') == 'partnumber' ? 'checked' : '' 
                                                                                        ?>  value="partnumber"> Part Number
                    </label>
                    <label class=" disabled arvore-opcao">
                        <input type="radio" name="opcao_texto" id="opcao_descricao" <?php // echo $this->input->post('opcao_texto') == 'descricao' ? 'checked' : '' 
                                                                                    ?> value="descricao"> Descrição Completa
                    </label>
                </div> -->
                        </div>

                    </div>
                </div>

            </div>
        </div>
    </div>
</div>
    <div class="col-sm-12">
        <div class="form-group" style="width: 100%;">
            <button id="pesquisar-btn" class="btn btn-primary btn-block" type="submit">
                <i class="glyphicon glyphicon-search"></i> Pesquisar
                <img id="loading-img" src="<?php echo base_url('assets/img/loading_ajax.gif') ?>" width="10px" height="10px" style="display: none;">
            </button>
        </div>
    </div>
    <div class="col-sm-12 text-right">
        <div class="form-group">
            <a href="<?php echo site_url('wf/atributos?reset_filters=1'); ?>" class="btn btn-link pull-right">Limpar Pesquisa</a>
        </div>
    </div>

</div>

<?php echo form_close() ?>
<script>
    window.onload = function() {
        const selectElement = document.getElementById('status_atributos');
        const selectIntegracao = document.getElementById('status_integracao');

        const nacionalRadio = document.getElementById('nacional');
        const importadoRadio = document.getElementById('importado');

        function toggleSelect() {
            if (nacionalRadio.checked) {
                selectElement.disabled = true;
                selectIntegracao.disabled = true;
                // Get the button element and change its background color (com tratamento de erro)
                const selectAtributosContainer = document.getElementById('select_atributos');
                const selectIntegracaoContainer = document.getElementById('select_integracao');
                const button = selectAtributosContainer ? selectAtributosContainer.querySelector('.btn.dropdown-toggle') : null;
                const buttonIntegracao = selectIntegracaoContainer ? selectIntegracaoContainer.querySelector('.btn.dropdown-toggle') : null;

                for (let i = 0; i < selectElement.options.length; i++) {
                    selectElement.options[i].selected = false;
                }
                for (let i = 0; i < selectIntegracao.options.length; i++) {
                    selectIntegracao.options[i].selected = false;
                }

                if (button) {
                    button.style.backgroundColor = '#d4d4d4';
                    buttonIntegracao.style.backgroundColor = '#d4d4d4';
                }

            } else if (importadoRadio.checked) {
                selectElement.disabled = false;
                selectElement.style.backgroundColor = '';
                selectIntegracao.disabled = false;
                selectIntegracao.style.backgroundColor = '';

                const selectAtributosContainer = document.getElementById('select_atributos');
                const selectIntegracaoContainer = document.getElementById('select_integracao');
                const button = selectAtributosContainer ? selectAtributosContainer.querySelector('.btn.dropdown-toggle') : null;
                const buttonIntegracao = selectIntegracaoContainer ? selectIntegracaoContainer.querySelector('.btn.dropdown-toggle') : null;

                if (button) {
                    button.style.backgroundColor = '';
                    buttonIntegracao.style.backgroundColor = '';
                }
            }
        }

        // Verifica o estado inicial ao carregar a página
        toggleSelect();

        // Adiciona eventos aos radio buttons
        nacionalRadio.addEventListener('change', toggleSelect);
        importadoRadio.addEventListener('change', toggleSelect);
    };
</script>
<div id="loading-overlay">
    <div id="loading-message">Carregando...</div>
</div>


<style>
    #loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 9999;
        display: none;
    }

    #loading-message {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: white;
        font-size: 24px;
    }
</style>
<script>
$(document).ready(function() {
    $('.selectpicker').on('show.bs.select', function () {
        $(this).closest('.elemento-com-overflow-hidden').addClass('overflow-visible');
    });

    $('.selectpicker').on('hide.bs.select', function () {
        $(this).closest('.elemento-com-overflow-hidden').removeClass('overflow-visible');
    });
});


</script>
<style>
.overflow-visible {
  overflow: visible !important; 
}
 
.bootstrap-select > .dropdown-menu {
    z-index: 9999 !important;
}
</style>

<script>
    var ncmSelecionados = <?= json_encode($this->cad_item_wf_atributo_model->get_state('filter.ncm_proposta')); ?>;
    var eventoSelecionados = <?= json_encode($this->cad_item_wf_atributo_model->get_state('filter.evento')); ?>;
    var prioridadeSelecionados = <?= json_encode($this->cad_item_wf_atributo_model->get_state('filter.prioridade')); ?>;
    var responsavelSelecionados = <?= json_encode($this->cad_item_wf_atributo_model->get_state('filter.responsavel')); ?>;
    $(document).ready(function() {
        let ncmLoaded = false;
        let eventoLoaded = false;
        let prioridadeLoaded = false;
        let responsavelLoaded = false;

        if (ncmSelecionados && ncmSelecionados.length > 0) {

            $.each(ncmSelecionados, function(index, value) {

                if ($('#ncm_proposta option[value="' + value + '"]').length === 0) {
                    $('#ncm_proposta').append(`<option value="${value}" selected>${value}</option>`);
                }
            });
        }


        if (eventoSelecionados && eventoSelecionados.length > 0) {

            $.each(eventoSelecionados, function(index, value) {

                if ($('#evento option[value="' + value + '"]').length === 0) {
                    $('#evento').append(`<option value="${value}" selected>${value}</option>`);
                }
            });
        }


        if (prioridadeSelecionados && prioridadeSelecionados.length > 0) {

            $.each(prioridadeSelecionados, function(index, value) {

                if ($('#prioridade option[value="' + value + '"]').length === 0) {
                    $('#prioridade').append(`<option value="${value}" selected>Carregando...</option>`);

                    $.ajax({
                        url: '/wf/atributos/get_list_prioridades', // URL do endpoint que retorna as prioridades
                        type: 'GET',
                        dataType: 'json',
                        success: function(response) {
                            let data = response;
                            $('#prioridade').empty().append('<option value="-1">Todas as prioridades</option>');
                            $.each(data, function(_, item) {
                                $('#prioridade').append(`<option value="${item.id_prioridade}">${item.nome}</option>`);
                            });
                            $('#prioridade').selectpicker('val', prioridadeSelecionados);
                            $('#prioridade').selectpicker('refresh');
                            prioridadeLoaded = true;
                        },
                        error: function(err) {
                            console.error('Erro ao carregar Prioridades:', err);
                        }
                    });
                }
            });
        }

        if (responsavelSelecionados && responsavelSelecionados.length > 0) {

            $.each(responsavelSelecionados, function(index, value) {

                if ($('#responsavel option[value="' + value + '"]').length === 0) {
                    $('#responsavel').append(`<option value="${value}" selected>${value}</option>`);
                }
            });
        }
        $('#ncm_proposta').on('show.bs.select', function() {
            if (ncmLoaded) return;
            $('#ncm_proposta').empty();
            $('#ncm_proposta').append('<option value="">Carregando...</option>');
            $('#ncm_proposta').selectpicker('refresh');
            $.ajax({
                url: '/wf/atributos/get_list_ncms',
                type: 'GET',
                dataType: 'json',
                success: function(response) {
                    let data = response;
                    let selectedValues = ncmSelecionados || []; // usa o valor selecionado que veio do PHP

                    $('#ncm_proposta').empty();
                    $('#ncm_proposta').append('<option value="-1">Todos os ncms propostos</option>');

                    $.each(data, function(_, item) {
                        $('#ncm_proposta').append(`<option value="${item.ncm_proposto}">${item.ncm_proposto}</option>`);
                    });

                    $('#ncm_proposta').selectpicker('val', selectedValues);
                    $('#ncm_proposta').selectpicker('refresh');

                    ncmLoaded = true;
                },
                error: function(err) {
                    console.error('Erro ao carregar NCMs:', err);
                }
            });
        });




        $('#evento').on('show.bs.select', function() {
            if (eventoLoaded) return;
            $('#evento').empty().append('<option value="">Carregando...</option>');
            $('#evento').selectpicker('refresh');

            $.ajax({
                url: '/wf/atributos/get_list_eventos', // URL do endpoint que retorna os eventos
                type: 'GET',
                dataType: 'json',
                success: function(response) {
                    let data = response;
                    $('#evento').empty().append('<option value="">Todos</option>');
                    $.each(data, function(_, item) {
                        $('#evento').append(`<option value="${item.evento}">${item.evento}</option>`);
                    });
                    $('#evento').selectpicker('val', eventoSelecionados);
                    $('#evento').selectpicker('refresh');
                    eventoLoaded = true;
                },
                error: function(err) {
                    console.error('Erro ao carregar Eventos:', err);
                }
            });
        });

        // Função para carregar as Prioridades
        $('#prioridade').on('show.bs.select', function() {
            if (prioridadeLoaded) return;
            $('#prioridade').empty().append('<option value="">Carregando...</option>');
            $('#prioridade').selectpicker('refresh');

            $.ajax({
                url: '/wf/atributos/get_list_prioridades', // URL do endpoint que retorna as prioridades
                type: 'GET',
                dataType: 'json',
                success: function(response) {
                    let data = response;
                    $('#prioridade').empty().append('<option value="-1">Todas as prioridades</option>');
                    $.each(data, function(_, item) {
                        $('#prioridade').append(`<option value="${item.id_prioridade}">${item.nome}</option>`);
                    });
                    $('#prioridade').selectpicker('val', prioridadeSelecionados);
                    $('#prioridade').selectpicker('refresh');
                    prioridadeLoaded = true;
                },
                error: function(err) {
                    console.error('Erro ao carregar Prioridades:', err);
                }
            });
        });




        var responsavelSelecionados = <?= json_encode($this->cad_item_wf_atributo_model->get_state('filter.responsavel')); ?>;
        $(document).ready(function() {
            let responsavelLoaded = false;

            if (responsavelSelecionados && responsavelSelecionados.length > 0) {

                $.each(responsavelSelecionados, function(index, value) {

                    if ($('#responsavel option[value="' + value + '"]').length === 0) {
                        $('#responsavel').append(`<option value="${value}" selected>${value}</option>`);
                    }
                });
            }

            $('#responsavel').on('show.bs.select', function() {
                if (responsavelLoaded) return;
                $('#responsavel').empty();
                $('#responsavel').append('<option value="">Carregando...</option>');
                $('#responsavel').selectpicker('refresh');
                $.ajax({
                    url: '/wf/atributos/get_list_responsaveis', // ajuste a URL conforme o seu backend
                    type: 'GET',
                    dataType: 'json',
                    success: function(response) {
                        let data = response;
                        let selectedValues = responsavelSelecionados || []; // usa o valor selecionado que veio do PHP

                        $('#responsavel').empty();
                        $('#responsavel').append('<option value="-1">Todos os responsáveis</option>');

                        // Popula o select com os novos responsáveis
                        $.each(data, function(_, item) {
                            $('#responsavel').append(`<option value="${item.id_resp_engenharia}">${item.nome}</option>`);
                        });

                        $('#responsavel').selectpicker('val', selectedValues);
                        $('#responsavel').selectpicker('refresh');

                        responsavelLoaded = true;
                    },
                    error: function(err) {
                        console.error('Erro ao carregar responsáveis:', err);
                    }
                });
            });
        });

    });
</script>

<script>

    $(function() {
        $('.datetimepicker').datetimepicker({
            'format': 'DD/MM/YYYY',
            'locale': 'pt-BR'
        });

    });

    $(document).ready(function() {
 

        <?php
        $minha_variavel = $this->input->post('data_inicio_importado_modal');
        if (strpos($minha_variavel, '-') !== false) {
        ?>
            $('#data_inicio_importado_modal').val('<?php echo date('d/m/Y', strtotime(str_replace('-', '/', $this->input->post('data_inicio_importado_modal')))) ?>');
        <?php } ?>

        <?php
        $minha_variavel = $this->input->post('data_fim_importado_modal');
        if (strpos($minha_variavel, '-') !== false) {
        ?>
            $('#data_fim_importado_modal').val('<?php echo date('d/m/Y', strtotime(str_replace('-', '/', $this->input->post('data_fim_importado_modal')))) ?>');
        <?php } ?>

    });
 
</script>
