<script type="text/javascript">
    $(function() {

        
        $('#input-part-number').on('blur', function() {
            $('#input-part-number-modal').val($(this).val());

            $('#message_user_transfer_owner').html('');
        });

        $('#estabelecimento').on('change', function() {
            $('#estabelecimento-modal').val($(this).find('option:selected').text());

            $('#message_user_transfer_owner').html('');
        });


        $('select[name="tag"]').on('change', function() {
            var tag = $(this).val();
            atualiza_grupo_tarifario(tag);
        });

        $('input[name="part_number_similar"]').typeahead({
            source: function(query, process) {
                return $.get("<?php echo site_url("cadastros/mestre_itens/json_lista_itens") ?>?search=" + query, function(data) {
                    process(data);
                });
            },
            afterSelect: function(e) {
                if (DESCONSIDERA_SIMILAR) {
                    return false;
                }

                var fields = ["ncm", "descricao", "funcao", "inf_adicionais", "material_constitutivo", "aplicacao", "marca"];

                for (var i in fields) {
                    var item = fields[i];

                    $('input[name="' + item + '"]').val('');

                    if (typeof e[item] != "undefined") {
                        $('input[name="' + item + '"]').val(e[item]).trigger('change');
                    }
                }

                $('select[name="tag"]').val(e.tag)
                    .trigger('change')
                    .selectpicker('refresh');

                $('select[name="estabelecimento"]').val(e.estabelecimento)
                    .selectpicker('refresh');

                atualiza_grupo_tarifario(e.tag, e.id_grupo_tarifario);
            }
        });

        $('select[name="tag"]').trigger('change');

        $('select[name="id_grupo_tarifario"], input[name="funcao"], input[name="inf_adicionais"], input[name="aplicacao"], \
            input[name="material_constitutivo"], input[name="marca"]')
            .on('change', function() {
                DESCONSIDERA_SIMILAR = true;
            });

        $('.selectpicker').selectpicker();
    });

    var DESCONSIDERA_SIMILAR = false;
    var POST_GRUPO_TARIFARIO = '<?php echo $this->input->post("id_grupo_tarifario") ?>';

    function atualiza_grupo_tarifario(tag, selected) {
        select_grupo_tarifario = $('select[name="id_grupo_tarifario"]');
        options_html = "";

        $(select_grupo_tarifario).find('option:not(:first)').remove();
        $(select_grupo_tarifario).selectpicker('refresh');
        $(select_grupo_tarifario).trigger('change');

        $.get("<?php echo site_url("cadastros/mestre_itens/json_lista_grupos") ?>?tag=" + tag, function(data) {
            $.each(data, function(item, v) {
                options_html += '<option value="' + v.id + '" ';

                if (selected && selected == v.id) {
                    options_html += 'selected="selected" ';
                } else if (POST_GRUPO_TARIFARIO && POST_GRUPO_TARIFARIO == v.id) {
                    options_html += 'selected="selected" ';
                    POST_GRUPO_TARIFARIO = '';
                }

                options_html += 'data-content="<span class=\'label label-primary\'>' + v.ncm_recomendada + '</span> ' + v.name + '">';
                options_html += '</option>';
            });

            $(select_grupo_tarifario).append(options_html);
        }).done(function() {
            $(select_grupo_tarifario).selectpicker('refresh');
            $(select_grupo_tarifario).trigger('change');

            DESCONSIDERA_SIMILAR = false;
        });
    }
</script>

<style type="text/css">
    #select-grupo-tarifario+.bootstrap-select li a {
        width: 100%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    #select-grupo-tarifario+.bootstrap-select .dropdown-menu.open {
        max-width: 800px;
    }
</style>

<?php echo form_open_multipart('', array('class' => 'form-horizontal', 'id' => 'myForm')) ?>

<div class="page-header">
    <h2>
        Novo item
    </h2>
</div>

<div class="form-group">
    <label for="input-part-number" class="col-sm-2 control-label">Part Number</label>
    <div class="col-sm-10">
        <input type="text" class="form-control" name="part_number" id="input-part-number" value="<?php echo set_value('part_number') ?>" placeholder="Part Number">
    </div>
</div>

<?php if (in_array('pn_primario_secundario', $campos_adicionais)) : ?>
    <div class="form-group">
        <label for="input-pn-primario-mpn" class="col-sm-2 control-label">PN Primário-MPN</label>
        <div class="col-sm-10">
            <input type="text" class="form-control" name="pn_primario_mpn" id="input-pn-primario-mpn" value="<?php echo set_value('pn_primario_mpn') ?>" placeholder="PN Primário">
        </div>
    </div>
    <div class="form-group">
        <label for="input-pn-secundario-ipn" class="col-sm-2 control-label">PN Secundário-IPN</label>
        <div class="col-sm-10">
            <input type="text" class="form-control" name="pn_secundario_ipn" id="input-pn-secundario-ipn" value="<?php echo set_value('pn_secundario_ipn') ?>" placeholder="PN Secundário">
        </div>
    </div>
<?php endif; ?>

<div class="form-group">
    <label for="estabelecimento" class="col-sm-2 control-label">Estabelecimento</label>
    <div class="col-sm-10">
        <select class="selectpicker" data-width="100%" name="estabelecimento" id="estabelecimento">
            <?php foreach ($estabelecimentos as $k => $estabelecimento) : ?>
                <?php if ($k == 1) : ?>
                    <option data-divider="true"></option>
                <?php endif ?>
                <option <?php echo set_select('estabelecimento', $estabelecimento) ?>><?php echo $estabelecimento ?></option>
            <?php endforeach ?>
        </select>
    </div>
</div>

<div class="form-group">
    <label for="input-part-number-similar" class="col-sm-2 control-label">Part Number Similar</label>
    <div class="col-sm-10">
        <input type="text" autocomplete="off" class="form-control" name="part_number_similar" id="input-part-number-similar" value="<?php echo set_value('part_number_similar') ?>" placeholder="Part Number Similar">
    </div>
</div>

<div class="form-group">
    <label for="input-descricao" class="col-sm-2 control-label">Descrição</label>
    <div class="col-sm-10">
        <input type="text" class="form-control" name="descricao" id="input-descricao" placeholder="Descrição" value="<?php echo set_value('descricao') ?>">
    </div>
</div>

<?php if ($hasDescricaoGlobal) : ?>
    <div class="form-group">
        <label for="descricao_global" class="col-sm-2 control-label">Descrição global</label>
        <div class="col-sm-10">
            <input type="text" class="form-control" name="descricao_global" id="input-descricao" placeholder="Descrição global" value="<?php echo set_value('descricao_global') ?>">
        </div>
    </div>
<?php endif ?>


<?php if (in_array('owner', $campos_adicionais)) : ?>
    <div class="form-group">
        <label for="input-owner" class="col-sm-2 control-label">Owner</label>
        <div class="col-sm-10">
            <select name="input-owner" id="input-owner" class="form-control select-picker">
                <option value="">Selecione</option>
                <?php foreach ($owners as $owner) : ?>
                    <option value="<?php echo $owner->codigo ?>" <?php echo set_select('input-owner', $owner->codigo) ?>>
                        <?php echo $owner->descricao ?>
                    </option>
                <?php endforeach ?>
            </select>
        </div>
    </div>
<?php endif; ?>

<?php if (!empty($empresa_pais)) : ?>
    <div class="form-group">
        <label for="input-ncm" class="col-sm-2 control-label">NCM</label>
        <div class="col-sm-10 input-group-data">
            <div class="input-group">
            <input type="text" class="form-control" name="ncm" id="input-ncm" placeholder="Ncm" value="<?php echo set_value('ncm') ?>">
            </div>
        </div>
    </div>
<?php else: ?>    
    <div class="form-group">
        <label for="input-ncm" class="col-sm-2 control-label">NCM</label>
        <div class="col-sm-10">
            <input type="text" class="form-control" name="ncm" id="input-ncm" placeholder="Ncm" value="<?php echo set_value('ncm') ?>">
        </div>
    </div>
<?php endif; ?>

<?php if ($can_ncm_fornecedor) : ?>
    <div class="form-group">
        <label for="input-ncm_fornecedor" class="col-sm-2 control-label">NCM Fornecedor</label>
        <div class="col-sm-10">
            <input type="text" class="form-control" name="ncm_fornecedor" id="input-ncm_fornecedor" placeholder="Ncm Fornecedor" value="<?php echo set_value('ncm_fornecedor') ?>">
        </div>
    </div>
<?php endif ?>

<div class="form-group">
    <label for="input-evento" class="col-sm-2 control-label">Evento</label>
    <div class="col-sm-10">
        <input type="text" class="form-control" name="evento" id="input-evento" placeholder="Evento" value="<?php echo set_value('evento') ?>">
    </div>
</div>

<div class="form-group">
    <label for="input-tag" class="col-sm-2 control-label">Agrupador</label>
    <div class="col-sm-10">
        <select name="tag" id="input-tag" class="form-control selectpicker" data-live-search="true">
            <option value="">[Selecione]</option>
            <?php foreach ($tags as $tag) : ?>
                <option <?php echo set_select('tag', $tag->tag) ?> value="<?php echo $tag->tag ?>"><?php echo $tag->tag ?></option>
            <?php endforeach; ?>
        </select>
    </div>
</div>

<div class="form-group">
    <label for="input-marca" class="col-sm-2 control-label">Ficha Técnica</label>
    <div class="col-sm-10">
        <input type="file" class="form-control" name="anexo" id="input-anexo" placeholder="Anexo" value="<?php echo set_value('anexo') ?>">
        <small class="file-info">Arquivos permitidos: <strong>DOC, XLS, ZIP/RAR, PDF, JPG/PNG/GIF/BMP/TIFF/TIF</strong>. Tamanho máximo permitido: <strong>20Mb</strong>.</small>
    </div>
</div>

<?php if (in_array('funcao', $campos_adicionais)) : ?>
    <div class="form-group form-group-funcao">
        <label for="input-funcao" class="col-sm-2 control-label">Função</label>
        <div class="col-sm-10">
            <input type="text" class="form-control" name="funcao" id="input-funcao" placeholder="Função" value="<?php echo set_value('funcao') ?>">
        </div>
    </div>
<?php endif ?>

<?php if (in_array('peso', $campos_adicionais)) : ?>
    <div class="form-group form-group-peso">
        <label for="input-peso" class="col-sm-2 control-label">Peso</label>
        <div class="col-sm-10">
            <input type="text" class="form-control" name="peso" id="input-peso" placeholder="Peso" value="<?php echo set_value('peso') ?>">
        </div>
    </div>
<?php endif ?>

<?php if (in_array('prioridade', $campos_adicionais)) : ?>
    <div class="form-group form-group-prioridade">
        <label for="input-prioridade" class="col-sm-2 control-label">Prioridade</label>
        <div class="col-sm-10">
            <select name="prioridade" id="input-prioridade"  class="form-control select-picker">
                <option value="">Selecione</option>
                <?php foreach ($empresa_prioridades as $prioridade) : ?>
                    <option value="<?php echo $prioridade->id_prioridade ?>" <?php echo $prioridade->nome == 'NORMAL' ? 'selected' : '' ?><?php echo set_select('prioridade', $prioridade->id_prioridade) ?>>
                        <?php echo $prioridade->nome ?>
                    </option>
                <?php endforeach ?>
            </select>
        </div>
    </div>
<?php endif ?>

<?php if (in_array('aplicacao', $campos_adicionais)) : ?>
    <div class="form-group form-group-aplicacao">
        <label for="input-aplicacao" class="col-sm-2 control-label">Aplicação</label>
        <div class="col-sm-10">
            <input type="text" class="form-control" name="aplicacao" id="input-aplicacao" placeholder="Aplicação" value="<?php echo set_value('aplicacao') ?>">
        </div>
    </div>
<?php endif ?>

<?php if (in_array('inf_adicionais', $campos_adicionais)) : ?>
    <div class="form-group form-group-inf_adicionais">
        <label for="input-inf_adicionais" class="col-sm-2 control-label">Informações Adicionais</label>
        <div class="col-sm-10">
            <input type="text" class="form-control" name="inf_adicionais" id="input-inf_adicionais" placeholder="Informações Adicionais" value="<?php echo set_value('inf_adicionais') ?>">
        </div>
    </div>
<?php endif ?>

<?php if (in_array('material_constitutivo', $campos_adicionais)) : ?>
    <div class="form-group form-group-material">
        <label for="input-material" class="col-sm-2 control-label">Material</label>
        <div class="col-sm-10">
            <input type="text" class="form-control" name="material_constitutivo" id="input-material" placeholder="Material" value="<?php echo set_value('material_constitutivo') ?>">
        </div>
    </div>
<?php endif ?>

<?php if (in_array('marca', $campos_adicionais)) : ?>
    <div class="form-group form-group-marca">
        <label for="input-marca" class="col-sm-2 control-label">Marca</label>
        <div class="col-sm-10">
            <input type="text" class="form-control" name="marca" id="input-marca" placeholder="Marca" value="<?php echo set_value('marca') ?>">
        </div>
    </div>
<?php endif ?>

<?php if (in_array('maquina', $campos_adicionais)) : ?>
    <div class="form-group form-group-maquina">
        <label for="input-maquina" class="col-sm-2 control-label">Máquina</label>
        <div class="col-sm-10">
            <input type="text" class="form-control" name="maquina" id="input-maquina" placeholder="Maquina" value="<?php echo set_value('maquina') ?>">
        </div>
    </div>
<?php endif ?>

<?php if (in_array('origem', $campos_adicionais)) : ?>
    <div class="form-group form-group-origem">
        <label for="input-origem" class="col-sm-2 control-label">Origem</label>
        <div class="col-sm-10">
            <input type="text" class="form-control" name="origem" id="input-origem" placeholder="Origem (País)" value="<?php echo set_value('origem') ?>">
        </div>
    </div>
<?php endif ?>

<?php if (in_array('memoria_classificacao', $campos_adicionais)) : ?>
    <div class="form-group form-group-memoria-classificacao">
        <label for="input-memoria-classificacao" class="col-sm-2 control-label">Memória de Classificação</label>
        <div class="col-sm-10">
            <input type="text" class="form-control" name="memoria_classificacao" id="input-memoria-classificacao" placeholder="Memória de Classificação" value="<?php echo set_value('memoria_classificacao') ?>" />
        </div>
    </div>
<?php endif ?>

<?php if (has_role('sysadmin') || has_role('cadastro_itens')) : ?>
    <div class="form-group">
        <label for="input-resp-engenharia" class="col-sm-2 control-label">Responsável Engenharia</label>
        <div class="col-sm-10">
            <select name="id_resp_engenharia" class="form-control selectpicker" data-live-search="true" data-size="5" id="input-resp-engenharia">
                <option value="">[Selecione]</option>
                <?php foreach ($usuarios_eng as $usuario) : ?>
                    <option <?php echo set_select('id_resp_engenharia', $usuario->id_usuario) ?> value="<?php echo $usuario->id_usuario ?>"><?php echo $usuario->nome ?></option>
                <?php endforeach; ?>
            </select>
        </div>
    </div>
<?php endif; ?>

<div class="form-group">
    <label for="input-resp-fiscal" class="col-sm-2 control-label">Responsável Fiscal</label>
    <div class="col-sm-10">
        <select name="id_resp_fiscal" class="form-control selectpicker" data-live-search="true" data-size="5" id="input-resp-fiscal">
            <option value="">[Selecione]</option>
            <?php foreach ($usuarios_fiscal as $usuario) : ?>
                <option <?php echo $empresa->id_resp_fiscal == $usuario->id_usuario ? 'selected' : '' ?> value="<?php echo $usuario->id_usuario ?>"><?php echo $usuario->nome ?></option>
            <?php endforeach; ?>
        </select>
    </div>
</div>

<hr>
<?php $has_permission = (customer_has_role('atribuir_grupo_tarifario_part_number', sess_user_id()) == TRUE && customer_has_role('classificar_itens_sem_pn', sess_user_id()) == TRUE); ?>

<div class="form-group">
    <label for="select-grupo-tarifario" class="col-sm-2 control-label">Grupo Tarifário</label>
    <div class="col-sm-10">
        <select <?php echo !$has_permission ? 'disabled' : ''; ?> name="id_grupo_tarifario" id="select-grupo-tarifario" class="selectpicker dropup" data-width="100%" data-live-search="true" data-none-results-text="Nenhum resultado encontrado para {0}">
            <option value="">[Selecione]</option>
        </select>
    </div>
</div>

<?php if (in_array('owner', $campos_adicionais)) : ?>
    <div class="form-group">
        <!-- Checkbox para validar se o usuário deseja receber as alterações de status desse item -->
        <div class="col-sm-offset-2 col-sm-10">
            <div class="checkbox">
                <label>
                    <input type="checkbox" name="recebe_email_alteracao_status" value="1" <?php echo set_checkbox('recebe_email_alteracao_status', '1') ?> checked> Responsável Recebe Email de Alteração de Status
                    <i data-toggle="tooltip" title="O responsável será definido automaticamente como o usuário que criou o item" class="glyphicon glyphicon-info-sign"></i>
                </label>
            </div>
        </div>
    </div>

<?php endif ?>

    <div class="form-group">
        <!-- Checkbox para definir o item como gestão mensal -->
        <div class="col-sm-offset-2 col-sm-10">
            <div class="checkbox">
                <label>
                    <input type="checkbox" name="gestao_mensal" value="1" <?php echo set_value('gestao_mensal', '1') ? 'checked' : '' ?>> Gestão Mensal
                    <i data-toggle="tooltip" title="Define se o item corresponde a gestão mensal" class="glyphicon glyphicon-info-sign"></i>
                    </label>
            </div>
        </div>
    </div>
    <div class="form-group">
        <!-- Checkbox para validar se o usuário deseja receber as alterações de status desse item -->
        <div class="col-sm-offset-2 col-sm-10">
            <div class="checkbox">
                <label>
                    <input type="checkbox" name="item_importado_default" value="1" <?php   echo set_checkbox('item_importado_default', '1'); echo (!empty($importado_default)) ? 'checked' : '';  ?> > Item importado 
                    <i data-toggle="tooltip" title="Definir esse part number como importado" class="glyphicon glyphicon-info-sign"></i>
                </label>
            </div>
        </div>
    </div>

<div class="form-group">
    <div class="col-sm-offset-2 col-sm-10">
        <button type="submit" class="btn btn-primary" value="1" name="submit"><i class="glyphicon glyphicon-floppy-disk"></i> Salvar</button>
        <a href="<?php echo site_url("cadastros/mestre_itens") ?>" class="btn">Cancelar</a>
    </div>
</div>

</form>