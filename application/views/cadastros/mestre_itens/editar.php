<script>
    $(function() {
        const base_url = "<?php echo base_url() ?>";
        var FORM = $('#form-edit');

        $(FORM).find(".btn-submit").on('click', function(e) {
            <?php if ($integracao_simplus) : ?>
                var status_simplus = $(FORM).find('[name="status_simplus"]').val();
                var item_id = $(FORM).find('[name="cad_item_id"]').val();

                if (status_simplus == 1) {
                    swal({
                        title: 'Integração Simplus',
                        confirmButtonText: 'Sim',
                        cancelButtonText: 'Não',
                        showCancelButton: true,
                        text: '<p><strong>Atenção!</strong> O item já foi enviado para a Simplus.</p>' +
                            '<p>Você gostaria de voltar o status do item para <strong>pendente de envio</strong>?</p>',
                        showLoaderOnConfirm: true,
                        allowOutsideClick: false
                    }).then(function() {
                        return new Promise(function(resolve) {
                            $.post('<?php echo site_url("homologacao/atualiza_simplus") ?>', {
                                    "id_item": item_id
                                },
                                function(data) {
                                    $("#form-edit [name='submit']").click();
                                });
                        })
                    }, function(dismiss) {
                        if (dismiss === 'cancel') {
                            $("#form-edit [name='submit']").click();
                        }
                    });
                } else {
                    $("#form-edit [name='submit']").click();
                }
            <?php else : ?>
                $("#form-edit [name='submit']").click();
            <?php endif ?>
        });

        $('select[name="tag"]').on('change', function() {
            var tag = $(this).val();
            atualiza_grupo_tarifario(tag);
        });

        $('input[name="part_number_similar"]').typeahead({
            source: function(query, process) {
                return $.get("<?php echo site_url("cadastros/mestre_itens/json_lista_itens") ?>?pn=" + PN + "&estabelecimento=" + ESTAB + "&search=" + query, function(data) {
                    process(data);
                });
            },
            afterSelect: function(e) {
                if (DESCONSIDERA_SIMILAR) {
                    return false;
                }

                var fields = ["ncm", "descricao", "funcao", "inf_adicionais", "material_constitutivo", "aplicacao", "marca"];

                for (var i in fields) {
                    var item = fields[i];

                    $('input[name="' + item + '"]').val('');

                    if (typeof e[item] != "undefined") {
                        $('input[name="' + item + '"]').val(e[item]).trigger('change');
                    }
                }

                $('select[name="tag"]').val(e.tag)
                    .trigger('change')
                    .selectpicker('refresh');

                $('select[name="estabelecimento"]').val(e.estabelecimento)
                    .selectpicker('refresh');

                atualiza_grupo_tarifario(e.tag, e.id_grupo_tarifario);
            }
        });

        $('select[name="id_grupo_tarifario"], input[name="funcao"], input[name="inf_adicionais"], input[name="aplicacao"], \
            input[name="material_constitutivo"], input[name="marca"]')
            .on('change', function() {
                DESCONSIDERA_SIMILAR = true;
            });

        $('select[name="tag"]').trigger('change');

        $('#bt-modal-multi-paises').on('click', function() {
           $.ajax({
               url: base_url + 'cadastros/mestre_itens/ajax_get_dados_modal_paises',
               method: 'POST',
               data: {
                   part_number: $("#input-part-number-modal").val(),
                   estabelecimento: $("#estabelecimento-modal").val(),
                   id_empresa: $("#id_empresa").val()
               },
               dataType: 'json',
               success: function(dados) {

                   if (dados && dados.empresa_pais) {
                       dados.empresa_pais.forEach(pais => {
                           const slug = pais.slug;
                           if(pais.item_pais.length > 0){
                               pais.item_pais.forEach(item => {
                                   // Atribuindo os valores aos campos usando jQuery
                                   $('#codigo_classificacao_' + slug).val(item.codigo_classificacao);
                                   $('#desc_curta_' + slug).val(item.descricao_curta);
                                   $('#desc_completa_' + slug).val(item.descricao_completa);
                                   $('#desc_li_' + slug).val(item.li);
                                   $('#desc_adicional_' + slug).val(item.informacao_adicional);
                               });
                           }else{
                               $('#codigo_classificacao_' + slug).val("");
                               $('#desc_curta_' + slug).val("");
                               $('#desc_completa_' + slug).val("");
                               $('#desc_li_' + slug).val("");
                               $('#desc_adicional_' + slug).val("");
                           }
                       });
                   }
               },
               error: function() {
                   $('#message_user_transfer_owner_paises').html('<div class="alert alert-danger"><button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>Erro ao enviar os dados para o servidor.</div>');
                   $('#message_user_transfer_owner_paises').show();
                   $('#loading-img').hide();
                   $('#btn-salvar-paises').prop('disabled', false);
                   $('#btn-salvar-paises').html('Salvar');
               }
           });
        });
    });

    DESCONSIDERA_SIMILAR = false;
    PN = '<?php echo $entry->part_number ?>';
    ESTAB = '<?php echo $entry->estabelecimento ?>';
    POST_GRUPO_TARIFARIO = '<?php echo $this->input->post("id_grupo_tarifario") ? $this->input->post("id_grupo_tarifario") : (isset($cad_item) ? $cad_item->id_grupo_tarifario : "") ?>';

    function atualiza_grupo_tarifario(tag, selected) {
        select_grupo_tarifario = $('select[name="id_grupo_tarifario"]');
        options_html = "";

        $(select_grupo_tarifario).find('option:not(:first)').remove();
        $(select_grupo_tarifario).selectpicker('refresh');
        $(select_grupo_tarifario).trigger('change');

        $.get("<?php echo site_url("cadastros/mestre_itens/json_lista_grupos") ?>?tag=" + tag, function(data) {
            $.each(data, function(item, v) {
                options_html += '<option value="' + v.id + '" ';

                if (selected && selected == v.id) {
                    options_html += 'selected="selected" ';
                } else if (POST_GRUPO_TARIFARIO && POST_GRUPO_TARIFARIO == v.id) {
                    options_html += 'selected="selected" ';
                    POST_GRUPO_TARIFARIO = '';
                }

                options_html += 'data-content="<span class=\'label label-primary\'>' + v.ncm_recomendada + '</span> ' + v.name + '">';
                options_html += '</option>';
            });

            $(select_grupo_tarifario).append(options_html);
        }).done(function() {
            $(select_grupo_tarifario).selectpicker('refresh');
            $(select_grupo_tarifario).trigger('change');

            DESCONSIDERA_SIMILAR = false;
        });
    }

    function remove_arquivo() {
        swal({
            title: 'Você tem certeza?',
            text: "Você deseja remover o arquivo?",
            type: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Sim, remova!',
            cancelButtonText: 'Não, cancele.',
            confirmButtonClass: 'btn btn-danger btn-margin-right',
            cancelButtonClass: 'btn',
            buttonsStyling: false
        }).then(function() {
            swal(
                'OK!',
                'O arquivo será removido após o envio do formulário.',
                'success'
            );

            $("#block-anexo").addClass('hide');
            $("#anexo-upload-block").removeClass('hide');
            $("input[name='remover_anexo']").val(1);
        }, function(dismiss) {
            if (dismiss === 'cancel') {
                swal(
                    'Cancelado!',
                    'O arquivo está a salvo!',
                    'error'
                )
            }
        });
    }

</script>


<style>
    /* .input-group-data {
        margin-bottom: 15px;
    } */
</style>

<?php
$form_data = array(
    'part_number'       => $entry->part_number,
    'estabelecimento'   => $entry->estabelecimento,
    'id_empresa'        => $entry->id_empresa
);

$form_data = array_filter($form_data, 'rawurlencode');
$form_qs = http_build_query($form_data);

echo form_open_multipart("cadastros/mestre_itens/editar?{$form_qs}", array('id' => 'form-edit', 'class' => 'form-horizontal'));
?>
<?php if (isset($show_edit_admin)) : ?>
    <div class="alert alert-info alert-dismissable">
        <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
        Você está alterando um grupo já atribuído (CAD_ITEM), com impacto nas abas de <strong>Revisão</strong> e <strong>Homologação</strong>.
    </div>
<?php endif; ?>

<div class="page-header">
    <!-- <?php if (isset($show_message) && !empty($show_message)): ?>
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-<?php echo $show_message['type'] ?> alert-dismissable">
                    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                    <?php echo $show_message['message'] ?>
                </div>
            </div>
        </div>
    <?php endif; ?> -->
    <div class="row d-flex">
        <div class="col-sm-4">
            <h2>
                Editar item
            </h2>
        </div>
        <div class="col-lg-8 col-sm-8 col-md-offset-5 d-flex justify-content-end">
            <?php if (in_array('usuario_seguidor', $campos_adicionais)) : ?>
                <div id="modal-usuarios" style="margin-top: 20px; margin-right: 10px">
                        <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#myModal"><span class="glyphicon glyphicon-user"></span>
                            Usuários Seguidores</button>
                </div>
                    <?php $this->load->view('cadastros/mestre_itens/modal_usuarios_seguidores'); ?>
            <?php endif; ?> 
            <div id="app-modal-historico-item" style="margin-top: 20px;">
                <v-modal-historico-item :partnumber="'<?php echo $entry->part_number; ?>'" :estabelecimento="'<?php echo $entry->estabelecimento; ?>'" :base-url="'<?php echo base_url(); ?>'">
                </v-modal-historico-item>
            </div>
        </div>
    </div>
    <div id="ajax_validate"></div>
</div>
<script type="text/javascript" src="<?php echo base_url('assets/vuejs/dist/modal-pr-historico-item.js?version=' . config_item('assets_version')) ?>"></script>
<link rel="stylesheet" href="<?php echo base_url('assets/vuejs/dist/modal-pr-historico-item.css?version=' . config_item('assets_version')) ?>" type="text/css" media="screen" />

<div class="row">
    <div class="col-md-10 col-md-offset-2">
        <div class="col-md-5">
            <label for="input-status">Status</label>
            <br />
            <span class="label label-primary" style="font-size: 13px"><?php echo $entry->status_formatado ?></span>
        </div>

        <div class="col-md-4">
            <label for="input-status">Criado em:</label>
            <?php echo date("d/m/Y H:i:s", strtotime($entry->dat_criacao)) ?>
            <?php if (!empty($entry->data_modificacao)) : ?>
                <br />
                <label for="input-status">Modificado em:</label>
                <?php echo date("d/m/Y H:i:s", strtotime($entry->data_modificacao)) ?>
            <?php endif ?>
            <?php if (isset($cad_item) && !empty($cad_item->dat_disp_homologacao)) : ?>
                <br />
                <label for="input-status">Disponível para Homologação:</label>
                <?php echo date("d/m/Y H:i:s", strtotime($cad_item->dat_disp_homologacao)) ?>
            <?php endif ?>
        </div>
    </div>
</div>

<hr />

<div class="form-group">
    <label for="input-nome" class="col-sm-2 control-label">Part Number</label>
    <div class="col-sm-10 input-group-data">
        <input type="text" class="form-control" readonly id="input-part-number" value="<?php echo set_value('part_number', $entry->part_number) ?>" placeholder="Part Number">
        <input type="hidden" class="form-control" name="part_number" id="input-part-number" value="<?php echo set_value('part_number', $entry->part_number) ?>">
    </div>
</div>

<?php if (in_array('pn_primario_secundario', $campos_adicionais)) : ?>
    <div class="form-group">
        <label for="input-pn-primario-mpn" class="col-sm-2 control-label">PN Primário-MPN</label>
        <div class="col-sm-10">
            <input type="text" class="form-control" name="pn_primario_mpn" id="input-pn-primario-mpn" value="<?php echo set_value('pn_primario_mpn', $entry->pn_primario_mpn) ?>" placeholder="PN Primário">
        </div>
    </div>
    <div class="form-group">
        <label for="input-pn-secundario-ipn" class="col-sm-2 control-label">PN Secundário-IPN</label>
        <div class="col-sm-10">
            <input type="text" class="form-control" name="pn_secundario_ipn" id="input-pn-secundario-ipn" value="<?php echo set_value('pn_secundario_ipn', $entry->pn_secundario_ipn) ?>" placeholder="PN Secundário">
        </div>
    </div>
<?php endif; ?>

<div class="form-group">
    <label for="input-senha" class="col-sm-2 control-label">Estabelecimento</label>
    <div class="col-sm-10 input-group-data">
        <input type="text" name="estabelecimento" class="form-control" readonly value="<?php echo $entry->estabelecimento ?>" />
    </div>
</div>

<div class="form-group">
    <label for="input-senha" class="col-sm-2 control-label">Part Number Similar</label>
    <div class="col-sm-10 input-group-data">
        <input type="text" class="form-control" name="part_number_similar" id="input-part-number-similar" placeholder="Part Number Similar" value="<?php echo set_value('part_number_similar', $entry->part_number_similar) ?>">
    </div>
</div>

<div class="form-group">
    <label for="input-email" class="col-sm-2 control-label">Descrição</label>
    <div class="col-sm-10 input-group-data">
        <input type="text" class="form-control" name="descricao" id="input-descricao" placeholder="Descrição" value="<?php echo set_value('descricao', $entry->descricao) ?>">
    </div>
</div>

<?php if ($hasDescricaoGlobal) : ?>
    <div class="form-group">
        <label for="descricao_global" class="col-sm-2 control-label">Descrição global</label>
        <div class="col-sm-10">
            <input type="text" class="form-control" name="descricao_global" id="input-descricao" placeholder="Descrição global" value="<?php echo set_value('descricao_global', $entry->descricao_global) ?>">
        </div>
    </div>
<?php endif ?>
<?php $has_permission = customer_has_role('alterar_owner', sess_user_id()); ?>

<?php if (in_array('owner', $campos_adicionais)) : ?>
    <div class="form-group">
        <label for="input-owner" class="col-sm-2 control-label">Owner</label>
        <div class="col-sm-10" >
            <select name="input-owner" id="input-owner" class="form-control select-picker" <?php echo !$has_permission ? 'disabled' : ''; ?>>
                <option value="">Selecione</option>
                <?php foreach ($owners as $owner) : ?>
                    <option value="<?php echo $owner->codigo ?>" <?php echo set_select('input-owner', $owner->codigo, $owner->codigo == $entry->cod_owner) ?>>
                        <?php echo $owner->descricao ?>
                    </option>
                <?php endforeach ?>
            </select>
        </div>
    </div>
<?php endif; ?>


<div class="form-group">
    <label for="input-senha" class="col-sm-2 control-label">NCM </label>
    <?php if(!empty($empresa_pais)): ?>
    <div class="col-sm-10 input-group-data">
        <div class="input-group">
            <input type="text" class="form-control" name="ncm" id="input-ncm" placeholder="Ncm" value="<?php echo set_value('ncm', $entry->ncm) ?>">
                <span class="input-group-btn">
                    <button id="bt-modal-multi-paises" class="btn btn-primary" type="button" data-toggle="modal" data-target="#modal-multi-paises"><i data-toggle="tooltip" title="Esta função permitirá efetuar a classificação do item para diversos países" class="glyphicon glyphicon-globe"></i> Multi Países</button>
                </span>
        </div>
    </div>
    <?php else: ?>
        <div class="col-sm-10">
            <input type="text" class="form-control" name="ncm" id="input-ncm" placeholder="Ncm" value="<?php echo set_value('ncm', $entry->ncm) ?>">
        </div>
    <?php endif; ?>
        
</div>

<?php if ($can_ncm_fornecedor) : ?>
    <div class="form-group">
        <label for="input-senha" class="col-sm-2 control-label">NCM Fornecedor</label>
        <div class="col-sm-10 input-group-data">
            <input type="text" class="form-control" name="ncm_fornecedor" id="input-ncm_fornecedor" placeholder="Ncm Fornecedor" value="<?php echo set_value('ncm_fornecedor', $entry->ncm_fornecedor) ?>">
        </div>
    </div>
<?php endif; ?>

<div class="form-group">
    <label for="input-evento" class="col-sm-2 control-label">Evento</label>
    <div class="col-sm-10 input-group-data">
        <input type="text" class="form-control" name="evento" id="input-evento" placeholder="Evento" value="<?php echo set_value('evento', $entry->evento) ?>">
    </div>
</div>

<div class="form-group">
    <label for="input-tag" class="col-sm-2 control-label">Agrupador</label>
    <div class="col-sm-10 input-group-data">
        <select name="tag" class="form-control selectpicker" data-live-search="true">
            <option value="">[Selecione]</option>
            <?php foreach ($tags as $tag) : ?>
                <option <?php echo set_select('tag', $tag->tag, $tag->tag == $entry->tag) ?> value="<?php echo $tag->tag ?>"><?php echo $tag->tag ?></option>
            <?php endforeach; ?>
        </select>
    </div>
</div>

<div class="form-group">
    <label for="input-marca" class="col-sm-2 control-label">Ficha Técnica</label>
    <div class="col-sm-10 input-group-data">
        <?php
        if (count($ficha_tecnica)) :
            $arquivo = current($ficha_tecnica);
            $download_url = site_url('download?arquivo=' . $arquivo->nome_hash);
        ?>
            <div id="block-anexo" class="input-group">
                <input type="text" class="form-control" readonly value="<?php printf("%s.%s", $arquivo->nome_arquivo, $arquivo->extensao) ?>">

                <div class="input-group-btn">
                    <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <span class="caret"></span>
                        <span class="sr-only"></span>
                    </button>

                    <ul class="dropdown-menu pull-right">
                        <li><a href="<?php echo $download_url ?>"><i class="glyphicon glyphicon-cloud-download"></i> Baixar</a></li>
                        <li><a href="javascript: void(0)" onclick="javascript: remove_arquivo()"><i class="glyphicon glyphicon glyphicon-remove"></i> Excluir</a></li>
                    </ul>
                </div>
            </div>

            <input type="hidden" name="remover_anexo" value="0" />

        <?php endif; ?>

        <div id="anexo-upload-block" class="<?php echo count($ficha_tecnica) ? 'hide' : '' ?>">
            <input type="file" class="form-control" name="anexo" placeholder="Anexo" value="<?php echo set_value('anexo') ?>">
            <small class="file-info">Arquivos permitidos: <strong>DOC, XLS, ZIP/RAR, PDF, JPG/PNG/GIF/BMP/TIFF/TIF</strong>. Tamanho máximo permitido: <strong>20Mb</strong>.</small>
        </div>
    </div>
</div>

<?php if (in_array('funcao', $campos_adicionais)) : ?>
    <div class="form-group form-group-funcao">
        <label for="input-funcao" class="col-sm-2 control-label">Função</label>
        <div class="col-sm-10 input-group-data">
            <input type="text" class="form-control" name="funcao" id="input-funcao" placeholder="Função" value="<?php echo set_value('funcao', (isset($entry) ? $entry->funcao : NULL)) ?>">
        </div>
    </div>
<?php endif ?>

<?php if (in_array('peso', $campos_adicionais)) : ?>
    <div class="form-group form-group-peso">
        <label for="input-peso" class="col-sm-2 control-label">Peso</label>
        <div class="col-sm-10 input-group-data">
            <input type="text" class="form-control" name="peso" id="input-peso" placeholder="Peso" value="<?php echo set_value('peso', (isset($entry) ? $entry->peso : NULL)) ?>">
        </div>
    </div>
<?php endif ?>

<?php if (in_array('prioridade', $campos_adicionais)) : ?>
    <?php 
        $has_permission = customer_has_role('alterar_criticidade', sess_user_id());
    ?>
    <div class="form-group form-group-prioridade">
        <label for="input-prioridade" class="col-sm-2 control-label">Prioridade</label>
        <div class="col-sm-10 input-group-data">
        <select name="prioridade" id="input-prioridade" class="form-control select-picker" <?php echo !$has_permission ? 'disabled' : ''; ?>>
            <option value="">Selecione</option>
            <?php foreach ($empresa_prioridades as $prioridade) : ?>
                <option value="<?php echo $prioridade->id_prioridade ?>" <?php echo set_select('prioridade', $prioridade->id_prioridade, $prioridade->id_prioridade == $entry->id_prioridade) ?>>
                    <?php echo $prioridade->nome ?>
                </option>
            <?php endforeach ?>
        </select>
        </div>
    </div>
<?php endif ?>

<?php if (in_array('aplicacao', $campos_adicionais)) : ?>
    <div class="form-group form-group-aplicacao">
        <label for="input-aplicacao" class="col-sm-2 control-label">Aplicação</label>
        <div class="col-sm-10 input-group-data">
            <input type="text" class="form-control" name="aplicacao" id="input-aplicacao" placeholder="Aplicação" value="<?php echo set_value('aplicacao', (isset($entry) ? $entry->aplicacao : NULL)) ?>">
        </div>
    </div>
<?php endif ?>

<?php if (in_array('inf_adicionais', $campos_adicionais)) : ?>
    <div class="form-group form-group-inf_adicionais">
        <label for="input-inf_adicionais" class="col-sm-2 control-label">Informações Adicionais</label>
        <div class="col-sm-10 input-group-data">
            <input type="text" class="form-control" name="inf_adicionais" id="input-inf_adicionais" placeholder="Informações Adicionais" value="<?php echo set_value('inf_adicionais', (isset($entry) ? $entry->inf_adicionais : NULL)) ?>">
        </div>
    </div>
<?php endif ?>

<?php if (in_array('material_constitutivo', $campos_adicionais)) : ?>
    <div class="form-group form-group-material">
        <label for="input-material" class="col-sm-2 control-label">Material</label>
        <div class="col-sm-10 input-group-data">
            <input type="text" class="form-control" name="material_constitutivo" id="input-material" placeholder="Material" value="<?php echo set_value('material_constitutivo', (isset($entry) ? $entry->material_constitutivo : NULL)) ?>">
        </div>
    </div>
<?php endif ?>

<?php if (in_array('marca', $campos_adicionais)) : ?>
    <div class="form-group form-group-marca">
        <label for="input-marca" class="col-sm-2 control-label">Marca</label>
        <div class="col-sm-10 input-group-data">
            <input type="text" class="form-control" name="marca" id="input-marca" placeholder="Marca" value="<?php echo set_value('marca', (isset($entry) && isset($entry->marca) ? $entry->marca : NULL)) ?>">
        </div>
    </div>
<?php endif ?>

<?php if (in_array('maquina', $campos_adicionais)) : ?>
    <div class="form-group form-group-maquina">
        <label for="input-maquina" class="col-sm-2 control-label">Máquina</label>
        <div class="col-sm-10 input-group-data">
            <input type="text" class="form-control" name="maquina" id="input-maquina" placeholder="Maquina" value="<?php echo set_value('maquina', (isset($entry) && isset($entry->maquina) ? $entry->maquina : NULL)) ?>">
        </div>
    </div>
<?php endif ?>

<?php if (in_array('origem', $campos_adicionais)) : ?>
    <div class="form-group form-group-origem">
        <label for="input-origem" class="col-sm-2 control-label">Origem</label>
        <div class="col-sm-10 input-group-data">
            <input type="text" class="form-control" name="origem" id="input-origem" placeholder="Origem (País)" value="<?php echo set_value('origem', (isset($entry) && isset($entry->origem) ? $entry->origem : NULL)) ?>">
        </div>
    </div>
<?php endif ?>
<?php if (in_array('memoria_classificacao', $campos_adicionais)) : ?>
    <div class="form-group form-group-memoria-classificacao">
        <label for="input-marca" class="col-sm-2 control-label">Memória de Classificação</label>
        <div class="col-sm-10 input-group-data">
            <input type="text" class="form-control" name="memoria_classificacao" id="input-memoria-classificacao" placeholder="Memória de Classificação" value="<?php echo set_value('memoria_classificacao', (isset($entry) && isset($entry->memoria_classificacao) ? $entry->memoria_classificacao : NULL)) ?>" />
        </div>
    </div>
<?php endif ?>

<?php if (in_array('observacoes', $campos_adicionais)) : ?>
    <div class="form-group form-group-observacoes">
        <label for="input-marca" class="col-sm-2 control-label">Observações</label>
        <div class="col-sm-10 input-group-data">
            <input type="text" class="form-control" name="observacoes" id="input-observacoes" placeholder="Observações" value="<?php echo set_value('observacoes', (isset($entry) && isset($entry->observacoes) ? $entry->observacoes : NULL)) ?>" />
        </div>
    </div>
<?php endif ?>

<?php if (has_role('sysadmin') || has_role('consultor') || has_role('cadastro_itens')) : ?>
    <div class="form-group">
        <label for="input-resp-engenharia" class="col-sm-2 control-label">Responsável Engenharia</label>
        <div class="col-sm-10 input-group-data">
            <select name="id_resp_engenharia" class="form-control selectpicker" data-live-search="true" data-size="5" id="input-resp-engenharia">
                <option value="">[Selecione]</option>
                <?php foreach ($usuarios_eng as $usuario) : ?>
                    <option <?php echo $usuario->id_usuario == $entry->id_resp_engenharia ? 'selected' : '' ?> value="<?php echo $usuario->id_usuario ?>"><?php echo $usuario->nome ?></option>
                <?php endforeach; ?>
            </select>
        </div>
    </div>
<?php endif; ?>

<div class="form-group">
    <label for="input-resp-fiscal" class="col-sm-2 control-label">Responsável Fiscal</label>
    <div class="col-sm-10 input-group-data">
        <select name="id_resp_fiscal" class="form-control selectpicker" data-live-search="true" data-size="5" id="input-resp-fiscal">
            <option value="">[Selecione]</option>
            <?php foreach ($usuarios_fiscal as $usuario) : ?>
                <option <?php echo $usuario->id_usuario == $entry->id_resp_fiscal ? 'selected' : '' ?> value="<?php echo $usuario->id_usuario ?>"><?php echo $usuario->nome ?></option>
            <?php endforeach; ?>
        </select>
    </div>
</div>

<hr>
<?php $has_permission = (customer_has_role('atribuir_grupo_tarifario_part_number', sess_user_id()) == TRUE && customer_has_role('classificar_itens_sem_pn', sess_user_id()) == TRUE); ?>
<div class="form-group">
    <label for="input-grupo_tarifario" class="col-sm-2 control-label">Grupo Tarifário</label>
    <div class="col-sm-10 input-group-data">
        <input type="hidden" disabled class="form-control" name="id_grupo_tarifario" value="<?php echo set_value('id_grupo_tarifario', (isset($grupo) ? $grupo->id_grupo_tarifario : NULL)) ?>">
        <select name="id_grupo_tarifario" id="select-grupo-tarifario" class="selectpicker dropup" data-width="100%" data-live-search="true" data-none-results-text="Nenhum resultado encontrado para {0}" <?php echo !$has_permission ? 'disabled' : ''; ?>>
            <option value="">[Selecione]</option>
        </select>
    </div>
</div>

<?php if (in_array('owner', $campos_adicionais)) : ?>
    <div class="form-group">
        <!-- Checkbox para validar se o usuário deseja receber as alterações de status desse item -->
        <div class="col-sm-offset-2 col-sm-10">
            <div class="checkbox">
                <label>
                    <input type="checkbox" name="recebe_email_alteracao_status" value="1" <?php echo set_value('recebe_email_alteracao_status', $entry->recebe_email_alteracao_status) ? 'checked' : '' ?>> Responsável Recebe Email de Alteração de Status
                    <i data-toggle="tooltip" title="O responsável será definido automaticamente como o usuário que criou o item" class="glyphicon glyphicon-info-sign"></i>
                </label>
            </div>
        </div>
    </div>

<?php endif ?>

    <div class="form-group">
        <div class="col-sm-offset-2 col-sm-10">
            <div class="checkbox">
                <label>
                    <input type="checkbox" name="item_importado_default" value="1" <?php echo set_value('item_importado_default', $entry->importado) ? 'checked' : '' ?>> Item importado 
                    <i data-toggle="tooltip" title="Definir esse part number como importado" class="glyphicon glyphicon-info-sign"></i>
                </label>
            </div>
        </div>
    </div>

<?php if (isset($cad_item) && customer_has_role('enviar_itens_revisao', sess_user_id())) : ?>
    <hr />

    <div class="form-group">
        <div class="col-sm-2"></div>
        <div class="col-sm-10 input-group-data">
            <label for="enviar-para-revisao" for="enviar-revisao">
                <input type="checkbox" name="revisao" id="enviar-para-revisao" value="1" /> Enviar para Revisão
                <i class="glyphicon glyphicon-info-sign" data-toggle="tooltip" data-title="Utilize esta opção para enviar o item novamente para a revisão dos consultores da Becomex."></i>
            </label>
        </div>
    </div>
<?php endif ?>

<div class="form-group">
        <!-- Checkbox para definir o item como gestão mensal -->
        <div class="col-sm-offset-2 col-sm-10">
            <div class="checkbox">
                <label>
                    <input type="checkbox" name="gestao_mensal" value="1" <?php echo set_value('gestao_mensal', $entry->gestao_mensal) ? 'checked' : '' ?>> Gestão Mensal
                    <i data-toggle="tooltip" title="Define se o item corresponde a gestão mensal" class="glyphicon glyphicon-info-sign"></i>
                </label>
            </div>
        </div>
    </div>

    <?php if (customer_has_role('inativar_pns', sess_user_id())) : ?>
    <div class="form-group">
        <div class="col-sm-offset-2 col-sm-10">
            <div class="checkbox">
                <label style=" padding-left: 0px;"> <b>Status do Part Number </b>
                    <div class="toggle-switch" style=" margin-left: -20px; padding-top: 13px;">
                        <input type="hidden" name="inativar_pn" value="0">
                        <input type="checkbox" id="inativar_pn" name="inativar_pn" value="1" class="toggle-checkbox">
                        <label for="inativar_pn" class="toggle-label">
                            <span class="toggle-inner"></span>
                            <span id="toggle-switch-text" class="toggle-switch-text"></span>
                        </label>
                    </div>
                </label>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12 mb-3">
            <label for="motivo_select" class="form-label">Motivo</label>
            <select class="selectpicker form-control motivo-select" id="motivo_select" name="motivo_select">
                <?php foreach ($motivos as $motivo) { ?>
                    <option value="<?php echo $motivo->motivo ?>">
                        <?php echo $motivo->motivo ?>
                    </option>
                <?php } ?>
                <option value=" ">Outra situação</option>
                
            </select>
        </div>

        <div class="col-12 mb-3">
            <label for="motivo" class="form-label">Descrição</label>
            <textarea class="form-control" id="motivo" name="motivo" rows="3"></textarea>
        </div>
    </div>

    <?php endif ?>
 

<div class="form-group">
    <div class="text-right col-sm-12">
        <a href="<?php echo site_url("cadastros/mestre_itens"); ?>" class="btn">Cancelar</a>
        <button type="button" class="btn btn-primary btn-submit" value="1"><i class="glyphicon glyphicon-floppy-disk"></i> Salvar</button>
    </div>
</div>

<?php if ($integracao_simplus) : ?>
    <input type="hidden" name="status_simplus" value="<?php echo (isset($cad_item) ? $cad_item->status_simplus : 0) ?>" />
    <input type="hidden" name="cad_item_id" value="<?php echo (isset($cad_item) ? $cad_item->id_item : NULL) ?>" />
<?php endif ?>

<input type="submit" name="submit" value="1" class="hide" />
</form>

<?php if ($empresa_pais) : ?>
    <?php $this->load->view('cadastros/mestre_itens/modal-multi-paises', 'empresa_pais', 'entry'); ?>
<?php endif;?>   


<script>
    let status_pn = "<?php echo $entry->id_status; ?>";

    var statusInativacao = status_pn != 4 ? 1 : 0; // 1 = ativo, 0 = inativo
    var checkbox = document.getElementById('inativar_pn');
    var labelText = document.getElementById('toggle-switch-text');

    var selectEl = document.getElementById('motivo_select');
    var textareaEl = document.getElementById('motivo');

    var selectGroup = selectEl.closest('.mb-3');
    var textareaGroup = textareaEl.closest('.mb-3');

    // Esconde tudo no início e remove required
    selectGroup.style.display = 'none';
    textareaGroup.style.display = 'none';
    selectEl.required = false;


    // Estado inicial do checkbox
    checkbox.checked = statusInativacao === 1;
    labelText.textContent = checkbox.checked ? 'Ativo' : 'Inativo';
 
    // Função para atualizar visibilidade + required
    function atualizarCampos() {
        // Se está igual ao estado inicial → esconde e remove required
        if ((checkbox.checked && statusInativacao === 1) ||
            (!checkbox.checked && statusInativacao === 0)) {
            selectGroup.style.display = 'none';
            textareaGroup.style.display = 'none';
            selectEl.required = false;
            textareaEl.required = false;
            return;
        }

        // Exibição original
        selectGroup.style.display = 'block';
        textareaGroup.style.display = 'block';

        // Regras de required
        if (checkbox.checked) {
            // Ativando → só textarea obrigatório
            selectEl.required = true;
            textareaEl.required = false;
        } else {
            // Inativando → só select obrigatório
            selectEl.required = true;
            textareaEl.required = true;
        }
    }

    // Evento de mudança
    checkbox.addEventListener('change', function () {
        labelText.textContent = this.checked ? 'Ativo' : 'Inativo';
        atualizarCampos();
    });
</script>



<style>
    .table.table-striped .descricao {
        width: 35% !important;
    }

    .table.table-striped .owner {
        max-width: 7% !important;
    }

    .toggle-switch {
        position: relative;
        display: inline-block;
        width: 100%;
        margin-top: 5px;
    }

    .toggle-checkbox {
        display: none;
    }

    .toggle-label {
        display: flex;
        align-items: center;
        cursor: pointer;
        margin-bottom: 0;
        font-weight: normal;
    }

    .toggle-inner {
        position: relative;
        display: inline-block;
        width: 50px;
        height: 24px;
        /* Cor vermelha quando desmarcado (inativo) */
        background-color: #dc3545; /* Vermelho padrão do Bootstrap */
        border-radius: 24px;
        transition: background-color 0.3s;
        margin-right: 10px;
        flex-shrink: 0;
    }

    .toggle-inner:before {
        content: '';
        position: absolute;
        top: 2px;
        left: 2px;
        width: 20px;
        height: 20px;
        background-color: white;
        border-radius: 50%;
        transition: transform 0.3s;
    }

    .toggle-checkbox:checked + .toggle-label .toggle-inner {
        /* Cor verde quando marcado (ativo) */
        background-color: #28a745; /* Verde padrão do Bootstrap */
    }

    .toggle-checkbox:checked + .toggle-label .toggle-inner:before {
        transform: translateX(26px);
    }

    .toggle-switch-text {
        font-size: 14px;
        /* Cor do texto vermelha por padrão */
        color: #dc3545;
        user-select: none;
    }

    .toggle-checkbox:checked + .toggle-label .toggle-switch-text {
        /* Cor do texto verde quando marcado */
        color: #28a745;
        font-weight: 500;
    }
</style>