<?php echo form_open_multipart('', array('class' => 'form-horizontal')) ?>

<?php
$funcoes_adicionais = explode('|', $entry->funcoes_adicionais);
?>
            <!-- Modal -->
            <div id="meuModal" class="modal fade" tabindex="-1" role="dialog" style="display:none; position:fixed; top:0; left:0; width:100%; height:100%; overflow:auto; background-color:rgba(0,0,0,0.5);">
                <div class="modal-dialog" role="document" style="position:relative; margin:50px auto; width:600px; max-width:90%;">
                    <div class="modal-content" style="background-color:#fff; border-radius:6px; box-shadow:0 5px 15px rgba(0,0,0,.5); border:1px solid #ccc;">
                        
                        <!-- Cabeçalho -->
                        <div class="modal-header" style="padding:15px; border-bottom:1px solid #e5e5e5;">
                            <button type="button" class="close" data-dismiss="modal" style="float:right; font-size:24px; font-weight:bold; border:none; background:none; cursor:pointer;">&times;</button>
                            <h4 class="modal-title" style="margin:0; font-size:18px; font-weight:bold;">Acordos e Premissas</h4>
                        </div>
                        
                        <!-- Corpo -->
                        <div class="modal-body" style="padding:20px; font-size:14px; line-height:1.5; color:#333;">
                            <h4 style="margin-top:0; font-size:15px; font-weight:bold;">Serviços contratados</h4>
                            <p style="margin:0 0 15px 0;"><?php echo !empty($entry->servicos_contratados) ? $entry->servicos_contratados : 'Nenhum serviço contratado' ?></p>
                            
                            <h4 style="margin-top:10px; font-size:15px; font-weight:bold;">Acordos e premissas</h4>
                            <ol style="padding-left:18px; margin:10px 0;"><?php echo !empty($entry->acordos_premissas) ? $entry->acordos_premissas : 'Nenhum acordo'; ?></ol>
                        </div>
                        
                        <!-- Rodapé -->
                        <div class="modal-footer" style="padding:10px 15px; text-align:right; border-top:1px solid #e5e5e5;">
                            <button type="button" class="btn btn-default" data-dismiss="modal" style="padding:6px 12px; border:1px solid #ccc; border-radius:4px; background-color:#f9f9f9; cursor:pointer;">Fechar</button>
                        </div>
                    </div>
                </div>
            </div>
<div class="row">
    <ul class="nav nav-tabs" role="tablist">
        <li class="active" role="presentation">
            <a href="#dados" aria-controls="home" role="tab" data-toggle="tab">Dados básicos</a>
        </li>
        <li role="presentation">
            <a href="#configuracoes" aria-controls="home" role="tab" data-toggle="tab">Configurações</a>
        </li>
        <li role="presentation">
            <a href="#configuracoes_financeiras" aria-controls="home" role="tab" data-toggle="tab">Configurações Financeiras</a>
        </li>
        <li role="presentation">
            <a href="#curva" aria-controls="home" role="tab" data-toggle="tab">Curva ABC</a>
        </li>
        <li role="presentation">
            <a href="#usuario-api" aria-controls="home" role="tab" data-toggle="tab">Usuários API</a>
        </li>
    </ul>

    <div class="tab-content">
        <div role="tabpanel" class="tab-pane active" id="dados">
            <div class="col-md-12">
                <legend>
                    <h2>Editar empresa</h2>
                </legend>

                <div class="form-group">
                    <label for="input-razao_social" class="col-sm-2 control-label">Razão Social</label>
                    <div class="col-sm-10">
                        <input type="text" class="form-control" name="razao_social" id="input-razao_social" value="<?php echo set_value('razao_social', $entry->razao_social) ?>" placeholder="Razão Social">
                    </div>
                </div>

                <div class="form-group">
                    <label for="input-nome_fantasia" class="col-sm-2 control-label">Nome Fantasia</label>
                    <div class="col-sm-10">
                        <input type="text" class="form-control" name="nome_fantasia" id="input-nome_fantasia" placeholder="Nome Fantasia" value="<?php echo set_value('nome_fantasia', $entry->nome_fantasia) ?>">
                    </div>
                </div>

                <div class="form-group">
                    <label for="input-cnpj" class="col-sm-2 control-label">CNPJ</label>
                    <div class="col-sm-10">
                        <input type="text" class="form-control" name="cnpj" id="input-cnpj" placeholder="CNPJ" value="<?php echo set_value('cnpj', $entry->cnpj) ?>">
                    </div>
                </div>

                <div class="form-group">
                    <label for="select-segmento" class="col-sm-2 control-label">Segmento</label>
                    <div class="col-sm-10">
                        <select class="form-control custom" name="id_segmento" id="select-segmento">
                            <option value="">[Selecione]</option>
                            <?php foreach ($segmentos as $segmento) { ?>
                                <option <?php echo set_select('id_segmento', $segmento->id_segmento, $segmento->id_segmento == $entry->id_segmento); ?> value="<?php echo $segmento->id_segmento ?>"><?php echo $segmento->descricao ?></option>
                            <?php } ?>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label for="select-segmento" class="col-sm-2 control-label">Empresa Ativa</label>
                    <div class="col-sm-10">
                        <select class="form-control" name="ativo" id="ativo">
                            <option <?php echo set_select('ativo', $entry->ativo, $entry->ativo == 1) ?> value="1">Ativa</option>
                            <option <?php echo set_select('ativo', $entry->ativo, $entry->ativo == 0) ?> value="0">Inativa</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label for="select-paises" class="col-sm-2 control-label">Multi Paises</label>
                    <div class="col-sm-10">
                        <select class="form-control selectpicker" multiple title="Paises onde atua" data-count-selected-text="{0} paises selecionados" data-selected-text-format="count" name="paises[]" id="paies" data-width="100%">
                            <option value="">[Selecione]</option>
                            <?php foreach ($paises as $pais) { ?>
                                <?php
                                $encontrado = false;
                                foreach ($paises_empresa as $pais_empresa) {
                                    if ($pais_empresa->id_pais === $pais->id_pais) {
                                        $encontrado = true;
                                        break;
                                    }
                                }
                                ?>
                                <?php if ($encontrado) { ?>
                                    <option <?php echo set_select('id_pais', $pais->id_pais); ?> selected="selected" value="<?php echo $pais->id_pais ?>"><?php echo $pais->nome ?></option>
                                <?php } else { ?>
                                    <option <?php echo set_select('id_pais', $pais->id_pais); ?> value="<?php echo $pais->id_pais ?>"><?php echo $pais->nome ?></option>
                                <?php } ?>

                            <?php } ?>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label for="select-perfil" class="col-sm-2 control-label">Perfil padrão para os usuários</label>
                    <div class="col-sm-10">
                        <select class="form-control custom" name="perfil_usuario_padrao_id" id="select-perfil">
                            <option value="">[Selecione]</option>
                            <?php foreach ($perfis as $perfil) { ?>
                                <option <?php echo set_select(
                                            'perfil_usuario_padrao_id',
                                            $perfil->id_perfil,
                                            $perfil->id_perfil == $entry->perfil_usuario_padrao_id
                                        ); ?>
                                    value="<?php echo $perfil->id_perfil ?>">
                                    <?php echo $perfil->descricao ?></option>
                            <?php } ?>
                        </select>
                    </div>
                </div>
                <?php if (!empty($gerentes_de_projeto)) { ?>
                    <div class="form-group">
                        <label for="select-id_gerente_de_projeto" class="col-sm-2 control-label">Gerente de Projetos</label>
                        <div class="col-sm-10">
                            <select class="form-control selectpicker" name="id_gerente_de_projetos" id="select-id_gerente_de_projeto" data-show-subtext="true" title="Selecione o gerente de projetos" data-live-search="true">
                                <?php foreach ($gerentes_de_projeto as $gp) { ?>
                                    <option value="<?php echo $gp->id_usuario; ?>" <?php echo $gp->id_usuario == $entry->id_gerente_de_projetos ? 'selected' : NULL; ?> data-subtext="<?php echo $gp->email; ?>"><?php echo $gp->nome; ?></option>
                                <?php } ?>
                            </select>
                        </div>
                    </div>
                <?php } ?>

                <div class="form-group">
                    <label for="select-id_gerente_de_projeto" class="col-sm-2 control-label">Squad Responsável</label>
                    <div class="col-sm-10">
                        <select class="form-control selectpicker" name="id_squad" id="id_squad" data-show-subtext="true" title="Selecione o squad responsável" data-live-search="true">
                            <?php foreach ($squads as $squad) { ?>
                                <option value="<?php echo $squad->id_squad; ?>" <?php echo $squad->id_squad == $entry->id_squad ? 'selected' : NULL; ?> data-subtext="<?php echo $squad->slug; ?>"><?php echo $squad->nome; ?></option>
                            <?php } ?>
                        </select>
                    </div>
                </div>

                <div class="clearfix"></div>
                <div class="form-group">
                    <label for="arquivo" class="col-sm-2 control-label">Upload da logo</label>
                    <div class="col-sm-10">

                        <div class="fileupload fileupload-new" data-provides="fileupload">
                            <span class="btn btn-primary btn-success btn-file"><span class="fileupload-new">Selecione um arquivo</span>
                                <span class="fileupload-exists">Modificar</span> <input type="file" name="arquivo" id="arquivo" /></span>
                            <span class="fileupload-preview"></span>
                            <a href="#" class="close fileupload-exists" data-dismiss="fileupload" style="float: none">×</a>
                        </div>

                        <?php

                        if ($logo = $this->empresa_model->get_logo_url($entry->logo_filename)) {
                        ?>
                            <img class="thumbnail" src="<?php echo $logo ?>" style="max-height: 50px;">
                        <?php
                        }
                        ?>

                    </div>
                </div>
                <?php if (has_role('gerenciar_empresas')) : ?>
                <div class="container" style="padding: 20px;">
                    <div class="row" style="border-bottom: 1px solid #ccc; padding-bottom: 10px; margin-bottom: 20px;">
                        <div class="col-xs-12">
                            <label style="font-weight: bold; font-size: 16px;">Serviços contratados</label>
                            <div style="display: inline-block; margin-left: 20px;">
                            <label class="checkbox-inline">
                                    <input type="checkbox" id="ncmCheckbox" name="servicos_contratados[]" <?php echo (strpos($entry->servicos_contratados, "NCM") !== false)   ? 'checked="checked"' : '' ?> value="NCM"> NCM
                                </label>
                                <label class="checkbox-inline">
                                    <input type="checkbox" id="atributosCheckbox" name="servicos_contratados[]" <?php echo (strpos($entry->servicos_contratados, "Atributos") !== false)  ? 'checked="checked"' : '' ?> value="Atributos"> Atributos
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="acordos" class="col-sm-2 control-label" style="font-weight: bold; font-size: 16px;">Acordos e Premissas</label>
                        <div class="col-sm-10">
                            <textarea name="acordos" id="acordos" class="form-control" rows="6" style="height: 224px;width: 100%;border: 1px solid #ccc;padding: 10px;"><?php echo $entry->acordos_premissas; ?></textarea>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
        <div role="tabpanel" class="tab-pane" id="configuracoes" style="padding-top: 10px;">
            <div class="col-md-6" style="border-right: 1px solid #e2e2e2;">
                <legend>
                    <h2>Monitor NFE</h2>
                </legend>

                <div class="well">

                    <div class="checkbox">
                        <label>
                            <input type="checkbox" name="retira_icms_preco" <?php echo 1 == $entry->retira_icms_preco ? 'checked="checked"' : '' ?> value="1"> Retira ICMS do Preço
                        </label>
                    </div>

                    <div class="checkbox">
                        <label>
                            <input type="checkbox" name="retira_pis_preco" <?php echo 1 == $entry->retira_pis_preco ? 'checked="checked"' : '' ?> value="1"> Retira PIS do Preço
                        </label>
                    </div>

                    <div class="checkbox">
                        <label>
                            <input type="checkbox" name="retira_cofins_preco" <?php echo 1 == $entry->retira_pis_preco ? 'checked="checked"' : '' ?> value="1"> Retira COFINS do Preço
                        </label>
                    </div>

                    <div class="clearfix" style="margin-top: 10px">
                        <label for="tipo_tolerancia">Tipo da Tolerância</label>
                        <select class="form-control" name="tipo_tolerancia" id="tipo_tolerancia">
                            <option value="">Nenhum</option>
                            <option <?php echo set_select('tipo_tolerancia', 'valor', 'valor' == $entry->tipo_tolerancia) ?> value="valor">Valor</option>
                            <option <?php echo set_select('tipo_tolerancia', 'percentual', 'percentual' == $entry->tipo_tolerancia) ?> value="percentual">Percentual</option>
                        </select>
                    </div>

                    <div class="clearfix" style="margin-top: 10px">
                        <label for="tolerancia">Tolerância</label>
                        <input type="text" placeholder="Valor Decimal (Ex.: 0.10)" class="form-control" name="tolerancia" id="tolerancia" value="<?php echo set_value('tolerancia', $entry->tolerancia) ?>">
                    </div>

                    <div class="clearfix" style="margin-top: 10px">
                        <label for="descricao_max_caracteres">Tamanho da descrição proposta resumida</label>
                        <input type="text" placeholder="Nº de caracteres (Ex.: 255)" class="form-control" name="descricao_max_caracteres" id="descricao_max_caracteres" value="<?php echo set_value('descricao_max_caracteres', $entry->descricao_max_caracteres) ?>">
                    </div>

                </div>

                <legend>
                    <h2>Estabelecimento</h2>
                </legend>

                <div class="well">
                    <p>Informe o estabelecimento padrão:</p>
                    <input type="text" class="form-control" name="estabelecimento_default" maxlength="50" placeholder="Utilize letras e números com limite de 50 caracteres." value="<?php echo set_value('estabelecimento_default', $entry->estabelecimento_default) ?>" />
                </div>

                <legend>
                    <h2>Usuário Fiscal</h2>
                </legend>
                <div class="well">
                    <p>Informe o usuário padrão:</p>
                    <select name="id_resp_fiscal" data-live-search="true" id="" data-size="5" class="selectpicker form-control">
                        <?php foreach ($usuarios as $usuario) : ?>
                            <option <?php echo $usuario->id_usuario == $entry->id_resp_fiscal ? 'selected' : '' ?> value="<?php echo $usuario->id_usuario; ?>"><?php echo $usuario->nome; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <?php $data = [];
                $data['users_status_change'] = $users_status_change;
                $data['id_empresa'] = $entry->id_empresa;

                ?>
                <?php $this->load->view('cadastros/empresa/concatenar-campos'); ?>
                <input type="hidden" name="id_empresa" value="<?php echo $entry->id_empresa ?>" />
                <div class="row">
                    <div class="col-md-12">
                        <legend>
                            <h2>Configurar email</h2>
                        </legend>
                        <div class="well">
                            <a href="#" data-toggle="modal" data-target="#config-mail-tag-modal" class="btn btn-default"> Configurar Email <i class="glyphicon glyphicon-cloud-download"></i></a>
                        </div>

                    </div>
                </div>
                <?php $this->load->view('cadastros/empresa/modal-config-mail', $data); ?>
            </div>
            <div class="col-md-6">
                <div class="row">
                    <div class="col-md-12">
                        <legend>
                            <h2>Campos adicionais</h2>
                        </legend>

                        <?php
                        $campos_adicionais = explode('|', $entry->campos_adicionais);
                        ?>

                        <div class="well">
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="campos_adicionais[]" value="aplicacao" <?php echo in_array("aplicacao", $campos_adicionais) ? 'checked="checked"' : '' ?> value="1"> Aplicação
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="campos_adicionais[]" value="caracteristica" <?php echo in_array("caracteristica", $campos_adicionais) ? 'checked="checked"' : '' ?> value="1"> Característica
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="campos_adicionais[]" value="dispositivo_legal" <?php echo in_array("dispositivo_legal", $campos_adicionais) ? 'checked="checked"' : '' ?> value="1"> Dispositivos legais
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="campos_adicionais[]" value="descricao_proposta_completa" <?php echo in_array("descricao_proposta_completa", $campos_adicionais) ? 'checked="checked"' : '' ?> value="1"> Descrição proposta completa </label>
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="campos_adicionais[]" value="descricao_mercado_local" <?php echo in_array("descricao_proposta_completa", $campos_adicionais) ? 'checked="checked"' : '' ?> value="1"> Descrição proposta resumida
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="campos_adicionais[]" value="descricao" <?php echo in_array("descricao", $campos_adicionais) ? 'checked="checked"' : '' ?> value="1"> Descrição
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="campos_adicionais[]" value="evento" <?php echo in_array("descricao", $campos_adicionais) ? 'checked="checked"' : '' ?> value="1"> Evento
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="campos_adicionais[]" value="funcao" <?php echo in_array("funcao", $campos_adicionais) ? 'checked="checked"' : '' ?> value="1"> Função
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="campos_adicionais[]" value="inf_adicionais" <?php echo in_array("inf_adicionais", $campos_adicionais) ? 'checked="checked"' : '' ?> value="1"> Informações Adicionais
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="campos_adicionais[]" value="maquina" <?php echo in_array("maquina", $campos_adicionais) ? 'checked="checked"' : '' ?> value="1"> Máquina
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="campos_adicionais[]" value="marca" <?php echo in_array("marca", $campos_adicionais) ? 'checked="checked"' : '' ?> value="1"> Marca
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="campos_adicionais[]" value="material_constitutivo" <?php echo in_array("material_constitutivo", $campos_adicionais) ? 'checked="checked"' : '' ?> value="1"> Material Constitutivo
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="campos_adicionais[]" value="memoria_classificacao" <?php echo in_array("memoria_classificacao", $campos_adicionais) ? 'checked="checked"' : '' ?> value="1"> Memória de Classificação
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="campos_adicionais[]" value="observacoes" <?php echo in_array("observacoes", $campos_adicionais) ? 'checked="checked"' : '' ?> value="1"> Observações
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="campos_adicionais[]" value="origem" <?php echo in_array("origem", $campos_adicionais) ? 'checked="checked"' : '' ?> value="1"> Origem (País)
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="campos_adicionais[]" value="owner" <?php echo in_array("owner", $campos_adicionais) ? 'checked="checked"' : '' ?> value="1"> Owner
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="campos_adicionais[]" value="triagem_owner" <?php echo in_array("triagem_owner", $campos_adicionais) ? 'checked="checked"' : '' ?> value="1"> Triagem de Owner
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="campos_adicionais[]" value="peso" <?php echo in_array("peso", $campos_adicionais) ? 'checked="checked"' : '' ?> value="1"> Peso
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="campos_adicionais[]" value="prioridade" <?php echo in_array("prioridade", $campos_adicionais) ? 'checked="checked"' : '' ?> value="1"> Prioridade
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="campos_adicionais[]" value="usuario_seguidor" <?php echo in_array("usuario_seguidor", $campos_adicionais) ? 'checked="checked"' : '' ?> value="1"> Usuário seguidor
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="campos_adicionais[]" value="revisar_info_erp" <?php echo in_array("revisar_info_erp", $campos_adicionais) ? 'checked="checked"' : '' ?> value="1"> Revisar Informações ERP
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="campos_adicionais[]" value="unidade_negocio" <?php echo in_array("unidade_negocio", $campos_adicionais) ? 'checked="checked"' : '' ?> value="1"> Unidade de Negócio
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="campos_adicionais[]" value="usuario_seguidor" <?php echo in_array("usuario_seguidor", $campos_adicionais) ? 'checked="checked"' : '' ?> value="1"> Usuário seguidor
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="campos_adicionais[]" value="subsidio" <?php echo in_array("subsidio", $campos_adicionais) ? 'checked="checked"' : '' ?> value="1"> Subsídio
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="campos_adicionais[]" value="descricao_global" <?php echo in_array("descricao_global", $campos_adicionais) ? 'checked="checked"' : '' ?> value="1"> Utiliza Descrição Global
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="campos_adicionais[]" value="pn_primario_secundario" <?php echo in_array("pn_primario_secundario", $campos_adicionais) ? 'checked="checked"' : '' ?> value="1"> PN Primário/Secundário
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="campos_adicionais[]" value="controle_drawback" <?php echo in_array("controle_drawback", $campos_adicionais) ? 'checked="checked"' : '' ?> value="1"> Controle de Drawback
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="campos_adicionais[]" value="item_novo_ou_modificado" <?php echo in_array("item_novo_ou_modificado", $campos_adicionais) ? 'checked="checked"' : '' ?> value="1"> Item novo ou modificado (S/N)
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <legend>
                            <h2>Funções adicionais</h2>
                        </legend>

                        <div class="well">
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="funcoes_adicionais[]" value="vinculacao_atr" <?php echo in_array("vinculacao_atr", $funcoes_adicionais) ? 'checked="checked"' : '' ?> value="1"> Atributos
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="funcoes_adicionais[]" value="vinculacao_ii" <?php echo in_array("vinculacao_ii", $funcoes_adicionais) ? 'checked="checked"' : '' ?> value="1"> Atribuição de EX II
                                </label>
                            </div>

                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="funcoes_adicionais[]" value="vinculacao_ipi" <?php echo in_array("vinculacao_ipi", $funcoes_adicionais) ? 'checked="checked"' : '' ?> value="1"> Atribuição de EX IPI
                                </label>
                            </div>

                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="funcoes_adicionais[]" value="vinculacao_nve" <?php echo in_array("vinculacao_nve", $funcoes_adicionais) ? 'checked="checked"' : '' ?> value="1"> Atribuição de NVE
                                </label>
                            </div>

                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="funcoes_adicionais[]" value="vinculacao_cest" <?php echo in_array("vinculacao_cest", $funcoes_adicionais) ? 'checked="checked"' : '' ?> value="1"> Atribuição de CEST
                                </label>
                            </div>

                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="funcoes_adicionais[]" value="ncm_dados_tecnicos" <?php echo in_array("ncm_dados_tecnicos", $funcoes_adicionais) ? 'checked="checked"' : '' ?> value="1"> Exibir NCM do item em Dados Técnicos
                                </label>
                            </div>

                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="funcoes_adicionais[]" value="homologacao_fiscal" <?php echo in_array("homologacao_fiscal", $funcoes_adicionais) ? 'checked="checked"' : '' ?> value="1"> Homologação Fiscal
                                </label>
                            </div>

                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="funcoes_adicionais[]" value="homologacao_engenharia" <?php echo in_array("homologacao_engenharia", $funcoes_adicionais) ? 'checked="checked"' : '' ?> value="1"> Homologação Engenharia
                                </label>
                            </div>

                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" <?php echo isset($entry->recebe_email_pendencias) && $entry->recebe_email_pendencias ? 'checked' : ''; ?> name="recebe_email_pendencias" value="1"> Recebe Email Pendências
                                </label>
                            </div>

                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="funcoes_adicionais[]" value="status_exportacao" <?php echo in_array("status_exportacao", $funcoes_adicionais) ? 'checked="checked"' : '' ?>> Status Exportação Item (Homologação / Booking Eletrônico)
                                </label>
                            </div>

                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="funcoes_adicionais[]" id="planilha-upload-input" value="planilha_upload" <?php echo in_array("planilha_upload", $funcoes_adicionais) ? 'checked="checked"' : '' ?> value="1"> Exportar Planilha de Upload
                                </label>
                            </div>

                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="funcoes_adicionais[]" id="exportar-input" value="exportar" <?php echo in_array("exportar", $funcoes_adicionais) ? 'checked="checked"' : '' ?> value="1"> Exportar Itens (Homologação / Booking Eletrônico)
                                </label>
                            </div>

                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="funcoes_adicionais[]" id="exportar-lista-impacto" value="exportar_lista_impacto" <?php echo in_array("exportar_lista_impacto", $funcoes_adicionais) ? 'checked="checked"' : '' ?> value="1"> Exportar Lista de Impacto
                                </label>
                            </div>

                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="funcoes_adicionais[]" id="classificacao-energetica-input" value="classificacao_energetica" <?php echo in_array("classificacao_energetica", $funcoes_adicionais) ? 'checked="checked"' : '' ?> value="1"> Classificação Energética
                                </label>
                            </div>

                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="funcoes_adicionais[]" id="exibir-id-gpt-input" value="exibir_id_gpt" <?php echo in_array("exibir_id_gpt", $funcoes_adicionais) ? 'checked="checked"' : '' ?> value="1"> Exibir ID do Grupo Tarifário na Exportação (Booking, Homologação e Planilha de Upload)
                                </label>
                            </div>

                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="funcoes_adicionais[]" id="suframa-input" value="vinculacao_suframa" <?php echo in_array("vinculacao_suframa", $funcoes_adicionais) ? 'checked="checked"' : '' ?> value="1"> SUFRAMA
                                </label>
                            </div>

                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="funcoes_adicionais[]" id="diana-input" value="has_diana" <?php echo in_array("has_diana", $funcoes_adicionais) ? 'checked="checked"' : '' ?> value="1"> Utiliza Diana
                                </label>
                            </div>

                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="funcoes_adicionais[]" id="ncm_fornecedor-input" value="vinculacao_ncm_fornecedor" <?php echo in_array("vinculacao_ncm_fornecedor", $funcoes_adicionais) ? 'checked="checked"' : '' ?> value="1"> NCM Fornecedor
                                </label>
                            </div>

                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="funcoes_adicionais[]" id="importar_itens_via_mestre-input" value="importar_itens" <?php echo in_array("importar_itens", $funcoes_adicionais) ? 'checked="checked"' : '' ?> value="1"> Importar Itens via Mestre
                                </label>
                            </div>

                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="funcoes_adicionais[]" id="integracao_ecomex-input" value="integracao_ecomex" <?php echo in_array("integracao_ecomex", $funcoes_adicionais) ? 'checked="checked"' : '' ?> value="1"> Integração eComex
                                </label>
                            </div>

                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="funcoes_adicionais[]" id="botao-preencher-desc-resumida-input" value="preencher_desc_resumida" <?php echo in_array("preencher_desc_resumida", $funcoes_adicionais) ? 'checked="checked"' : '' ?> value="1"> Exibir Botão Preencher Descrição Proposta Resumida
                                </label>
                            </div>

                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="funcoes_adicionais[]" id="formatar_texto" value="formatar_texto" <?php echo in_array("formatar_texto", $funcoes_adicionais) ? 'checked="checked"' : '' ?> value="1"> Formatar Textos
                                </label>
                            </div>

                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="funcoes_adicionais[]" id="lessin" value="lessin" <?php echo in_array("lessin", $funcoes_adicionais) ? 'checked="checked"' : '' ?> value="1"> Lessin
                                </label>
                            </div>

                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="funcoes_adicionais[]" id="validar_pn" value="validar_pn" <?php echo in_array("validar_pn", $funcoes_adicionais) ? 'checked="checked"' : '' ?> value="1"> Validar PN de estabelecimentos diferentes para NCM divergentes
                                </label>
                            </div>

                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="funcoes_adicionais[]" id="exportar_diana" value="exportar_diana" <?php echo in_array("exportar_diana", $funcoes_adicionais) ? 'checked="checked"' : '' ?> value="1">
                                    Gerar planilha atributos
                                </label>
                            </div>

                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="funcoes_adicionais[]" id="atribuicao_automatica" value="atribuicao_automatica" <?php echo in_array("atribuicao_automatica", $funcoes_adicionais) ? 'checked="checked"' : '' ?> value="1">
                                    Atribuição automática
                                </label>
                            </div>

                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="funcoes_adicionais[]" id="destinatatio_tec" value="destinatatio_tec" <?php echo in_array("destinatatio_tec", $funcoes_adicionais) ? 'checked="checked"' : '' ?> value="1">
                                    Destinatário TEC
                                </label>
                            </div>

                            <div class="row mt-4 <?php echo in_array("destinatatio_tec", $funcoes_adicionais) ? '' : 'hide' ?>" id="div_destinatarios_tec">
                                <div class="col-sm-12 mt-4">
                                    <div class="well">
                                        <p>Destinatários TEC:</p>
                                        <input name="destinatarios_tec" id="destinatarios_tec" type="text" class="form-control" placeholder="Informe os email's separadas por ; (ponto e virgula)" value="<?php echo set_value('destinatarios_tec', $entry->destinatarios_tec)  ?>" />
                                    </div>
                                </div>
                            </div>

                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="funcoes_adicionais[]" id="chk_destinatarios_revisao_pucomex" value="chk_destinatarios_revisao_pucomex" <?php echo in_array("chk_destinatarios_revisao_pucomex", $funcoes_adicionais) ? 'checked="checked"' : '' ?> value="1">
                                    Destinatários Revisão Pucomex
                                </label>
                            </div>

                            <div class="row mt-4 <?php echo in_array("chk_destinatarios_revisao_pucomex", $funcoes_adicionais) ? '' : 'hide' ?>" id="div_destinatarios_revisao_pucomex">
                                <div class="col-sm-12 mt-4">
                                    <div class="well">
                                        <p>Destinatários Revisão Pucomex:</p>
                                        <input name="destinatarios_revisao_pucomex" id="destinatarios_revisao_pucomex" type="text" class="form-control" placeholder="Informe os email's separadas por ; (ponto e virgula)" value="<?php echo set_value('destinatarios_revisao_pucomex', $entry->destinatarios_revisao_pucomex)  ?>" />
                                    </div>
                                </div>
                            </div>

                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="funcoes_adicionais[]" id="diana_atributos" value="diana_atributos" <?php echo in_array("diana_atributos", $funcoes_adicionais) ? 'checked="checked"' : '' ?> value="1">
                                    Utiliza Diana Atributos
                                </label>
                            </div>

                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="funcoes_adicionais[]" id="item_importado_default" value="item_importado_default" <?php echo in_array("item_importado_default", $funcoes_adicionais) ? 'checked="checked"' : '' ?> value="1"> Definir item importado como padrão
                                </label>
                            </div>

                            <div class="checkbox">
                                <label for="homologacao_atributos_incompletos">
                                    <input
                                        type="checkbox"
                                        name="funcoes_adicionais[]"
                                        id="homologacao_atributos_incompletos"
                                        value="homologacao_atributos_incompletos"
                                        <?php echo in_array("homologacao_atributos_incompletos", $funcoes_adicionais) ? 'checked="checked"' : '' ?>>
                                    Permitir homologação de atributos incompletos
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="funcoes_adicionais[]" id="visualizar_bi" value="visualizar_bi" <?php echo in_array("visualizar_bi", $funcoes_adicionais) ? 'checked="checked"' : '' ?> value="1">
                                        Visualizar Bi 
                                </label>
                            </div>

                            <div class="checkbox">
                                <label>
                                    <input
                                        type="checkbox"
                                        name="funcoes_adicionais[]"
                                        id="status_triagem_diana"
                                        value="status_triagem_diana" <?php echo in_array("status_triagem_diana", $funcoes_adicionais) ? 'checked="checked"' : '' ?>
                                        value="1"
                                    >
                                    Status Triagem Diana
                                </label>
                            </div>

                        </div>

                        <button type="button" id="suframa-button" class="btn btn-primary" style="display: none">Gerenciar Suframa</button>

                        <div class="modal fade" id="suframa-modal" tabindex="-1" role="dialog" aria-hidden="true">
                            <div class="modal-dialog modal-xl">
                                <?php $this->load->view('cadastros/empresa/modal-suframa'); ?>
                            </div>
                        </div>
                        <?php $this->load->view('cadastros/empresa/modal-diana'); ?>
                    </div>
                </div>


                <div class="row">
                    <div class="col-sm-12">
                        <legend>
                            <h2>Parâmetro separador Part Numbers</h2>
                        </legend>
                        <div class="well">
                            <div class="">
                                <label for="">Informe parametro separador</label>
                                <input type="text" class="form-control" value="<?php echo set_value('separador_pesquisa', $entry->separador_pesquisa) ?>" name="separador_pesquisa" id="separador_pesquisa" placeholder="Por exemplo: ';', ':', ',', ' '" />
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <legend>
                            <h2>CEST</h2>
                        </legend>
                        <div class="well">
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="venda_porta_a_porta" value="1" <?php echo $entry->porta_a_porta == 1 ? 'checked="checked"' : '' ?>> Utiliza sistema de venda Porta a Porta
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div role="tabpanel" class="tab-pane" id="configuracoes_financeiras" style="padding-top: 10px;">

            <div class="col-md-12">
            <?php if (customer_has_role('exportar_relatorio_franquia', sess_user_id())) : ?>
                <button style="margin-top: 10px;" type="button" id="btn_rel_franquia" class="btn btn-default btn-download-xls pull-right" data-toggle="modal" data-target="#exportOptionsModal">
                    <i class="glyphicon glyphicon-export"></i> Exportar Relatório de franquia
                </button>
            <?php endif; ?>
                <legend>
                    <h2>Controle de franquia</h2>
                </legend>
                 

                <div class="well">
                    <div class="checkbox">
                        <label>
                            <input type="checkbox" id="habilitar_franquia_check" name="habilitar_uso_franquia" value="1" <?php echo $entry->habilitar_uso_franquia == 1 ? 'checked="checked"' : ''
                                                                                                                            ?>> Habilitar controle de franquia
                        </label>
                    </div>

                    <div id="controle_franquia_campos" style="margin-top: 15px; display: none;">

                        <div style="margin-left: 20px;">
                            <div class="form-group">
                                <label for="quantidade_franquia_mensal" style="font-size: 0.9em; font-weight: normal;">Quantidade da franquia controlada</label>
                                <input type="number"
                                    class="form-control"
                                    id="quantidade_franquia_mensal"
                                    name="quantidade_franquia_mensal"
                                    placeholder="Informe a quantidade"
                                    value="<?php echo set_value('quantidade_franquia_mensal', $entry->quantidade_franquia_mensal) ?>">
                            </div>
                        </div>
                        <hr>

                        <div style="padding-left: 20px; margin-top: 20px;">
                            <legend style="margin-bottom: 10px; font-size: 1.2em;">Valores de cobranças adicionais</legend>
                            <div class="checkbox">
                                <label><input type="checkbox" id="possui_cobranca_check" name="habilitar_cobrancas_adicionais" value="1" <?php echo $entry->habilitar_cobrancas_adicionais == 1 ? 'checked="checked"' : ''
                                                                                                                                            ?>> Possui cobrança adicional</label>
                            </div>
                            <div id="tabela_cobrancas_container" style="margin-top: 15px; display: none;">
                                <div style="margin-left: 20px;">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th style="width: 33%; font-size: 0.9em; font-weight: bold; vertical-align: bottom;">Prioridade</th>
                                                <th style="width: 33%; font-size: 0.9em; font-weight: bold; vertical-align: bottom;">Valor por item padrão</th>
                                                <th style="width: 34%; font-size: 0.9em; font-weight: bold; vertical-align: bottom;">Valor por item químico</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($prioridades as $prioridade): ?>
                                                <tr>
                                                    <td style="font-size: 0.9em; vertical-align: middle;">
                                                        <?= htmlspecialchars($prioridade->nome ?? '', ENT_QUOTES, 'UTF-8') ?>
                                                    </td>
                                                    <td>
                                                        <input type="text"
                                                            class="form-control cobranca-input"
                                                            name="valor_padrao_normal[<?php echo $prioridade->id_prioridade; ?>]"
                                                            value="<?= htmlspecialchars($prioridade->valor_padrao ?? '', ENT_QUOTES, 'UTF-8') ?>">
                                                    </td>
                                                    <td>
                                                        <input type="text"
                                                            class="form-control cobranca-input"
                                                            name="valor_quimico_normal[<?php echo $prioridade->id_prioridade; ?>]"
                                                            value="<?= htmlspecialchars($prioridade->valor_quimico ?? '', ENT_QUOTES, 'UTF-8') ?>">
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                            <!-- <tr>
                                                <td style="font-size: 0.9em; vertical-align: middle;">Normal</td>
                                                <td><input type="text" class="form-control cobranca-input" name="valor_padrao_normal" value="<?php // Lógica PHP 
                                                                                                                                                ?>"></td>
                                                <td><input type="text" class="form-control cobranca-input" name="valor_quimico_normal" value="<?php // Lógica PHP 
                                                                                                                                                ?>"></td>
                                            </tr>
                                            <tr>
                                                <td style="font-size: 0.9em; vertical-align: middle;">Alta</td>
                                                <td><input type="text" class="form-control cobranca-input" name="valor_padrao_alta" value="<?php // Lógica PHP 
                                                                                                                                            ?>"></td>
                                                <td><input type="text" class="form-control cobranca-input" name="valor_quimico_alta" value="<?php // Lógica PHP 
                                                                                                                                            ?>"></td>
                                            </tr>
                                            <tr>
                                                <td style="font-size: 0.9em; vertical-align: middle;">Urgente</td>
                                                <td><input type="text" class="form-control cobranca-input" name="valor_padrao_urgente" value="<?php // Lógica PHP 
                                                                                                                                                ?>"></td>
                                                <td><input type="text" class="form-control cobranca-input" name="valor_quimico_urgente" value="<?php // Lógica PHP 
                                                                                                                                                ?>"></td>
                                            </tr>
                                            <tr>
                                                <td style="font-size: 0.9em; vertical-align: middle;">Zero</td>
                                                <td><input type="text" class="form-control cobranca-input" name="valor_padrao_zero" value="<?php // Lógica PHP 
                                                                                                                                            ?>"></td>
                                                <td><input type="text" class="form-control cobranca-input" name="valor_quimico_zero" value="<?php // Lógica PHP 
                                                                                                                                            ?>"></td>
                                            </tr> -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <hr>

                        <div style="padding-left: 20px; margin-top: 20px;">
                            <legend style="margin-bottom: 10px; font-size: 1.2em;">Notificações de excedente</legend>
                            <div class="checkbox">
                                <label><input type="checkbox" id="habilitar_notificacoes_check" name="habilitar_notificacoes" value="1" <?php echo $entry->habilitar_notificacoes == 1 ? 'checked="checked"' : ''
                                                                                                                                        ?>> Habilitar notificações</label>
                            </div>
                            <div id="notificacoes_campos_container" style="display: none; margin-top: 15px;">
                                <div style="margin-left: 20px;">
                                    <div class="form-group">
                                        <label for="emails_notificados_input" style="font-size: 0.9em; font-weight: normal;">Email(s) notificado(s) <i class="glyphicon glyphicon-info-sign" title="Informe os emails separados por ponto e vírgula (;)"></i></label>
                                        <input type="text" class="form-control" id="emails_notificados_input" name="destinatarios_excedente" placeholder="Informe os email's separados por ; (ponto e virgula)" value="<?php echo !empty($entry->destinatarios_excedente) ? $entry->destinatarios_excedente : null ?>">
                                    </div>
                                    <div class="form-group" style="margin-top: 20px;">
                                        <table class="table table-bordered">
                                            <thead>
                                                <tr>
                                                    <th style="width: 50%; font-size: 0.9em; font-weight: bold; vertical-align: bottom;">Notificação</th>
                                                    <th style="width: 50%; font-size: 0.9em; font-weight: bold; vertical-align: bottom;">Percentual de envio <i class="glyphicon glyphicon-info-sign" title="Percentual da franquia para disparar a notificação"></i></th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>

                                                    <td style="font-size: 0.9em; vertical-align: middle;">Primeira notificação</td>
                                                    <td>
                                                        <input type="text" class="form-control notificacao-input"
                                                            id="percentual_input"
                                                            name="percentual_primeira_notificacao"
                                                            value="<?php echo isset($entry->percentual_primeira_notificacao) ? $entry->percentual_primeira_notificacao . '%' : '' ?>">

                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td style="font-size: 0.9em; vertical-align: middle;">Segunda Notificação </td>
                                                    <td>
                                                        <input type="text"
                                                            class="form-control notificacao-input"
                                                            id="percentual_segunda_input"
                                                            name="percentual_segunda_notificacao"
                                                            value="<?php echo isset($entry->percentual_segunda_notificacao) ? $entry->percentual_segunda_notificacao . '%' : ''; ?>">
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <hr>

                        <div style="padding-left: 20px; margin-top: 20px;">
                            <legend style="margin-bottom: 10px; font-size: 1.2em;">Bloqueio de cadastro de itens</legend>
                            <div class="checkbox">
                                <label><input type="checkbox" id="habilitar_bloqueio_check" name="habilitar_bloqueio" value="1" <?php echo $entry->habilitar_bloqueio == 1 ? 'checked="checked"' : ''
                                                                                                                                ?>> Habilitar bloqueio <i class="glyphicon glyphicon-info-sign" title="Habilita o bloqueio de cadastro ao atingir a franquia"></i></label>
                            </div>
                            <div id="bloqueio_campos_container" style="display: none; margin-top: 15px;">
                                <div style="margin-left: 20px;">
                                    <div class="form-group">
                                        <div style="display: flex; flex-wrap: wrap; gap: 20px; align-items: baseline;"> <label style="font-size: 0.9em; font-weight: normal; margin-bottom: 0;">
                                                <input type="radio" name="tipo_bloqueio" value="0" class="bloqueio-input" style="margin-right: 4px; vertical-align: middle;" <?php echo $entry->tipo_bloqueio ==  '0' ? 'checked' : ''
                                                                                                                                                                                ?>>Bloqueio após exceder a franquia
                                            </label>
                                            <label style="font-size: 0.9em; font-weight: normal; margin-bottom: 0;">
                                                <input type="radio" name="tipo_bloqueio" value="1" class="bloqueio-input" style="margin-right: 4px; vertical-align: middle;" <?php echo $entry->tipo_bloqueio ==  '1' ? 'checked' : ''
                                                                                                                                                                                ?>>Bloqueio imediato ao atingir a franquia
                                            </label>
                                        </div>
                                    </div>
                                    <div class="form-group" style="margin-top: 15px;">
                                        <label for="percentual_excedente_input" style="font-size: 0.9em; font-weight: normal;">
                                            Percentual de excedente
                                            <i class="glyphicon glyphicon-info-sign" title="Percentual sobre a franquia antes de bloquear (se aplicável)"></i>
                                        </label>
                                        <input type="text"
                                            class="form-control bloqueio-input"
                                            id="percentual_excedente_input"
                                            name="percentual_excedente"
                                            value="<?php echo isset($entry->percentual_excedente) ? $entry->percentual_excedente . '%' : ''; ?>">
                                    </div>

                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
        <script>
            document.addEventListener('DOMContentLoaded', function() {

                // --- Referências aos Elementos ---
                const habilitarFranquiaCheck = document.getElementById('habilitar_franquia_check');
                const controleFranquiaCampos = document.getElementById('controle_franquia_campos'); // Container principal
                const qtdInput = document.getElementById('quantidade_franquia_mensal'); // Input direto da franquia

                const possuiCobrancaCheck = document.getElementById('possui_cobranca_check');
                const tabelaCobrancasContainer = document.getElementById('tabela_cobrancas_container');

                const habilitarNotificacoesCheck = document.getElementById('habilitar_notificacoes_check');
                const notificacoesCamposContainer = document.getElementById('notificacoes_campos_container');

                const habilitarBloqueioCheck = document.getElementById('habilitar_bloqueio_check');
                const bloqueioCamposContainer = document.getElementById('bloqueio_campos_container');

                // --- Funções de Toggle ---

                function toggleCobrancaContainerVisibility() {
                    if (possuiCobrancaCheck && tabelaCobrancasContainer && habilitarFranquiaCheck && habilitarFranquiaCheck.checked) {
                        const isChecked = possuiCobrancaCheck.checked;
                        tabelaCobrancasContainer.style.display = isChecked ? 'block' : 'none';
                        const inputs = tabelaCobrancasContainer.querySelectorAll('.cobranca-input');
                        inputs.forEach(input => input.disabled = !isChecked);
                    } else if (tabelaCobrancasContainer) {
                        tabelaCobrancasContainer.style.display = 'none';
                    }
                }

                function toggleNotificacoesVisibility() {
                    if (habilitarNotificacoesCheck && notificacoesCamposContainer && habilitarFranquiaCheck && habilitarFranquiaCheck.checked) {
                        const isChecked = habilitarNotificacoesCheck.checked;
                        notificacoesCamposContainer.style.display = isChecked ? 'block' : 'none';
                        const inputs = notificacoesCamposContainer.querySelectorAll('input.form-control');
                        inputs.forEach(input => {
                            if (!input.hasAttribute('readonly')) {
                                input.disabled = !isChecked;
                            }
                        });
                    } else if (notificacoesCamposContainer) {
                        notificacoesCamposContainer.style.display = 'none';
                    }
                }

                function toggleBloqueioVisibility() {
                    if (habilitarBloqueioCheck && bloqueioCamposContainer && habilitarFranquiaCheck && habilitarFranquiaCheck.checked) {
                        const isChecked = habilitarBloqueioCheck.checked;
                        bloqueioCamposContainer.style.display = isChecked ? 'block' : 'none';
                        const inputs = bloqueioCamposContainer.querySelectorAll('.bloqueio-input');
                        inputs.forEach(input => input.disabled = !isChecked);
                    } else if (bloqueioCamposContainer) {
                        bloqueioCamposContainer.style.display = 'none';
                    }
                }

                function toggleFranquiaCamposVisibility() {
                    if (habilitarFranquiaCheck && controleFranquiaCampos) {
                        const isChecked = habilitarFranquiaCheck.checked;
                        controleFranquiaCampos.style.display = isChecked ? 'block' : 'none';
                        if (qtdInput) qtdInput.disabled = !isChecked;
                        toggleCobrancaContainerVisibility();
                        toggleNotificacoesVisibility();
                        toggleBloqueioVisibility();
                    }
                }

                // --- Adiciona Listeners ---
                if (habilitarFranquiaCheck) {
                    habilitarFranquiaCheck.addEventListener('change', toggleFranquiaCamposVisibility);
                }
                if (possuiCobrancaCheck) {
                    possuiCobrancaCheck.addEventListener('change', toggleCobrancaContainerVisibility);
                }
                if (habilitarNotificacoesCheck) {
                    habilitarNotificacoesCheck.addEventListener('change', toggleNotificacoesVisibility);
                }
                if (habilitarBloqueioCheck) {
                    habilitarBloqueioCheck.addEventListener('change', toggleBloqueioVisibility);
                }

                toggleFranquiaCamposVisibility();


                 // =========================================================================
    // == LÓGICA DA MODAL DE EXPORTAÇÃO ==
    // =========================================================================
    const selectAno = $('#selectAno');
    const selectMes = $('#selectMes');

    const anoAtual = new Date().getFullYear();
    for (let i = 0; i < 10; i++) {
        const ano = anoAtual - i;
        selectAno.append(`<option value="${ano}">${ano}</option>`);
    }

    const meses = [
        { valor: 1, nome: 'Janeiro' }, { valor: 2, nome: 'Fevereiro' },
        { valor: 3, nome: 'Março' }, { valor: 4, nome: 'Abril' },
        { valor: 5, nome: 'Maio' }, { valor: 6, nome: 'Junho' },
        { valor: 7, nome: 'Julho' }, { valor: 8, nome: 'Agosto' },
        { valor: 9, nome: 'Setembro' }, { valor: 10, nome: 'Outubro' },
        { valor: 11, nome: 'Novembro' }, { valor: 12, nome: 'Dezembro' }
    ];

    meses.forEach(function(mes) {
        selectMes.append(`<option value="${mes.valor}">${mes.nome}</option>`);
    });

    $('#exportOptionsModal').on('shown.bs.modal', function () {
        $('#selectAno').selectpicker('refresh');
        $('#selectMes').selectpicker('refresh');
    });

    $('#btnExportarComFiltros').on('click', function() {
        const anoSelecionado = selectAno.val();
        const mesesSelecionados = selectMes.val();
        const tipoExportacao = $('input[name="tipoExportacao"]:checked').val();
 

        const pathname = window.location.pathname; 

        // Quebra o caminho em um array usando a barra como separador
        // Resultado: ["", "cadastros", "empresa", "editar", "1"]
        const parts = pathname.split('/');

        // Pega o último elemento do array, que é o ID
        const idEmpresa =  parts[parts.length - 1];

        if (!anoSelecionado) {
            setTimeout(function() {
                $('#loading-overlay').hide();
                $("#btnExportarComFiltros").attr("disabled", false);
                swal("Atenção", "Por favor, selecione um ano.", "warning");
            }, 500);
            return;
        }
        if (!mesesSelecionados || mesesSelecionados.length === 0) {
            setTimeout(function() {
                $('#loading-overlay').hide();
                $("#btnExportarComFiltros").attr("disabled", false);
                swal("Atenção", "Por favor, selecione pelo menos um mês.", "warning");
            }, 500);
            return;
        }

        const baseUrl = '<?php echo site_url("cadastros/empresa/exportar_franquia"); ?>';
        const downloadToken = new Date().getTime();

        const params = new URLSearchParams({
            id_empresa: idEmpresa,
            ano: anoSelecionado,
            meses: mesesSelecionados.join(','),
            downloadToken: downloadToken // Envia o token para o servidor
        });

        window.location.href = `${baseUrl}?${params.toString()}`;

        let downloadTimer = setInterval(function() {
        // Função para ler um cookie (helper)
        const getCookie = (name) => {
            const value = `; ${document.cookie}`;
            const parts = value.split(`; ${name}=`);
            if (parts.length === 2) return parts.pop().split(';').shift();
        }
        
        // Se o cookie com o nosso token existir...
        if (getCookie('downloadToken') == downloadToken) {
            clearInterval(downloadTimer); // Para de verificar
            document.cookie = "downloadToken=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;"; // Limpa o cookie

            // Executa as ações desejadas!
            $('#loading-overlay').hide();
            $("#btnExportarComFiltros").attr("disabled", false);

        }
    }, 1000); // Verifica a cada 1 segundo
    });


                // Adicional: Tooltips (código comentado como antes)
                /*
                if (typeof $ === 'function' && typeof $.fn.tooltip === 'function') {
                    $('.glyphicon-info-sign').tooltip({ placement: 'right', trigger: 'hover' });
                }
                */
            });



            $('body').on('keypress', '.cobranca-input', function(event) {
                var $this = $(this);
                var charCode = (event.which) ? event.which : event.keyCode;
                var value = $this.val();
                var cursorPosition = this.selectionStart;

                // Permite: números (0-9)
                if (charCode >= 48 && charCode <= 57) {
                    var commaIndex = value.indexOf(',');
                    if (commaIndex !== -1 && cursorPosition > commaIndex && value.substring(commaIndex + 1).length >= 2) {
                        if (this.selectionStart === this.selectionEnd) { // Nenhuma seleção
                            event.preventDefault();
                        }
                    }
                    return true;
                }

                // Permite: uma única VÍRGULA decimal
                if (charCode === 44) { // Vírgula (,)
                    if (value.indexOf(',') === -1) { // Só permite se não houver outra vírgula
                        return true;
                    } else {
                        event.preventDefault();
                        return false;
                    }
                }

                // Permite: teclas de controle como backspace, delete, tab, escape, enter, setas
                if (charCode === 8 || charCode === 46 || charCode === 9 || charCode === 27 || charCode === 13 ||
                    (charCode >= 35 && charCode <= 40)) {
                    return true;
                }
                // Permite: Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X, Command+A (para Mac)
                if ((event.ctrlKey || event.metaKey) && (charCode === 97 || charCode === 99 || charCode === 118 || charCode === 120 || charCode === 65 || charCode === 67 || charCode === 86 || charCode === 88)) {
                    return true;
                }

                // Bloqueia todas as outras teclas
                event.preventDefault();
                return false;
            });

            $('body').on('blur', '.cobranca-input', function() {
                var $this = $(this);
                var value = $this.val();

                if (value === "" || value === null) {
                    $this.val(""); // Garante que seja uma string vazia
                    return;
                }

                // 1. Substitui a vírgula por ponto para o processamento interno (parseFloat)
                var valueForParsing = value.replace(',', '.');

                // 2. Limpeza adicional: remove caracteres não numéricos exceto o primeiro ponto
                // (após a conversão da vírgula, só deve haver pontos aqui)
                var cleanedValue = valueForParsing.replace(/[^0-9.-]+/g, "");
                var parts = cleanedValue.split('.');
                if (parts.length > 2) { // Remove pontos extras se houver
                    cleanedValue = parts[0] + '.' + parts.slice(1).join('');
                }

                var num = parseFloat(cleanedValue);

                if (!isNaN(num)) {
                    // 3. Formata para duas casas decimais (toFixed sempre usa ponto)
                    var formattedWithDot = num.toFixed(2);
                    // 4. Substitui o ponto por vírgula para exibição
                    var formattedWithComma = formattedWithDot.replace('.', ',');
                    $this.val(formattedWithComma);
                } else {
                    // Se não for um número válido após a limpeza (ex: ",", "abc")
                    $this.val(""); // Limpa o campo
                }
            });

            $(document).ready(function() {
                $('form').on('submit', function(e) {
                    var checkboxMarcado = $('#habilitar_notificacoes_check').is(':checked');
                    var campoEmailVazio = $('#emails_notificados_input').val() === '';

                    if (checkboxMarcado && campoEmailVazio) {
                        swal("Atenção", "Você deve preencher os e-mails que receberão a notificação.", "warning");
                        e.preventDefault(); // Impede o envio do formulário
                    }
                });

            });
        </script>
        <div role="tabpanel" class="tab-pane" id="curva">
            <div class="col-md-12" style="padding-top: 30px;" id="container_curva">
                <?php
                if (empty($empresa_curvas)) {
                    $chr = ord('A');
                ?>
                    <div class="row" style="padding-left: 20%;" id="row_<?php echo $chr; ?>" data-chr="<?php echo $chr; ?>">
                        <div class="form-group">
                            <label class="col-md-1 control-label">Classe: </label>
                            <div class="col-md-1">
                                <input type="text" name="classe[]" id="classe_<?php echo $chr; ?>" class="form-control text-center" value="<?php echo strtoupper(chr($chr)); ?>" readonly>
                            </div>
                            <label class="col-md-1 control-label">Percentual: </label>
                            <div class="col-md-1">
                                <input type="text" name="percentual[]" class="form-control" value="">
                            </div>
                            <label class="col-md-1 control-label">Status: </label>
                            <div class="col-md-3">
                                <select name="status[]" class="form-control">
                                    <option value="" disabled selected>Selecione o status</option>
                                    <option value="considera">Considera escopo</option>
                                    <option value="nao_considera">Fora do escopo</option>
                                </select>
                            </div>
                            <a href="javascript:void(0);" class="btn btn-danger btn-sm delete-row" data-chr="<?php echo $chr; ?>"><i class="glyphicon glyphicon-trash"></i></a>
                        </div>
                    </div>
                    <?php
                } else {
                    foreach ($empresa_curvas as $i => $curva) :

                        $chr = ord($curva->classe);
                    ?>
                        <div class="row" style="padding-left: 20%;" id="row_<?php echo $chr; ?>" data-chr="<?php echo $chr; ?>">
                            <div class="form-group">
                                <label class="col-md-1 control-label">Classe: </label>
                                <div class="col-md-1">
                                    <input type="text" name="classe[]" id="classe_<?php echo $chr; ?>" class="form-control text-center" value="<?php echo strtoupper(chr($chr)); ?>" readonly>
                                </div>
                                <label class="col-md-1 control-label">Percentual: </label>
                                <div class="col-md-1">
                                    <input type="text" name="percentual[]" class="form-control" value="<?php echo $curva->percentual; ?>">
                                </div>
                                <label class="col-md-1 control-label">Status: </label>
                                <div class="col-md-3">
                                    <select name="status[]" class="form-control">
                                        <option value="" disabled selected>Selecione o status</option>
                                        <option value="considera" <?php echo ('considera' == $curva->status) ? 'selected' : NULL; ?>>Considera escopo</option>
                                        <option value="nao_considera" <?php echo ('nao_considera' == $curva->status) ? 'selected' : NULL; ?>>Fora do escopo</option>
                                    </select>
                                </div>
                                <?php if (count($empresa_curvas) - 1 == $i) { ?>
                                    <a href="javascript:void(0);" class="btn btn-danger btn-sm delete-row" data-chr="<?php echo $chr; ?>"><i class="glyphicon glyphicon-trash"></i></a>
                                <?php } ?>
                            </div>
                        </div>
                <?php endforeach;
                } ?>
            </div>
            <a id="add_row" class="btn btn-link">+ Adicionar classe</a>
        </div>
        <?php $this->load->view('cadastros/empresa/usuario-api'); ?>
    </div>
</div>
<div class="row" style="margin-bottom: 30px;">
    <hr />
    <div class="col-sm-5">
        <button type="submit" class="btn btn-primary" value="1" name="submit"><i class="glyphicon glyphicon-floppy-disk"></i> Salvar</button>
        <a href="<?php echo site_url("cadastros/empresa") ?>" class="btn">Cancelar</a>
    </div>
    <?php
    if ($entry->updated_at) {
    ?>
        <div class="pull-right col-sm-3">
            <p class="text-success" role="alert">
                <span class="glyphicon glyphicon-time"></span>
                <small>Modificado em <?php echo date('d/m/Y \à\s H:i:s', strtotime($entry->updated_at)) ?></small>
            </p>
        </div>
    <?php
    }
    ?>
</div>

<div class="modal fade" id="exportOptionsModal" tabindex="-1" role="dialog" aria-labelledby="exportModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="exportModalLabel">Filtros para Exportação</h4>
            </div>
            <div class="modal-body" style="padding: 50px;">
                <p class="text-info">Selecione o ano e os meses para filtrar os itens a serem exportados.</p>
                <div class="form-group">
                    <label for="selectAno">Ano</label>
                    <select id="selectAno" class="form-control selectpicker" data-width="100%" title="Selecione um ano"></select>
                </div>
                <div class="form-group">
                    <label for="selectMes">Mês(es)</label>
                    <select id="selectMes" class="form-control selectpicker" data-select-all-text="Todos" data-deselect-all-text="Nenhum" multiple data-actions-box="true" data-width="100%" title="Selecione um ou mais meses"></select>
                </div>
                <hr>
 
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" id="btnExportarComFiltros"><i class="glyphicon glyphicon-ok"></i> Exportar</button>
            </div>
        </div>
    </div>
</div>
<div id="loading-overlay">
    <div id="loading-message">Carregando...</div>
</div>
</form>

<script type="text/javascript">
    $(document).ready(function() {
        $('#destinatatio_tec').change(function() {
            if ($(this).is(':checked')) {

                $('#div_destinatarios_tec').removeClass('hide');
            } else {
                $('#div_destinatarios_tec').addClass('hide');
            }
        });
    });

    $(document).ready(function() {
        $('#chk_destinatarios_revisao_pucomex').change(function() {
            if ($(this).is(':checked')) {

                $('#div_destinatarios_revisao_pucomex').removeClass('hide');
            } else {
                $('#div_destinatarios_revisao_pucomex').addClass('hide');
            }
        });
    });

    $('#add_row').on('click', function() {

        var last_row_added = $('#container_curva').children().last();
        var ascii = $(last_row_added).attr('data-chr');

        //se for z, impedir que sejam adicionados novos rows;
        if (parseInt(ascii) == 122) {
            return true;
        }

        var new_ascii = parseInt(ascii) + 1;
        var new_row = $(last_row_added).clone(true).appendTo('#container_curva');

        $(last_row_added).find('.delete-row').remove();

        $(new_row).attr('data-chr', new_ascii);
        $(new_row).attr('id', 'row_' + new_ascii);

        var new_chr = String.fromCharCode(new_ascii);

        $(new_row).find('.delete-row').attr('data-chr', new_ascii);
        $(new_row).find('#classe_' + ascii).attr('id', 'classe_' + new_ascii).val(new_chr);
    });

    $('.delete-row').on('click', function() {

        if ($('#container_curva').children().length == 1) {
            return true;
        }

        chr = $(this).attr('data-chr');

        old_chr = parseInt(chr - 1);
        var new_remove = $(this).clone(true).appendTo('#row_' + old_chr + ' .form-group');
        $(new_remove).attr('data-chr', old_chr);

        $('#row_' + chr).remove();
    });


    $('form').on('submit', function(e) {
        const primeira = parseInt($('[name="percentual_primeira_notificacao"]').val());
        const segunda = parseInt($('[name="percentual_segunda_notificacao"]').val());
        const excedente = parseInt($('[name="percentual_excedente_input"]').val());
        var checkboxMarcadoNotificacoes = $('#habilitar_notificacoes_check').is(':checked');
        if (checkboxMarcadoNotificacoes && (excedente < 1 || excedente > 100)) {
            swal("Atenção", "A o percentual excedente deve ser entre 1 e 100.", "warning");
            $('[name="percentual_excedente_input"]').focus();
            e.preventDefault();
            return;
        }

        if (checkboxMarcadoNotificacoes && (primeira < 1 || primeira > 99)) {
            swal("Atenção", "A Primeira Notificação deve ser entre 1 e 99.", "warning");
            $('[name="percentual_primeira_notificacao"]').focus();
            e.preventDefault();
            return;
        }

        if (checkboxMarcadoNotificacoes && (segunda <= primeira || segunda > 100)) {
            swal("Atenção", "A Segunda Notificação deve ser maior que a Primeira.", "warning");
            $('[name="percentual_segunda_notificacao"]').focus();
            e.preventDefault();
            return;
        }
    });
    $(document).ready(function() {
        $('#quantidade_franquia_mensal').on('keydown', function(e) {
            // Bloqueia a tecla "-" (código 189 e 109 para numpad)
            if (e.key === '-' || e.keyCode === 189 || e.keyCode === 109) {
                e.preventDefault();
            }
        });

        $('#quantidade_franquia_mensal').on('input', function() {
            // Remove qualquer sinal de menos que tenha sido colado
            let valor = $(this).val();
            valor = valor.replace(/-/g, '');
            $(this).val(valor);
        });
    });

    document.addEventListener("DOMContentLoaded", function() {
        const form = document.getElementById("main-content");
        const input = document.getElementById("emails_notificados_input");

        // Restringe os caracteres permitidos no campo
        input.addEventListener("input", function() {
            this.value = this.value.replace(/[^a-zA-Z@;.]/g, '');
        });

        if (form) {
            // Valida os emails no submit
            form.addEventListener("submit", function(e) {
                const valor = input.value;

                if (!validarEmails(valor)) {
                    e.preventDefault();
                    swal("Atenção", "Por favor, insira apenas e-mails válidos separados por ponto e vírgula (;).", "warning");
                }
            });
        }

        function validarEmails(inputStr) {
            const emails = inputStr.split(';').map(e => e.trim()).filter(e => e !== '');
            const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
            return emails.every(email => emailRegex.test(email));
        }
    });


    document.addEventListener("DOMContentLoaded", function() {
        const input = document.getElementById("percentual_input");

        // Ao digitar, mantém número + %
        input.addEventListener("input", function() {
            let val = this.value.replace(/\D/g, ''); // Remove tudo que não for número
            let num = parseInt(val);

            if (!isNaN(num) && num >= 1 && num <= 99) {
                this.value = num + '%';
            } else if (val === '') {
                this.value = '';
            } else {
                // Se número inválido (>99), corta para 99
                this.value = '99%';
            }
        });

        // Ao focar, remove % para facilitar edição
        input.addEventListener("focus", function() {
            this.value = this.value.replace('%', '');
        });

    });
</script>


<?php if ($this->session->flashdata('message_on_render')) : ?>
    <script>
        window.addEventListener('DOMContentLoaded', function() {
            swal({
                title: "Erro!",
                text: "Existem itens com status específicos vinculados a esta empresa => Revisar Informações Técnicas e/ou Homologado em Revisão.",
                type: "error",
                confirmButtonText: "Ok"
            });
        });
    </script>
<?php endif; ?>
<?php if ($this->session->flashdata('error_franquia')) : ?>
    <script>
        let error_msg = "<?php echo $this->session->flashdata('error_franquia'); ?>";
        window.addEventListener('DOMContentLoaded', function() {
            swal({
                title: "Atenção!",
                text: error_msg,
                type: "warning",
                confirmButtonText: "Ok"
            });
        });
        $('#loading-overlay').hide();
 
        $("#btn_rel_franquia").attr("disabled", false);
    </script>
<?php endif; ?>
<?php if ($this->session->flashdata('ok_franquia')) : ?>
    <script>
         $('#loading-overlay').hide();
 
        $("#btn_rel_franquia").attr("disabled", false);
    </script>
<?php endif; ?>
<script>
    document.addEventListener("DOMContentLoaded", function() {


        const percentualInputs = [
            document.getElementById("percentual_input"),
            document.getElementById("percentual_segunda_input"),
            document.getElementById("percentual_excedente_input")
        ];
        const emailInput = document.getElementById('emails_notificados_input');

        emailInput.addEventListener('blur', function() {
            const value = emailInput.value.trim();

            if (value && !value.includes('@')) {

                swal('Atenção!', 'Deve ser um email válido', 'warning');
                emailInput.value = '';
            }
        });

        percentualInputs.forEach(input => {
            // Ao digitar, adiciona % automaticamente
            input.addEventListener("input", function() {
                let val = this.value.replace(/\D/g, ''); // remove não números
                let num = parseInt(val);

                if (!isNaN(num) && num >= 1 && num <= 100) {
                    this.value = num + '%';
                } else if (val === '') {
                    this.value = '';
                } else {
                    this.value = '100%'; // máximo permitido
                }
            });

            // Ao focar, remove o %
            input.addEventListener("focus", function() {
                this.value = this.value.replace('%', '');
            });

            // Ao sair do campo, garante que o valor fique com %
            input.addEventListener("blur", function() {
                let val = this.value.replace(/\D/g, '');
                let num = parseInt(val);

                if (!isNaN(num) && num >= 1 && num <= 100) {
                    this.value = num + '%';
                } else {
                    this.value = '';
                }
            });
        });



        const radioButtons = document.querySelectorAll('input[name="tipo_bloqueio"]');
        const percentualInputContainer = document.getElementById('percentual_excedente_input').closest('.form-group');
        const percentualInput = document.getElementById('percentual_excedente_input');

        function togglePercentualInput(value) {
            if (value === '1') {
                percentualInput.value = '';
                percentualInputContainer.style.display = 'none';
            } else {
                percentualInputContainer.style.display = 'block';
            }
        }

        radioButtons.forEach(radio => {
            radio.addEventListener('change', function() {
                togglePercentualInput(this.value);
            });

            if (radio.checked) {
                togglePercentualInput(radio.value);
            }
        });
    });
</script>

<script>
$(document).ready(function() {
    $('#habilitar_franquia_check').change(function() {
        if($(this).is(':checked')) {
            $('.btn-download-xls').show();
        } else {
            $('.btn-download-xls').hide();
        }
    });

    // Garante que o botão esteja escondido caso a página carregue com o checkbox desmarcado
    if(!$('#habilitar_franquia_check').is(':checked')) {
        $('.btn-download-xls').hide();
    }
});
</script>

<style>
    #loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 9999;
        display: none;
    }
    #loading-message {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: white;
        font-size: 24px;
    }
</style>
 
<script type="text/javascript">
    $(function() {
        $('#btnExportarComFiltros').click(function(e) {
            $('#loading-overlay').show();
            e.preventDefault();
            $("#btnExportarComFiltros").attr("disabled", true);
        });
    });

 
</script>

