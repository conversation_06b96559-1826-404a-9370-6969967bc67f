<?php

/**
 * Componente Modal de Filtros reutilizável
 * @param array $basic_filters Campos básicos
 * @param array $advanced_filters Campos avançados
 * @param string $form_action Endpoint do form
 */
?>
<style>
    #filterModal .modal-dialog {
        max-width: 1200px;
        margin: 2rem auto;
    }

    #filterModal .modal-content {
        border-radius: 10px;
    }

    #filterModal .modal-body {
        padding: 2rem 1.5rem 1rem 1.5rem;
    }

    #filterModal .filter-row {
        display: flex;
        flex-wrap: wrap;
        gap: 1.5rem;
        margin-bottom: 0.5rem;
    }

    #filterModal .filter-col {
        flex: 0 0 24%;
        max-width: 24%;
        min-width: 220px;
        box-sizing: border-box;
    }

    #filterModal .card {
        border-radius: 8px;
        margin-top: 1.5rem;
    }

    #filterModal .card-header {
        display: flex;
        justify-content: space-between;
        background: #f7f7f7;
        font-weight: 500;
        cursor: pointer;
        border-bottom: 1px solid #e5e5e5;
        border-radius: 8px 8px 0 0;
        padding: 1.3rem 1.25rem;
        font-size: 16px;
    }

    #filterModal .card-header .float-right {
        margin-right: 1rem;
        font-size: 2rem;
    }

    #advancedFilters {
        margin-top: 15px;
    }

    #filterModal .form-check.diana-switch {
        display: flex;
        align-items: center;
        margin-top: 2.1rem;
    }

    #filterModal .form-check-input {
        width: 1.5em;
        height: 1.5em;
        margin-right: 0.5em;
    }

    #filterModal .form-check-label {
        font-weight: 500;
        font-size: 1rem;
    }

    #filterModal .date-range-col {
        flex: 0 0 33%;
        max-width: 32.4%;
        min-width: 280px;
        /* Ajuste se necessário para telas menores; mantém consistência com o min-width original de 220px, mas aumentado para melhor preenchimento */
    }

    #filterModal .modal-title {
        font-size: 2rem;
        font-weight: 500;
        /* margin-bottom: 1rem; */
    }

    /* Estilo do botão de fechar do modal para ficar maior e na mesma linha que o title */
    #filterModal .close {
        font-size: 3rem;
        font-weight: 500;
        position: absolute;
        right: 1.1rem;
        top: 1.1rem;
    }

    .toggle-switch {
        position: relative;
        display: inline-block;
        width: 100%;
        margin-top: 5px;
    }

    .toggle-checkbox {
        display: none;
    }

    .toggle-label {
        display: flex;
        align-items: center;
        cursor: pointer;
        margin-bottom: 0;
        font-weight: normal;
    }

    .toggle-inner {
        position: relative;
        display: inline-block;
        width: 50px;
        height: 24px;
        background-color: #ccc;
        border-radius: 24px;
        transition: background-color 0.3s;
        margin-right: 10px;
        flex-shrink: 0;
    }

    .toggle-inner:before {
        content: '';
        position: absolute;
        top: 2px;
        left: 2px;
        width: 20px;
        height: 20px;
        background-color: white;
        border-radius: 50%;
        transition: transform 0.3s;
    }

    .toggle-checkbox:checked+.toggle-label .toggle-inner {
        background-color: #CD1F73;
    }

    .toggle-checkbox:checked+.toggle-label .toggle-inner:before {
        transform: translateX(26px);
    }

    .toggle-switch-text {
        font-size: 14px;
        color: #333;
        user-select: none;
    }

    .toggle-checkbox:checked+.toggle-label .toggle-switch-text {
        color: #CD1F73;
        font-weight: 500;
    }

    /* Regras visuais para a página principal (barra de pesquisa e badges) */
    .page-search-input {
        width: 100%;
        min-height: 80px;
        padding: 12px 14px;
        border-radius: 8px;
        box-shadow: none;
        border: 1px solid #d6d6d6;
        font-size: 14px;
        line-height: 1.4;
        resize: vertical;
    }

    .filter-badges {
        margin-top: 12px;
        margin-bottom: 16px;
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
    }

    .filter-badges .badge {
        background: #4A90E2;
        color: #fff;
        padding: 4px 12px;
        border-radius: 12px;
        font-weight: 600;
        font-size: 12px;
        position: relative;
        display: inline-flex;
        align-items: center;
        border: none;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
        height: 24px;
        min-height: 24px;
        max-height: 24px;
        line-height: 1;
        white-space: nowrap;
    }

    .filter-badges .badge:hover {
        background: #357ABD;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
    }

    .filter-badges .badge .badge-text {
        font-weight: 700;
        margin-right: 6px;
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 200px;
    }

    .filter-badges .badge .remove-filter {
        cursor: pointer;
        opacity: 0.8;
        font-weight: 700;
        font-size: 14px;
        line-height: 1;
        padding: 0 2px;
        margin-left: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        transition: all 0.2s ease;
    }

    .filter-badges .badge .remove-filter:hover {
        opacity: 1;
        background: rgba(255, 255, 255, 0.2);
    }

    .search-bar-action {
        background: #2C6FA9;
        color: #fff;
        height: 40px;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        width: 100%;
        border: none;
    }

    .filter-btn {
        position: relative;
        width: 42px;
        height: 40px;
        padding: 0 10px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        border-radius: 6px;
        background-color: #2C6FA9 !important;
        border-color: #2C6FA9 !important;
        color: #fff !important;
    }

    .filter-btn:hover {
        background-color: #357ABD !important;
        border-color: #357ABD !important;
    }

    .filter-btn .count {
        position: absolute;
        top: -8px;
        right: -8px;
        background: #dc3545;
        color: #fff;
        font-size: 11px;
        font-weight: 600;
        padding: 3px 7px;
        border-radius: 12px;
        min-width: 18px;
        text-align: center;
        line-height: 1;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    .actions {
        display: flex;
        gap: 10px;
        border-radius: 6px !important;
    }

    .dropdown-menu-down {
        margin-top: 3.5rem;
        transform: translateY(0);
        transition: all 0.3s ease;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        padding: 8px 0;
        min-width: 220px;
    }

    /* Botão outline azul */
    .btn-outline-primary {
        color: #337ab7;
        /* cor padrão primary do bootstrap 3 */
        background-color: transparent;
        border: 1px solid #337ab7;
    }

    /* Hover: fica azul preenchido */
    .btn-outline-primary:hover,
    .btn-outline-primary:focus {
        color: #fff;
        background-color: #337ab7;
        border-color: #2e6da4;
    }

    /* Estado ativo/clicado */
    .btn-outline-primary:active,
    .btn-outline-primary.active,
    .open>.dropdown-toggle.btn-outline-primary {
        color: #fff;
        background-color: #286090;
        border-color: #204d74;
    }


    .top-actions {
        display: flex !important;
        gap: 10px;
        align-items: center;
        justify-content: flex-end !important;
    }

    .top-actions .btn {
        height: 36px;
        padding: 6px 12px;
        border-radius: 6px;
        vertical-align: middle;
    }

    /* Bootstrap 3 compatibility - alinhamento vertical */
    /* Ajusta corretamente o header-row para suportar flexbox */
    .row.header-row {
        display: flex;
        /* em vez de table */
        align-items: center;
        /* alinha verticalmente */
        justify-content: space-between;
        /* título à esquerda, botões à direita */
        width: 100%;
        margin-top: 16px;
    }

    /* remove o comportamento table-cell herdado */
    .row.header-row>[class*="col-"] {
        display: block;
        /* volta ao padrão do Bootstrap */
        float: none;
        /* garante que o flex assuma */
    }


    .date-range-group {
        display: flex;
        align-items: center;
        gap: 0;
        /* Remove o gap para colar os grupos */
    }

    .date-range-group .input-group {
        flex: 1;
        display: flex;
        align-items: center;
        /* Adiciona alinhamento vertical */
    }

    .date-range-group .input-group:first-child .form-control {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
        border-right: none;
    }

    .date-range-group .input-group:last-child .input-group-addon {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
        border-left: none;
        /* Remove borda esquerda para evitar duplicação */
    }

    .date-range-group .input-group-addon {
        border: 1px solid #ccc;
        background-color: #e9ecef;
        padding: 6px 18px;
        text-align: center;
        display: flex;
        /* Usar flexbox para centralização */
        align-items: center;
        /* Centralização vertical */
        justify-content: center;
        /* Centralização horizontal */
        height: 34px;
        /* Altura consistente com o input */
        line-height: normal;
        /* Remove line-height padrão */
    }

    .date-range-group .input-group:first-child .input-group-addon {
        border-right: 1px solid #ccc;
        /* Mantém a borda direita */
        border-radius: 4px 0 0 4px;
    }

    .date-range-group .input-group:last-child .form-control {
        border-radius: 0 4px 4px 0;
        height: 34px;
    }

    /* Garante que os inputs tenham a mesma altura */
    .date-range-group .form-control {
        height: 34px;
    }

    /* Opcional: manter o ícone do date picker */
    .date-range-group input[type="date"]::-webkit-calendar-picker-indicator {
        display: none;
        /* Comentado para manter o ícone */
    }

    /* aumenta fonte e melhora espaçamento */
    .dropdown-menu>li>a {
        font-size: 14px;
        /* ajuste aqui (padrão bootstrap é 13px) */
        /* padding: 8px 16px; */
        /* aumenta espaço */
    }

    .items-menu-dropdown {
        font-size: 16px !important;
        padding: 8px 14px !important;
    }
</style>
<div
    class="modal fade"
    id="filterModal"
    tabindex="-1"
    role="dialog"
    aria-labelledby="filterModalLabel"
    aria-hidden="true"
    data-endpoint-tags="<?= base_url('atribuir_grupo/ajax_get_tags_modal') ?>">
    <div
        class="modal-dialog modal-lg"
        role="document">
        <form method="post" action="#" id="filterForm" data-endpoint="<?= $form_action ?>">
            <input type="hidden" id="order_item" name="part_number" value="asc" />
            <input type="hidden" name="filtered" value="1" />
            <div class="modal-content">
                <div class="modal-header">
                    <div class="modal-title" id="filterModalLabel">Filtros</div>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="filter-row">
                        <?php foreach ($basic_filters as $filter): ?>
                            <?php
                            $colClass = 'filter-col';
                            // Estilo especial para o checkbox de triagem Diana
                            if ($filter['type'] === 'checkbox' && strpos(strtolower($filter['name']), 'triagem') !== false) {
                                $filter['extra_class'] = 'form-check diana-switch';
                            }
                            $this->load->view('components/filter_field', array_merge($filter, ['col_class' => $colClass]));
                            ?>
                        <?php endforeach; ?>
                    </div>
                    <?php
                    $advanced_filters_applied = !empty($filtros['novo_material']) ||
                        !empty($filtros['estabelecimento']) ||
                        !empty($filtros['importado']) ||
                        !empty($filtros['farol_sla']) ||
                        !empty($filtros['data_criacao']['from']) || !empty($filtros['data_criacao']['to']) ||
                        !empty($filtros['data_modificacao']['from']) || !empty($filtros['data_modificacao']['to']) ||
                        !empty($filtros['data_importado']['from']) || !empty($filtros['data_importado']['to']);

                    $collapse_class = $advanced_filters_applied ? 'collapse show' : 'collapse';
                    $icon_class = $advanced_filters_applied ? 'fa fa-chevron-up' : 'fa fa-chevron-down';
                    ?>
                    <div class="card mt-3">
                        <div class="card-header" data-toggle="collapse" data-target="#advancedFilters" style="cursor:pointer;">
                            Filtros avançados
                            <span class="float-right"><i class="<?php echo $icon_class; ?>"></i></span>
                        </div>
                        <div id="advancedFilters" class="<?php echo $collapse_class; ?>">
                            <div class="filter-row">
                                <?php foreach ($advanced_filters as $filter): ?>
                                    <?php $this->load->view('components/filter_field', array_merge($filter, ['col_class' => 'filter-col'])); ?>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-primary" id="btn-filtrar-modal">Filtrar</button>
                </div>
            </div>
        </form>
    </div>
</div>

<script type="text/javascript" src="<?php echo base_url('assets/js/filter_modal_dynamic.js?version=' . config_item('assets_version')); ?>"></script>

<script>
    $(document).ready(function() {
        $('#advancedFilters').on('show.bs.collapse', function() {
            $(this).prev().find('.fa').removeClass('fa-chevron-down').addClass('fa-chevron-up');
        });

        $('#advancedFilters').on('hide.bs.collapse', function() {
            $(this).prev().find('.fa').removeClass('fa-chevron-up').addClass('fa-chevron-down');
        });

        $(function() {
            $('.datetimepicker').datetimepicker({
                'format': 'DD/MM/YYYY',
                'locale': 'pt-BR'
            });
        });
    });
</script>