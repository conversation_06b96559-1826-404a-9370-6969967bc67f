<?php
$form_action = base_url('atribuir_grupo/index');

$this->load->view('components/filter_modal', [
    'basic_filters' => $basic_filters,
    'advanced_filters' => $advanced_filters,
    'form_action' => $form_action,
    'filtros' => $filtros
]);

$this->load->view('atribuir_grupo/modal-tag');
$this->load->view('atribuir_grupo/modal-atribuir-grupo');
$this->load->view('atribuir_grupo/modal-transferencia');
$this->load->view('atribuir_grupo/modal-transferencia-owner', ['ownersToTransfer' => $ownersToTransfer]);

if ($empresa_pais) {
    $this->load->view('cadastros/mestre_itens/modal-multi-paises', ['empresa_pais' => $empresa_pais, 'entry' => $entry]);
}

if (customer_has_role('desblquear_itens', sess_user_id())) {
    $this->load->view('atribuir_grupo/modal-desbloquear-itens');
}
?>
<style>
    .result-container-title {
        font-size: 20px;
    }

    .result-container-badge {
        font-size: 15px;
    }

    /* Estilos específicos para os botões de ação conforme Figma */
    .header-row .btn-group {
        margin-right: 12px;
    }

    .header-row .btn-group:last-child {
        margin-right: 0;
    }

    /* Ajustes específicos para o dropdown de ações */
    #dropdownMenu2 {
        min-width: 44px;
        height: 38px;
    }

    /* Melhor alinhamento dos botões */
    .btn-toolbar .btn-group .btn {
        height: 38px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* Estilos para checkboxes e ícones de bloqueio */
    .item_selected.travado {
        opacity: 0.6;
    }

    .click-select {
        cursor: pointer;
    }

    .click-select td:first-child {
        white-space: nowrap;
        width: 1%;
    }

    .click-select td:first-child .glyphicon {
        margin-left: 5px;
    }

    .click-select td:first-child .label {
        margin-left: 3px;
        font-size: 10px;
    }
</style>

<script type="text/javascript">
    // Injetar filtros salvos do backend no JavaScript
    window.eventosSelecionados = <?php echo json_encode($filtros_salvos['eventosSelecionados']); ?>;
    window.sistemasOrigemSelecionados = <?php echo json_encode($filtros_salvos['sistemasOrigemSelecionados']); ?>;
    window.ownersSelecionados = <?php echo json_encode($filtros_salvos['ownersSelecionados']); ?>;
    window.prioridadesSelecionadas = <?php echo json_encode($filtros_salvos['prioridadesSelecionadas']); ?>;
    window.usuariosSelecionados = <?php echo json_encode($filtros_salvos['usuariosSelecionados']); ?>;
    window.statusSelecionados = <?php echo json_encode($filtros_salvos['statusSelecionados']); ?>;
    window.triagemDianaFalhaSelecionado = <?php echo json_encode($filtros_salvos['triagemDianaFalhaSelecionado']); ?>;
    window.novoMaterialSelecionado = <?php echo json_encode($filtros_salvos['novoMaterialSelecionado']); ?>;
    window.estabelecimentoSelecionado = <?php echo json_encode($filtros_salvos['estabelecimentoSelecionado']); ?>;
    window.importadoSelecionado = <?php echo json_encode($filtros_salvos['importadoSelecionado']); ?>;
    window.farolSlaSelecionado = <?php echo json_encode($filtros_salvos['farolSlaSelecionado']); ?>;
    window.dataCriacaoFrom = <?php echo json_encode($filtros_salvos['dataCriacaoFrom']); ?>;
    window.dataCriacaoTo = <?php echo json_encode($filtros_salvos['dataCriacaoTo']); ?>;
    window.dataModificacaoFrom = <?php echo json_encode($filtros_salvos['dataModificacaoFrom']); ?>;
    window.dataModificacaoTo = <?php echo json_encode($filtros_salvos['dataModificacaoTo']); ?>;
    window.dataImportadoFrom = <?php echo json_encode($filtros_salvos['dataImportadoFrom']); ?>;
    window.dataImportadoTo = <?php echo json_encode($filtros_salvos['dataImportadoTo']); ?>;

    const base_url = "<?php echo base_url() ?>";
</script>
<div id="edit-item">
    <v-edit-item
        has-prioridade="<?php echo in_array('prioridade', $campos_adicionais); ?>"
        has-peso="<?php echo in_array('peso', $campos_adicionais); ?>"></v-edit-item>
</div>
<script type="text/javascript" src="<?php echo base_url('assets/vuejs/dist/edit-item.js?version=' . config_item('assets_version')) ?>"></script>
<link rel="stylesheet" href="<?php echo base_url('assets/vuejs/dist/edit-item.css?version=' . config_item('assets_version')) ?>" type="text/css" media="screen" />

<div class="container-fluid pt-4">
    <div class="row header-row" style="margin-bottom: 15px;">
        <div class="col-md-6">
            <h2 style="margin-top: 0; margin-bottom: 0; line-height: 36px;">Dados Técnicos</h2>
        </div>
        <div class="col-md-6 d-flex top-actions" style="text-align: right; padding-right: 0;">
            <?php if ($empresa_pais) : ?>
                <div class="dropdown" style="margin-right: 8px;">
                    <button class="btn btn-success dropdown-toggle" type="button" id="dropdownMenu1" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true" data-loading-text="Gerando...">
                        Exportar <i class="glyphicon glyphicon-cloud-download"></i>
                        <span class="caret"></span>
                    </button>
                    <ul class="dropdown-menu" aria-labelledby="dropdownMenu1">
                        <li><a href="#" id="send_generate_log" data-loading-text="Gerando...">Padrão</a></li>
                        <li><a href="#" id="send_multipaises_log" data-loading-text="Gerando...">Multi Paises</a></li>
                    </ul>
                </div>
            <?php else : ?>
                <button id="send_generate_log" type="button" class="btn btn-success" data-loading-text="Gerando...">
                    Exportar <i class="glyphicon glyphicon-cloud-download"></i>
                </button>
            <?php endif; ?>
            <div>
                <a class="btn btn-primary actions" data-toggle="modal" data-target="#perguntasRespostas">
                    Perguntas e Respostas <i class="glyphicon glyphicon-question-sign"></i>
                </a>
            </div>
        </div>
    </div>

    <div class="row header-row" style="margin-bottom: 10px; margin-top: 4rem;">
        <div class="col-md-12" style="text-align: right; padding-right: 0;">
            <!-- Agrupamento total alinhado à direita -->
            <div class="btn-toolbar" role="toolbar" style="display: inline-flex;">

                <!-- Dropdown à esquerda -->
                <div class="btn-group" role="group" style="margin-right: 8px;">
                    <div class="dropdown">
                        <!-- Botão com ícone + seta -->
                        <button class="btn btn-outline-primary dropdown-toggle" type="button" id="dropdownMenu2"
                            data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                            <i class="glyphicon glyphicon-book" style="margin-right: 5px;"></i> <span class="caret"></span>
                        </button>

                        <!-- Menu alinhado ao botão -->
                        <ul class="dropdown-menu dropdown-menu-down" aria-labelledby="dropdownMenu2">
                            <li><a class="items-menu-dropdown" href="#" id="alterar_pre_agrupamento" data-toggle="modal" data-target="#modal_change_tag"><i class="fa fa-random"></i> Alterar pré-agrupamento</a></li>
                            <li><a class="items-menu-dropdown" href="#" id="desbloquear_item"><i class="fa fa-unlock"></i> Desbloquear item</a></li>
                            <li><a class="items-menu-dropdown" href="#" id="desvincularGrupo"><i class="fa fa-chain-broken"></i> Desvincular</a></li>
                            <li role="separator" class="divider"></li>
                            <?php if ($has_owner) : ?>
                                <li><a class="items-menu-dropdown" data-toggle="modal" data-target="#modal-transferencia-owner" href="#" id="btn-transferencia-owner"><i class="fa fa-user"></i> Transferir Owner</a></li>
                            <?php endif; ?>
                            <li><a class="items-menu-dropdown" data-toggle="modal" data-target="#transferencia-modal" href="#" id="btn-transferir"><i class="fa fa-user"></i> Transferir responsável</a></li>
                        </ul>
                    </div>
                </div>

                <!-- Grupo de botões principais -->
                <div class="btn-group" role="group">
                    <?php if (in_array('prioridade', $campos_adicionais) || in_array('peso', $campos_adicionais)) : ?>
                        <button type="submit" class="btn btn-success actions"
                            data-toggle="modal" data-target="#editItem" style="margin-right: 13px;"
                            <?php echo has_role('becomex_pmo') && (!has_role('sysadmin') && !has_role('consultor')) ? 'disabled' : null; ?>>
                            Editar itens <i class="glyphicon glyphicon-pencil"></i>
                        </button>
                    <?php endif; ?>
                    <button class="btn btn-primary actions">Atribuir <i class="fa fa-check"></i></button>
                </div>

            </div>
        </div>
    </div>



    <div class="row" style="margin-bottom: 10px;">
        <div class="col-md-12">
            <textarea
                class="form-control page-search-input"
                placeholder="Pesquisar..."
                rows="3"
                style="resize: vertical; min-height: 80px;"
                id="main_search_input"
                value="<?php echo !empty($search) ? $search : ''; ?>"
                name="search"><?php echo !empty($search) ? $search : ''; ?></textarea>
        </div>
    </div>

    <div class="row" style="margin-bottom: 10px;">
        <div class="col-md-12">
            <div id="active-filters" class="filter-badges">
                <!-- Badges de filtros selecionados serão renderizados aqui via JS -->
            </div>

            <div style="display: table; width: 100%;">
                <div style="display: table-cell; width: 100%; padding-right: 10px;">
                    <button id="btn-pesquisar" class="search-bar-action" style="width: 100%;">Pesquisar</button>
                </div>
                <div style="display: table-cell; white-space: nowrap;">
                    <button id="btn-open-filter-modal" class="btn filter-btn" data-toggle="modal" data-target="#filterModal" aria-label="Abrir filtros" style="margin-right: 8px; position: relative;">
                        <i class="fa fa-filter"></i>
                        <span id="filter-count-badge" class="count" style="display:none;">0</span>
                    </button>
                    <button id="btn-clear-filters" class="btn btn-outline-secondary">Limpar</button>
                </div>
            </div>
        </div>
    </div>
    <?php if (!empty($itens)) : ?>
        <div class="row" style="margin-bottom: 10px;">
            <div class="col-md-12">
                <div class="card">
                    <div class="result-container d-flex p-2">
                        <div class="result-container-title font-weight-bold col-md-7" style="margin: auto 0;">Resultado:

                            <span class="result-container-badge badge badge-pill badge-primary"><?php echo $total_rows; ?></span>
                        </div>
                        <?php if (!empty($pagination)) : ?>
                            <div class="controls col-md-5" style="margin: auto 0;">
                                <div class="pull-right">
                                    <?php echo $pagination; ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12">
                <table class="table table-bordered table-hover">
                    <thead>
                        <tr>
                            <th></th>
                            <th>Part Number</th>
                            <?php if ($has_owner) : ?>
                                <th>Owner</th>
                            <?php else : ?>
                                <th>NCM</th>
                            <?php endif; ?>
                            <th>Estab.</th>
                            <th>Peso</th>
                            <th>Prior.</th>
                            <th>SLA Hrs restantes</th>
                            <th>Farol</th>
                            <th>Descrição</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($itens as $item) : ?>
                            <?php
                            // Lógica de bloqueio do checkbox
                            $desabilitado = "";
                            if (
                                !empty($item->usuario_bloqueador) &&
                                $item->usuario_bloqueador != $id_usuario_logado &&
                                $usuario_desbloqueador != 1
                            ) {
                                $desabilitado = "disabled";
                            }
                            ?>
                            <tr class="click-select">
                                <td>
                                    <input type="checkbox"
                                        data-bloqueador="<?php echo $item->usuario_bloqueador ?? ''; ?>"
                                        class="item_selected"
                                        <?php echo $desabilitado; ?>
                                        data-part-number="<?php echo htmlspecialchars($item->part_number ?? '', ENT_QUOTES); ?>"
                                        data-tag="<?php echo htmlspecialchars($item->tag ?? '', ENT_QUOTES); ?>"
                                        data-descricao="<?php echo htmlspecialchars($item->descricao ?? '', ENT_QUOTES); ?>"
                                        data-ncm_item="<?php echo htmlspecialchars($item->ncm ?? '', ENT_QUOTES); ?>"
                                        data-estabelecimento="<?php echo htmlspecialchars($item->estabelecimento ?? '', ENT_QUOTES); ?>"
                                        name="item[]"
                                        value="<?php echo htmlspecialchars($item->part_number ?? '', ENT_QUOTES); ?>" />

                                    <?php if (!empty($item->usuario_bloqueador) && $item->usuario_bloqueador != $id_usuario_logado) : ?>
                                        <i class="glyphicon glyphicon-lock"
                                            style="color:#FF0000"
                                            data-toggle="tooltip"
                                            data-placement="top"
                                            title="Bloqueado por <?php echo htmlspecialchars($item->nome_usuario_bloqueador ?? 'Usuário desconhecido', ENT_QUOTES); ?>"></i>
                                    <?php elseif (!empty($item->usuario_bloqueador) && $item->usuario_bloqueador == $id_usuario_logado) : ?>
                                        <i class="glyphicon glyphicon-lock"
                                            style="color:#008000"
                                            data-toggle="tooltip"
                                            data-placement="top"
                                            title="Bloqueado por <?php echo htmlspecialchars($item->nome_usuario_bloqueador ?? 'Você', ENT_QUOTES); ?>"></i>
                                    <?php endif; ?>
                                    <br>
                                    <?php if (isset($item->sistema_origem) && $item->sistema_origem == "MANUAL") : ?>
                                        <span class="label label-success"
                                            data-toggle="tooltip"
                                            title="MANUAL">M</span>
                                    <?php endif; ?>
                                </td>
                                <td width="19%" style="word-break: break-word;">
                                    <div class="d-flex">
                                        <?php if ($integracao_simplus == true) : ?>
                                            <a href="javascript: void(0)"
                                                class="part_number_click"
                                                data-estabelecimento-simplus="<?php echo htmlspecialchars($item->estabelecimento ?? '', ENT_QUOTES); ?>"
                                                data-part-number="<?php echo htmlspecialchars($item->part_number ?? '', ENT_QUOTES); ?>">
                                            <?php else : ?>
                                                <a href="javascript: void(0)"
                                                    class="part_number_inf"
                                                    data-estabelecimento="<?php echo htmlspecialchars($item->estabelecimento ?? '', ENT_QUOTES); ?>"
                                                    data-part-number="<?php echo htmlspecialchars($item->part_number ?? '', ENT_QUOTES); ?>">
                                                <?php endif; ?>

                                                <?php echo htmlspecialchars($item->part_number ?? '', ENT_QUOTES); ?><br />

                                                <?php if (!empty($item->pn_primario_mpn) && isset($item->hasPnPrimarioSecundario) && $item->hasPnPrimarioSecundario == "S") : ?>
                                                    <?php echo htmlspecialchars($item->pn_primario_mpn, ENT_QUOTES); ?><br />
                                                <?php endif; ?>

                                                <?php if (!empty($item->pn_secundario_ipn) && isset($item->hasPnPrimarioSecundario) && $item->hasPnPrimarioSecundario == "S") : ?>
                                                    <?php echo htmlspecialchars($item->pn_secundario_ipn, ENT_QUOTES); ?>
                                                <?php endif; ?>

                                                </a>

                                                <?php if (isset($item->item_ja_homologado) && $item->item_ja_homologado == 1) : ?>
                                                    <span class='bg-success'
                                                        data-toggle='tooltip'
                                                        title='Item já homologado anteriormente'
                                                        style='margin-left: 5px;'>
                                                        <img src="<?php echo base_url('assets/img/ok-icon.ico'); ?>"
                                                            alt="Ícone check verde para representar OK"
                                                            width="14">
                                                    </span>
                                                <?php endif; ?>
                                    </div>

                                    <?php if (isset($item->has_pergunta) && $item->has_pergunta > 0) : ?>
                                        <?php if (isset($item->has_pergunta_pendente) && $item->has_pergunta_pendente > 0) : ?>
                                            <span class="label label-warning">
                                                <?php echo $item->has_pergunta_pendente; ?> pergunta(s) pendente(s)
                                            </span>
                                        <?php else : ?>
                                            <span class="label label-success">nenhuma pergunta pendente</span>
                                        <?php endif; ?>
                                    <?php endif; ?>

                                    <?php if ($has_status_triagem_diana) : ?>
                                        <?php if (isset($item->status_triagem_diana) && $item->status_triagem_diana == "Pendente de triagem" && isset($item->id_status) && $item->id_status == 6) : ?>
                                            <br><span class="label label-warning">Pendente de triagem</span>
                                        <?php elseif (isset($item->status_triagem_diana) && $item->status_triagem_diana == "Triagem aprovada" && isset($item->id_status) && ($item->id_status == 6 || $item->id_status == 8)) : ?>
                                            <br><span class="label label-success">Triagem aprovada</span>
                                        <?php elseif (isset($item->status_triagem_diana) && $item->status_triagem_diana == "Triagem reprovada" && isset($item->id_status) && $item->id_status == 7) : ?>
                                            <br><span class="label" style="background-color: #B01A00">Triagem reprovada</span>
                                        <?php elseif (isset($item->status_triagem_diana) && $item->status_triagem_diana == "Falha na triagem" && isset($item->id_status) && $item->id_status == 8) : ?>
                                            <br><span class="label" style="background-color: #CD1F73">Falha na triagem</span>
                                        <?php endif; ?>
                                    <?php endif; ?>

                                    <?php if (empty($tag) && !empty($item->tag)) : ?>
                                        <br><span class="label label-info"><?php echo htmlspecialchars($item->tag, ENT_QUOTES); ?></span>
                                    <?php endif; ?>

                                    <?php if (!empty($item->dat_criacao)) : ?>
                                        <?php
                                        $dataCriada = date('d/m/y', strtotime($item->dat_criacao));
                                        ?>
                                        <br><strong>DC: <?php echo $dataCriada; ?></strong><br />
                                    <?php endif; ?>

                                    <?php if (!empty($item->data_modificacao)) : ?>
                                        <?php
                                        $dataModificada = date('d/m/y', strtotime($item->data_modificacao));
                                        ?>
                                        <strong>DM: <?php echo $dataModificada; ?></strong>
                                    <?php endif; ?>
                                </td>
                                <?php if ($has_owner) : ?>
                                    <td><?php echo $item->owner_codigo ?? '-'; ?> - <?php echo $item->owner_descricao ?? '-'; ?> - <?php echo $item->responsaveis_gestores_nomes ?? '-'; ?></td>
                                <?php else : ?>
                                    <td><?php echo $item->ncm ?? '-'; ?></td>
                                <?php endif; ?>
                                <td><?php echo $item->estabelecimento ?? '-'; ?></td>
                                <td><?php echo $item->peso ?? '-'; ?></td>
                                <td><?php echo $item->prioridade ?? 'Normal'; ?></td>
                                <td>
                                    <?php
                                    $tempo_restante = $item->tempo_restante ?? 0;
                                    echo number_format($tempo_restante, 1, ',', '');
                                    ?>
                                </td>
                                <td>
                                    <?php
                                    $horas_sla_prioridade = $item->horas_sla_prioridade ?? 1;
                                    $tempo_consumido_total = ($item->tempo_consumido_becomex ?? 0) + ($item->tempo_ultimo_status_becomex ?? 0);

                                    // Evitar divisão por zero
                                    if ($horas_sla_prioridade == 0) {
                                        $horas_sla_prioridade = 1;
                                    }

                                    $percentual_consumido = ($tempo_consumido_total / $horas_sla_prioridade) * 100;

                                    if ($percentual_consumido <= 75) {
                                        echo '<i class="fa fa-circle text-success" data-toggle="tooltip" data-placement="top" title="SLA em dia (' . number_format($percentual_consumido, 1) . '%)"></i>';
                                    } elseif ($percentual_consumido < 100) {
                                        echo '<i class="fa fa-exclamation-triangle text-warning" data-toggle="tooltip" data-placement="top" title="75% do SLA (' . number_format($percentual_consumido, 1) . '%)"></i>';
                                    } else {
                                        echo '<i class="fa fa-times-circle text-danger" data-toggle="tooltip" data-placement="top" title="SLA em atraso (' . number_format($percentual_consumido, 1) . '%)"></i>';
                                    }
                                    ?>
                                </td>
                                <td><?php echo $item->descricao ?? 'N/A'; ?></td>
                            </tr>

                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php elseif (empty($itens) && $filtered) : ?>
                <div class="row" style="margin-bottom: 10px;">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="result-container d-flex p-2">
                                <div class="result-container-title font-weight-bold col-md-7" style="margin: auto 0;">Resultado:

                                    <span class="result-container-badge badge badge-pill badge-primary"><?php echo $total_rows; ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <table class="table table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th></th>
                                    <th>Part Number</th>
                                    <?php if ($has_owner) : ?>
                                        <th>Owner</th>
                                    <?php else : ?>
                                        <th>NCM</th>
                                    <?php endif; ?>
                                    <th>Estab.</th>
                                    <th>Peso</th>
                                    <th>Prior.</th>
                                    <th>SLA Hrs restantes</th>
                                    <th>Farol</th>
                                    <th>Descrição</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td colspan="9" class="text-center">
                                        <div class="alert alert-info">
                                            <i class="fa fa-info-circle"></i> Nenhum item encontrado com os filtros aplicados.
                                            <br><small>Tente ajustar ou <a href="<?php echo site_url('atribuir_grupo?reset_filters=1'); ?>">remover todos os filtros</a>.</small>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    <?php endif; ?>
                    <!-- Paginação -->
                    <?php if (!empty($pagination)) : ?>
                        <div class="controls">
                            <div class="pull-right">
                                <?php echo $pagination; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Campos hidden necessários para compatibilidade -->
            <input type="hidden" id="integracao_simplus" value="<?php echo isset($integracao_simplus) && $integracao_simplus == true ? 1 : '' ?>" />
            <input type="hidden" id="desblquear_itens" value="<?php echo customer_has_role('desblquear_itens', sess_user_id()) ? 1 : 0; ?>" />

            <script src="<?= base_url('assets/js/filter_badges.js') ?>"></script>
            <script src="<?= base_url('assets/js/filter_modal_integration.js') ?>"></script>
            <script src="<?= base_url('assets/js/filter_modal_fields.js') ?>"></script>
            <script src="<?= base_url('assets/js/index_new_export.js') ?>"></script>
            <script type="text/javascript" src="<?php echo base_url('assets/js/atribuir_grupos/atribuir_grupos.js?version=' . config_item('assets_version')); ?>"></script>
            <script type="text/javascript" src="<?php echo base_url('assets/js/atribuir_grupos/atribuir_grupos_new.js?version=' . config_item('assets_version')); ?>"></script>

            <script type="text/javascript">
                $(document).ready(function() {
                    // Aguardar um pouco para garantir que todos os componentes estejam carregados
                    setTimeout(function() {
                        // Inicializar FilterModal
                        if (typeof FilterModal !== 'undefined') {
                            FilterModal.init();
                        }

                        // Verificar se o modal existe
                        if ($('#filterModal').length === 0) {
                            console.error('Modal #filterModal não encontrado no DOM');
                        }
                    }, 100);

                    // Inicializar o novo módulo AtribuirGruposNew
                    if (typeof AtribuirGruposNew !== 'undefined') {
                        AtribuirGruposNew.init(
                            '<?php echo site_url(); ?>',
                            <?php echo json_encode($id_usuario_logado); ?>,
                            <?php echo json_encode($usuario_desbloqueador); ?>
                        );
                    }
                });
            </script>

            <!-- Loading Overlay (similar ao mestre_itens) -->
            <div id="loading-overlay" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.5); z-index: 9999; display: none;">
                <div id="loading-message" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 24px;">
                    Carregando...
                </div>
            </div>