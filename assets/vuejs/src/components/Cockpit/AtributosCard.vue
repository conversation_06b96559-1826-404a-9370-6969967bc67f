<template>
  <div :class="'col-sm-' + colSize">
    <div class="card">
      <div class="card-header">
        <h5 class="card-title">{{ cardTitle }}</h5>
          <div class="btn-group float-right"> 
            <div class="btn-group" style="margin-right: 10px;">

              <button
                  v-if="hasConsult &&  this.permissoes['exportar_consultores']"
                  @click="downloadReportConsult()"
                  class="btn btn-primary btn-visualization"
                  style="margin-right: 10px;"
                >
                  <i
                    :class="{
                      'glyphicon glyphicon-cloud-download icon-size': true,
                      'glyphicon-stats': visualization == 'graphic',
                      'glyphicon-list': visualization == 'table',
                    }"
                  ></i>
              </button>
 

              <button
                v-if="!hasConsult  && this.permissoes['exportar_atributos_cockpit']"
                type="button"
                class="btn btn-primary btn-visualization dropdown-toggle"
                data-toggle="dropdown"
                aria-haspopup="true"
                aria-expanded="false"
              >
                <i
                  :class="{
                    'glyphicon glyphicon-cloud-download icon-size': true,
                    'glyphicon-stats': visualization == 'graphic',
                    'glyphicon-list': visualization == 'table',
                  }"
                ></i>
                <span class="caret"></span>
              </button>
              <ul v-if="!hasConsult" class="dropdown-menu">
                <li>
                  <a href="#" @click.prevent="downloadReport('Sintetico')">Relatório Sintético</a>
                </li>
                <li>
                  <a href="#" @click.prevent="downloadReport('Detalhado')">Relatório Detalhado</a>
                </li>
              </ul>
            </div>

            <button
              @click="handleVisualization()"
              class="btn btn-primary btn-visualization"
            >
              <i
                :class="{
                  'glyphicon icon-visualization icon-size': true,
                  'glyphicon-stats': visualization == 'graphic',
                  'glyphicon-list': visualization == 'table',
                }"
              ></i>
            </button>
          </div>
      </div>

      <div :class="'card-body ' + (isLoading ? 'min-height-card-body' : '')">
        <loading :active.sync="isLoading" :is-full-page="fullPage"> </loading>
        <div :id="graphicKey" :class="graphicKey + '-graphic'"></div>

        <div :class="graphicKey + '-table'" style="display: none">
          <table class="table table-bordered" style="margin-bottom: 0px !important">
            <thead>
              <tr>
                <th>{{ tableTitle }}</th>
                <th>Quantidade</th>
              </tr>
            </thead>

            <tbody>
              <tr v-for="item in dados" :key="item.descricao">
                <td :class="{ 'font-weight-bold': item.descricao == 'Total' }">
                  {{ item.descricao }}
                </td>
                <td :class="{ 'font-weight-bold': item.descricao == 'Total' }">
                  {{ item.quantidade }}
                </td>
              </tr>
            </tbody>

            <tfoot v-if="cardTitle != 'Perguntas e Respostas'">
              <tr>
                <th>Total</th>
                <th>
                  {{ totalQuantidade }}
                </th>
              </tr>
            </tfoot>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from "axios";
import "vue-loading-overlay/dist/vue-loading.css";
import Loading from "vue-loading-overlay";

export default {
  data() {
    return {
      permissoes: [],
      dadosFormatadosColumnGraphic: [],
      visualization: "graphic",
      totalQuantidade: null,
      isLoading: false,
      fullPage: false,
      dados: null,
      dataEstabelecimento: null,
      dataResponsavel: null,
      dataEvento: null,
      dataSearch: null,
      dataStatusAttr: null,
      dataStatusClassificacao: null,
      dataStatusPreenchimento: null,
      dataPrioridade: null,
      dataNcmProposto: null,
      dataStatusIntegracao: null,
      dataOwner: null,
      dataObjetivo: null,
      status_mes:  [],
      status_ano:  [],
      status_consultor:  [],
      status_empresa:  []

    };
  },
  components: {
    Loading,
  },
  props: {
    updateComponent: {
      type: Number,
      required: false,
    },
    colSize: {
      type: String,
      required: true,
    },
    cardTitle: {
      type: String,
      required: true,
    },
    graphicType: {
      type: String,
      required: true,
    },
    graphicKey: {
      type: String,
      required: true,
    },
    graphicTitle: {
      type: String,
      required: true,
    },
    tableTitle: {
      type: String,
      required: true,
    },
    route: {
      type: String,
      required: true,
    },
    timeout: {
      type: Number,
      required: false,
    },
    hasDisplayGraphic: {
      type: Boolean,
      required: true,
    },
    hasConsult: {
      type: Boolean,
      required: true,
    },
  },
  methods: {
    getPermissao() {
            this.$http.get('/cockpit/getPermissoes', {
            }).then(({data}) => {
                console.log(data);
                this.permissoes = data;
            })
    },

    async downloadReport(tipo) {       
        try {
         this.isLoading = true;
            document.getElementById("btn-search").disabled = true;
            const response = await axios.post(
                `/cockpit/getStatusAtributos`, 
                {
                  export: tipo,
                  evento: this.dataEvento,
                  estabelecimento: this.dataEstabelecimento,
                  responsaveis: this.dataResponsavel,
                  search: this.dataSearch,
                  status_attr: this.dataStatusAttr,
                  status_classificacao: this.dataStatusClassificacao,
                  status_preenchimento: this.dataStatusPreenchimento,
                  prioridade: this.dataPrioridade,
                  ncm_proposto: this.dataNcmProposto,
                  status_integracao: this.dataStatusIntegracao,
                  owner: this.dataOwner,
                  objetivo: this.dataObjetivo,
                },
                {
                  responseType: 'blob'  
                }
            );
               
            this.isLoading = false;
            document.getElementById("btn-search").disabled = false;
            const url = window.URL.createObjectURL(new Blob([response.data], { type: "application/vnd.ms-excel" }));
            const link = document.createElement('a');
            link.href = url;
            link.setAttribute('download', 'relatorio.xlsx'); 
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

        } catch (error) {
            console.error("Erro na requisição:", error);
        } 
    },
    async downloadReportConsult(tipo) {       
        try {
         this.isLoading = true;
            document.getElementById("btn-search").disabled = true;
            const response = await axios.post(
                `/cockpit/downloadReportConsult`, 
                {
                  status_mes:  this.status_mes,
                  status_ano:  this.status_ano,
                  status_consultor:  this.status_consultor,
                  status_empresa:  this.status_empresa
                },
                {
                  responseType: 'blob'  
                }
            );
               
            this.isLoading = false;
            document.getElementById("btn-search").disabled = false;
            const url = window.URL.createObjectURL(new Blob([response.data], { type: "application/vnd.ms-excel" }));
            const link = document.createElement('a');
            link.href = url;
            link.setAttribute('download', 'ClassMensalConsultores.xlsx'); 
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

        } catch (error) {
            console.error("Erro na requisição:", error);
        } 
    },
    
    handleVisualization() {
      if (this.visualization == "graphic") {
        return this.turnIntoList();
      }

      this.turnIntoGraphic();
    },
    turnIntoGraphic() {
      $(`.${this.graphicKey}-table`).hide(200);
      $(`.${this.graphicKey}-graphic`).show(200);

      this.visualization = "graphic";
    },
    turnIntoList() {
      $(`.${this.graphicKey}-graphic`).hide(200);
      $(`.${this.graphicKey}-table`).show(200);

      this.visualization = "table";
    },
    formatColumnData(data) {
      this.totalQuantidade = 0;

      this.dadosFormatadosColumnGraphic = this.dados.map((data) => {
        this.totalQuantidade += data.quantidade;

        return {
          name: data.descricao,
          y: data.quantidade,
        };
      });
    },
    handleGraphicType() {
      switch (this.graphicType) {
        case "pie":
          this.setPieGraphic();
          break;
        case "bar":
          this.setMultipleBarGraphic();
          break;
        case "column":
          this.formatColumnData();
          this.setColumnGraphic();
          break;
        default:
          this.setMultipleColumnGraphic();
      }
    },
    async handleGraphicData(evento, estabelecimento, responsavel, search, status_attr, status_classificacao, status_preenchimento, prioridade, ncm_proposto, status_integracao, owner, objetivo) {
      this.isLoading = true;

      await this.$http
        .get(`cockpit/${this.route}`, {
          params: {
            evento: evento,
            estabelecimento: estabelecimento,
            responsaveis: responsavel,
            search: search,
            status_attr: status_attr,
            status_classificacao: status_classificacao,
            status_preenchimento: status_preenchimento,
            prioridade: prioridade,
            ncm_proposto: ncm_proposto,
            status_integracao: status_integracao,
            owner: owner,
            objetivo: objetivo,
          },
        })
        .then(({ data }) => {
          this.dados = data.data;

          this.handleGraphicType();

          this.isLoading = false;
          document.getElementById("btn-search").disabled = false;
        });
    },
    async handleGraphicDataConsult(status_mes, status_ano, status_consultor, status_empresa) {
      this.isLoading = true;

      await this.$http
        .get(`cockpit/${this.route}`, {
          params: {
            status_mes: status_mes,
            status_ano: status_ano,
            status_consultor: status_consultor,
            status_empresa: status_empresa
          },
        })
        .then(({ data }) => {
          this.dados = data.data;

          this.handleGraphicType();

          this.isLoading = false;
          document.getElementById("btn-search-consult").disabled = false;
        });
    },
    async updateMyselfAttr(evento, estabelecimento, responsavel, search, status_attr, status_classificacao, status_preenchimento, prioridade, ncm_proposto, status_integracao, owner, 
    objetivo) {

      this.dataEstabelecimento = estabelecimento;
      this.dataResponsavel = responsavel;
      this.dataEvento = evento;
      this.dataSearch = search;
      this.dataStatusAttr = status_attr;
      this.dataStatusClassificacao = status_classificacao;
      this.dataStatusPreenchimento = status_preenchimento;
      this.dataPrioridade = prioridade;
      this.dataNcmProposto = ncm_proposto;
      this.dataStatusIntegracao = status_integracao;
      this.dataOwner = owner;
      this.dataObjetivo = objetivo;
      
      await this.handleGraphicData(evento, estabelecimento, responsavel, search, status_attr, status_classificacao, status_preenchimento, prioridade, ncm_proposto, status_integracao, owner, 
      objetivo);
    },
    async updateMyselfConsult(status_mes, status_ano, status_consultor, status_empresa) {
      this.status_mes = status_mes;
      this.status_ano = status_ano;
      this.status_consultor = status_consultor;
      this.status_empresa = status_empresa;
      
      await this.handleGraphicDataConsult(status_mes, status_ano, status_consultor, status_empresa);
    },
    setMultipleColumnGraphic() {
      let self = this;

      $(`#${this.graphicKey}`).highcharts({
        chart: {
          /* Tipo de Gráfico **/
          type: "column",
        },
        title: {
          /* Título do Gráfico **/
          text: self.graphicTitle,
        },
        xAxis: {
          /* Informações abaixo do eixo X - categories[0] = coluna 1 **/
          categories: ["Jan", "Aug", "Sep", "Dec"],
          crosshair: true,
        },
        yAxis: {
          /* Valor base do eixo Y **/
          min: 0,
          /* Título posicionado ao lado do eixo Y **/
          title: {
            text: "Rainfall (mm)",
          },
        },
        tooltip: {
          /* Título do Tooltip **/
          headerFormat:
            '<span style="font-size:15px; font-weight: bold; padding: 4px; display: flex; justify-content: center;">{point.key}</span><table>',
          /* Subtítulos do Tooltip **/
          pointFormat:
            '<tr><td style="color:{series.color}; padding:4px; font-weight: bold;">{series.name}: </td>' +
            '<td style="padding:0"><b>{point.y:.1f} mm</b></td></tr>',
          /* Formato do Tooltip **/
          footerFormat: "</table>",
          /* Tooltip com as informações das 4 Colunas **/
          shared: true,
          useHTML: true,
        },
        plotOptions: {
          column: {
            /* Largura da Coluna **/
            pointPadding: 0.2,
            borderWidth: 0,
          },
        },
        /* Créditos para a highcharts abaixo do Gráfico **/
        credits: {
          enabled: false,
        },
        /* Dados do Gráfico **/
        series: [
          {
            name: "Tokyo",
            data: [216.4, 194.1, 95.6, 54.4],
          },
          {
            name: "New York",
            data: [91.2, 83.5, 106.6, 92.3],
          },
          {
            name: "London",
            data: [52.4, 65.2, 59.3, 51.2],
          },
          {
            name: "Berlin",
            data: [47.6, 39.1, 46.8, 51.1],
          },
        ],
      });
    },
    setMultipleBarGraphic() {
      let self = this;

      $(`#${this.graphicKey}`).highcharts({
        /* Tipo de Gráfico **/
        chart: {
          type: "bar",
        },
        /* Título do Gráfico **/
        title: {
          text: self.graphicTitle,
        },
        xAxis: {
          /* Informações ao lado esquerdo do eixo Y - categories[0] = coluna horizontal 1 **/
          categories: ["Africa", "America", "Asia", "Europe"],
          /* Título posicionado ao lado do eixo Y **/
          title: {
            text: "null",
          },
        },
        yAxis: {
          /* Valor base do eixo X **/
          min: 0,
          /* Título posicionado abaixo do eixo X **/
          title: {
            text: "Population (millions)",
            align: "high",
          },
          labels: {
            overflow: "justify",
          },
        },
        /* Sufixo dos valores apresentados dentro do Tooltip **/
        tooltip: {
          valueSuffix: " millions",
        },
        plotOptions: {
          bar: {
            /* Valor da coluna apresentado à direita da Coluna **/
            dataLabels: {
              enabled: true,
            },
          },
        },
        /* Configurações da legenda do Gráfico **/
        legend: {
          layout: "vertical",
          align: "right",
          verticalAlign: "middle",
          x: -10,
          y: 90,
          floating: true,
          borderWidth: 0.5,
          backgroundColor: "#FFFFFF",
          shadow: true,
        },
        /* Créditos para a highcharts abaixo do Gráfico **/
        credits: {
          enabled: false,
        },
        /* Dados do Gráfico **/
        /* 
                    Para um gráfico com várias colunas em diferentes x's, continua o mesmo.
                    Para um gráfico com únicas colunas em diferentes x's, deixar somente 'Year 1800' e editar a partir disso.
                **/
        series: [
          {
            name: "Year 1800",
            data: [31, 635, 203, 2],
          },
          {
            name: "Year 1900",
            data: [156, 947, 408, 6],
          },
          {
            name: "Year 2000",
            data: [841, 3714, 727, 31],
          },
          {
            name: "Year 2016",
            data: [1001, 4436, 738, 40],
          },
        ],
      });
    },
    setColumnGraphic() {
      let self = this;

      $(`#${this.graphicKey}`).highcharts({
        /* Tipo de Gráfico **/
        chart: {
          type: "column",
        },
        /* Título do Gráfico **/
        title: {
          text: self.graphicTitle,
        },
        accessibility: {
          announceNewData: {
            enabled: true,
          },
        },
        /* Permite que as colunas recebam o identificador dentro de series:[data:[{name:'identificador'}]] **/
        xAxis: {
          type: "category",
        },
        /* Título posicionado ao lado do eixo Y **/
        yAxis: {
          title: {
            text: null,
          },
        },
        /* Configurações da legenda do Gráfico **/
        legend: {
          enabled: false,
        },
        plotOptions: {
          series: {
            borderWidth: 0,
            /* Valor da coluna apresentado acima da Coluna **/
            dataLabels: {
              enabled: true,
            },
          },
        },
        /* Créditos para a highcharts abaixo do Gráfico **/
        credits: {
          enabled: false,
        },
        //{series.name}
        tooltip: {
          headerFormat: '<span style="font-size:11px">{point.key}</span><br>',
           pointFormat: '<b>{point.y}</b><br/>'
        },
        series: [
          {
            /* Título dos dados do eixo X - Aparece em todos os Tooltips **/
            name: self.cardTitle,
            /* Diferentes cores para as Colunas **/
            colorByPoint: true,
            /* Dados do Gráfico **/
            data: this.dadosFormatadosColumnGraphic,
          },
        ],
      });
    },
    setPieGraphic() {
      let self = this;

      $(`#${this.graphicKey}`).highcharts({
        /* Tipo de Gráfico **/
        chart: {
          type: "pie",
          options3d: {
            enabled: true,
            alpha: 45,
            beta: 0,
          },
        },
        /* Título do Gráfico **/
        title: {
          text: self.graphicTitle,
        },
        accessibility: {
          point: {
            valueSuffix: "%",
          },
        },
        /* Configurações do Tooltip do Gráfico **/
        tooltip: {
          pointFormat: "{series.name}: <b>{point.percentage:.1f}%</b>",
        },
        plotOptions: {
          pie: {
            allowPointSelect: true,
            cursor: "pointer",
            depth: 35,
            /* Valor da torta apresentado ao lado da Torta **/
            dataLabels: {
              enabled: true,
              format: "{point.name}",
            },
          },
        },
        /* Créditos para a highcharts abaixo do Gráfico **/
        credits: {
          enabled: false,
        },
        series: [
          {
            type: "pie",
            /* Título da informação Apresentada - Aparece em todos os tooltips com o valor ao Lado **/
            name: "Browser share",
            /* Dados do Gráfico - O TOTAL DEVE ATINGIR 100% **/
            data: [
              /* Título da fatia de Torta | Valor da fatia de Torta **/
              ["Firefox", 45.0],
              ["IE", 26.8],
              /* Título da fatia de torta inicialmente Selecionada | Valor da fatia de torta inicialmente Selecionada **/
              {
                name: "Chrome",
                y: 12.8,
                sliced: true,
                selected: true,
              },
              ["Safari", 8.5],
              ["Opera", 6.2],
              ["Others", 0.7],
            ],
          },
        ],
      });
    },
  },
  async mounted() {

    let self = this;

    if (self.hasDisplayGraphic) {
      if (self.timeout > 0) {
        setTimeout(() => self.handleGraphicData(), self.timeout);
      } else {
        await this.handleGraphicData();
      }
    }

    this.getPermissao();
  },
};
</script>

<style type="text/css">
.d-none {
  display: none !important;
}

/* Transition Table Graphic */

.transition {
  transition: opacity 0.15s;
}

.opacity-off {
  opacity: 0;
}

.opacity-on {
  opacity: 1;
}

/* Buttons and Icons  */

.btn-visualization {
  outline: none !important;
  align-items: center;
  display: flex;
}

.icon-visualization.glyphicon {
  font-size: 16px;
}

.font-weight-bold {
  font-weight: bold;
}

.icon-size {
  font-size: 20px; /* Ajuste o tamanho conforme necessário */
  height: 20px;
  width: 20px;
  line-height: 20px;
}

.icon-download {
  background-color: green;
  padding: 5px; /* Ajuste para centralizar o ícone no fundo */
  border-radius: 3px; /* Para suavizar os cantos */
  margin-right: 2px;
  color: white; /* Para o ícone ficar visível sobre o fundo verde */
}


</style>
