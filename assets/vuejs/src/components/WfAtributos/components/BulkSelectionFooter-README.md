# BulkSelectionFooter Component

Componente Vue reutilizável para exibir um footer de seleção em massa com ações personalizáveis.

## Props

| Prop | Tipo | Obrigatório | Padrão | Descrição |
|------|------|-------------|--------|-----------|
| `selectedItems` | Array | Sim | `[]` | Array de itens selecionados (IDs ou objetos) |
| `totalSelectedItems` | Number | Sim | `0` | Total de itens selecionados (calculado) |
| `totalAvailableItems` | Number | Sim | `0` | Total de itens disponíveis para seleção |
| `totalAvailableCount` | Number | Não | `0` | Total de registros disponíveis (ex: NCMs) |
| `canShow` | Boolean | Não | `true` | Se pode mostrar o footer |
| `isLoading` | Boolean | Não | `false` | Se está carregando dados |
| `hasLoadedAll` | Boolean | Não | `false` | Se já carregou todos os dados |
| `showSelectAllAction` | Boolean | Não | `true` | Se deve mostrar a ação "Selecionar todos" |
| `showCustomActions` | Boolean | Não | `false` | Se deve mostrar ações customizadas |
| `itemLabel` | String | Não | `'item'` | Label para os itens (ex: "item", "produto") |
| `countLabel` | String | Não | `'registros'` | Label para a contagem (ex: "NCMs", "categorias") |
| `offsetMainFooter` | Boolean | Não | `false` | Se deve ajustar posição quando footer principal está visível |

## Eventos

| Evento | Descrição |
|--------|-----------|
| `@selectAll` | Emitido quando o usuário clica em "Selecionar todos" |
| `@clearSelection` | Emitido quando o usuário clica em "Limpar seleção" |

## Slots

| Slot | Descrição |
|------|-----------|
| `customActions` | Slot para ações customizadas adicionais |

## Exemplo de Uso no WfAgrupamentoNcm.vue

```vue
<template>
  <!-- Outros elementos do componente -->
  
  <BulkSelectionFooter
    :selectedItems="selectedNcms"
    :totalSelectedItems="totalSelectedItems"
    :totalAvailableItems="totalAvailableItems"
    :totalAvailableCount="hasLoadedAllNcms ? allNcmsData.length : data.length"
    :canShow="canHomologar"
    :isLoading="isLoadingAllNcms"
    :hasLoadedAll="hasLoadedAllNcms"
    :offsetMainFooter="offsetMainFooter"
    itemLabel="item"
    countLabel="NCMs"
    @selectAll="selectAllItems"
    @clearSelection="clearSelection"
  />
</template>

<script>
import BulkSelectionFooter from './components/BulkSelectionFooter.vue';

export default {
  components: {
    BulkSelectionFooter,
  },
  // ... resto do componente
};
</script>
```

## Exemplo de Uso no WfTabelaAtributos.vue

```vue
<template>
  <!-- Tabela de atributos existente -->
  
  <BulkSelectionFooter
    :selectedItems="selectedItems"
    :totalSelectedItems="selectedItems.length"
    :totalAvailableItems="dadosAtributos.itens.length"
    :canShow="selectedItems.length > 0"
    :showSelectAllAction="false"
    :showCustomActions="true"
    itemLabel="item"
    countLabel="itens"
    @clearSelection="clearItemSelection"
  >
    <template #customActions>
      <a @click="openModalHomologSelecteds(false, true)" class="footer-action-link">
        Homologar selecionados
      </a>
      <span class="separator">|</span>
      <a @click="openModalMoveSelecteds" class="footer-action-link">
        Alterar Status
      </a>
    </template>
  </BulkSelectionFooter>
</template>

<script>
import BulkSelectionFooter from './components/BulkSelectionFooter.vue';

export default {
  components: {
    BulkSelectionFooter,
  },
  methods: {
    clearItemSelection() {
      this.selectedItems = [];
      this.selectAll = false;
    },
    // ... outros métodos
  }
};
</script>
```

## Exemplo com Ações Customizadas Avançadas

```vue
<template>
  <BulkSelectionFooter
    :selectedItems="selectedItems"
    :totalSelectedItems="totalSelectedItems"
    :totalAvailableItems="totalAvailableItems"
    :showCustomActions="true"
    @selectAll="selectAllItems"
    @clearSelection="clearSelection"
  >
    <template #customActions>
      <a @click="exportSelected" class="footer-action-link">
        Exportar
      </a>
      <span class="separator">|</span>
      <a @click="deleteSelected" class="footer-action-link text-danger">
        Excluir
      </a>
      <span class="separator">|</span>
      <button @click="bulkEdit" class="btn btn-sm btn-secondary">
        Editar em Lote
      </button>
    </template>
  </BulkSelectionFooter>
</template>
```

## Estilos CSS

O componente já inclui todos os estilos necessários. Os estilos são scoped e incluem:

- Posicionamento fixo no rodapé
- Responsividade para dispositivos móveis
- Estados de loading
- Ajuste automático quando há footer principal
- Transições suaves

## Funcionalidades

1. **Exibição Condicional**: Só aparece quando há itens selecionados
2. **Contadores Dinâmicos**: Mostra quantos itens estão selecionados e quantos estão disponíveis
3. **Ações Padrão**: "Selecionar todos" e "Limpar seleção"
4. **Ações Customizadas**: Slot para adicionar ações específicas do contexto
5. **Estados de Loading**: Feedback visual durante carregamento
6. **Responsividade**: Adapta-se a diferentes tamanhos de tela
7. **Acessibilidade**: Estrutura semântica e navegação por teclado

## Integração com Sistemas Existentes

O componente foi projetado para ser facilmente integrado em sistemas existentes:

- **Props flexíveis**: Permite configurar labels e comportamentos
- **Eventos simples**: Emite eventos que podem ser capturados pelos componentes pai
- **Slots customizáveis**: Permite adicionar ações específicas sem modificar o componente
- **Estilos isolados**: Não interfere com estilos existentes
- **Performance otimizada**: Renderização condicional e props reativas
