<template>
  <div
    class="modal fade"
    id="ModalMoveSelecteds"
    tabindex="-1"
    aria-hidden="true"
  >
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content modal-content-custom">
        <div class="header">
          <h4 class="modal-title text-title" id="exampleModalLabel">
            Alterar Status em massa - {{ listItens.length }} itens selecionados
          </h4>
          <button
            type="button"
            class="close"
            @click="closeModal"
            aria-label="Close"
          >
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body body">
          <div class="list">
            <table class="table-movement">
              <thead class="table-movement-thead">
                <tr>
                  <th style="width: 20%">Part number</th>
                  <th>Descrição</th>
                </tr>
              </thead>
              <tbody class="table-movement-tbody">
                <tr
                  v-for="(item, rowIndex) in listItens"
                  :key="item.part_number"
                  :class="{ 'odd-item': rowIndex % 2 === 0 }"
                >
                  <td style="width: 20%">
                    <strong>
                      {{ item.part_number }}
                    </strong>
                  </td>
                  <td>{{ item.descricao }}</td>
                </tr>
              </tbody>
            </table>
          </div>
          <div class="form-move-selected">
            <div class="move-selected-input">
              <label for="select-status">
                Selecione o status que deseja enviar os itens
              </label>
              <select
                v-model="status"
                data-live-search="true"
                title="SELECIONE"
                class="form-control"
                id="select-status"
              >
                <!-- selectpicker -->
                <option
                  v-for="item in listStatus"
                  :key="item.id"
                  :value="item.id"
                >
                  {{ item.status }}
                </option>
              </select>
            </div>
          </div>
        </div>
        <div class="footer">
          <div></div>
          <div>
            <button type="button" class="btn btn-default" @click="closeModal">
              Cancelar
            </button>
            <button
              type="button"
              class="btn btn-success"
              :disabled="!status"
              @click="submit()"
            >
              <i class="glyphicon glyphicon-ok icon"></i> Alterar Status
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios';
import 'vue-loading-overlay/dist/vue-loading.css';
import Loading from 'vue-loading-overlay';

export default {
  name: 'ModalMoveSelecteds',
  components: {
    Loading,
  },
  props: {
    ncmId: {
      required: true,
      default: Array,
    },
    selectedItems: {
      required: true,
      default: Array,
    }
  },
  data() {
    return {
      listItens: [],
      listStatus: [
          { id: 2, status: 'Análise de atributos - Fiscal' },
          { id: 3, status: 'Preenchimento/Validação Engenharia' },
          { id: 5, status: 'Em revisão' }
        ],
      status: '',
    };
  },
  watch: {
    listStatus: {
      handler(newVal, oldVal) {
        // console.log('Items array changed:', newVal);
        this.$nextTick(() => {
          $('.selectpicker').selectpicker();
        });
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {
    $('.selectpicker').selectpicker();
    $('.selectpicker').selectpicker('refresh');
    // console.log('ModalMoveSelecteds', this.ncmId);
    this.loadItems();
    this.loadStatus();
  },
  methods: {
    async loadItems() {
      try {
        const response = await axios.post(
          'atributos/ajax_get_lista_itens_status',
          {
            ncm: this.ncmId,
            idItem: this.selectedItems
          }
        );
        this.listItens = response.data.data;
        console.log('response items', response);
        // teste
        // const listaStatus = getNextStatuses('analise_de_atributos_fiscal');
        // console.log('listaStatus', listaStatus);
      } catch (error) {}
    },
    async loadStatus() {
      try {
        // const response = await axios.get('atributos/ajax_get_all_status');
        // console.log('response status', response.data.data);

        // this.listStatus = response.data.data;

        // $('.selectpicker')
        //   .selectpicker('refresh')
        //   .empty()
        //   .append(response.data.data)
        //   .selectpicker('refresh')
        //   .trigger('change');
      } catch (error) {}
    },
    async submit() {
      try {
        const data = {
          ncm: this.ncmId,
          status: this.status,
          idItem: this.listItens
        };
        const response = await axios.post('atributos/ajax_set_status', data);
          
          if (response.status === 200) {

            swal('Sucesso', response.data.msg, 'success');
            this.closeModal();
          } else {
            swal('Erros', response.data.msg, 'warning');
              console.log('Requisição concluída com status não 200:', response.status);
          }
        // console.log('submit res', response);
      } catch (error) {}
    },
    closeModal() {
      this.$emit('closeModal');
    },
  },
};
</script>

<style lang="scss" scoped>
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #e5e5e5;
}
.body {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  /* padding: 25px 15px; */
}

.icon {
  font-size: smaller;
  margin-right: 10px;
}

.text-title {
  color: #8d9296;
  font-weight: 600;
}

.text-body {
  font-size: 20px;
  font-weight: 500;
  color: #4d545b;
  margin: 0;
}

.footer {
  border-top: 1px solid #e5e5e5;
  padding: 15px;
  display: flex;
  justify-content: space-between;
}

.list,
.form-move-selected {
  display: flex;
  width: 100%;
  margin: 0 0 10px;
}

.table-movement {
  width: 100%;
  border: 1px solid;
  border-radius: 5px;
}

.table-movement td {
  background: white;
}

.table-movement th,
.table-movement td {
  padding: 10px;
}

.table-movement-thead {
  color: white;
  background-color: #337ab7;
}
.table-movement-tbody {
  display: block;
  max-height: 200px;
  overflow-y: auto;
}

.table-movement-thead,
.table-movement-tbody tr {
  display: table;
  width: 100%;
  table-layout: fixed;
}

.odd-item td {
  background: #e2e3e5;
}

.move-selected-input {
  width: 100%;
}
</style>
