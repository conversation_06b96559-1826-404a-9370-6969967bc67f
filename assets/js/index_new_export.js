/**
 * Módulo para funcionalidades de exportação na nova tela de Dados Técnicos
 * Responsável por gerenciar exportações Padrão e Multi Países
 *
 * Utiliza os novos métodos otimizados (log_xls_new/xls_multipaises_new) que usam apply_default_filters
 * ou faz fallback para os métodos originais se necessário.
 */

var IndexNewExport = {
  base_url: typeof base_url !== "undefined" ? base_url : "/",
  isExporting: false,
  exportTimer: null,
  useNewMethods: true, // Flag para controlar uso de métodos novos

  init: function () {
    this.bindEvents();
    this.setupForm();
    this.checkMethodsAvailability();
  },

  checkMethodsAvailability: function () {
    var self = this;

    // Fazer uma verificação simples para ver se os novos métodos estão disponíveis
    // Se houver erro 404, usar os métodos antigos
    $.ajax({
      url: this.base_url + "atribuir_grupo/ajax_xls_log_status_new",
      method: "POST",
      timeout: 3000,
      success: function () {
        self.useNewMethods = true;
        console.log("Usando novos métodos de exportação otimizados");
      },
      error: function () {
        self.useNewMethods = false;
        console.log("Fallback para métodos de exportação originais");
      },
    });
  },

  bindEvents: function () {
    var self = this;

    // Bind para exportação padrão
    $(document).on("click", "#send_generate_log", function (e) {
      e.preventDefault();
      self.startExport("padrao");
    });

    // Bind para exportação multi países
    $(document).on("click", "#send_multipaises_log", function (e) {
      e.preventDefault();
      self.startExport("multipaises");
    });
  },

  setupForm: function () {
    // Verificar se o formulário já existe, senão criar
    if ($("#export_form").length === 0) {
      var form = $("<form></form>").attr({
        id: "export_form",
        method: "GET",
        style: "display: none;",
      });

      // Adicionar token para controle de download
      var token = $("<input>").attr({
        type: "hidden",
        name: "download_token",
        value: this.generateToken(),
      });

      form.append(token);
      $("body").append(form);
    }
  },

  startExport: function (type) {
    if (this.isExporting) {
      return;
    }

    this.isExporting = true;

    // Determinar o endpoint baseado no tipo e disponibilidade dos métodos
    var endpoint = "";
    var buttonSelector = "";

    if (type === "padrao") {
      endpoint = this.useNewMethods
        ? this.base_url + "atribuir_grupo/log_xls_new"
        : this.base_url + "atribuir_grupo/log_xls";
      buttonSelector = "#send_generate_log";
    } else if (type === "multipaises") {
      endpoint = this.useNewMethods
        ? this.base_url + "atribuir_grupo/xls_multipaises_new"
        : this.base_url + "atribuir_grupo/xls_multipaises";
      buttonSelector = "#send_multipaises_log";
    }

    var $button = $(buttonSelector);

    // Aplicar estado de loading
    this.setLoadingState($button, true);

    // Coletar filtros ativos
    var filters = this.collectActiveFilters();

    // Preparar formulário com filtros
    this.prepareExportForm(filters, endpoint);

    // Submeter formulário
    $("#export_form").submit();

    // Aguardar processo de exportação
    this.waitForExportCompletion(type, $button);
  },

  collectActiveFilters: function () {
    var filters = {};

    // Coletar filtros do FilterModalIntegration se disponível
    if (
      typeof FilterModalIntegration !== "undefined" &&
      FilterModalIntegration.getSavedFilters
    ) {
      var savedFilters = FilterModalIntegration.getSavedFilters();

      // Para métodos novos, usar nomes diretos; para antigos, mapear conforme esperado
      if (this.useNewMethods) {
        // Métodos novos usam apply_default_filters que espera nomes como no modal
        Object.keys(savedFilters).forEach(function (key) {
          var value = savedFilters[key];
          if (value && (Array.isArray(value) ? value.length > 0 : true)) {
            if (Array.isArray(value)) {
              const nonEmptyValues = value.filter(
                (v) => v && v !== "" && v !== "-1"
              );
              if (nonEmptyValues.length > 0) {
                filters[key] = nonEmptyValues;
              }
            } else if (value !== "" && value !== null && value !== "-1") {
              filters[key] = value;
            }
          }
        });
      } else {
        // Métodos antigos esperam mapeamento específico
        if (
          savedFilters.pacotes_eventos &&
          Array.isArray(savedFilters.pacotes_eventos) &&
          savedFilters.pacotes_eventos.length > 0
        ) {
          filters.evento = savedFilters.pacotes_eventos;
        }

        if (
          savedFilters.sistema_origem &&
          Array.isArray(savedFilters.sistema_origem) &&
          savedFilters.sistema_origem.length > 0
        ) {
          filters.sistemas_origens = savedFilters.sistema_origem;
        }

        if (
          savedFilters.owner &&
          Array.isArray(savedFilters.owner) &&
          savedFilters.owner.length > 0
        ) {
          filters.owner = savedFilters.owner;
        }

        if (
          savedFilters.prioridade &&
          Array.isArray(savedFilters.prioridade) &&
          savedFilters.prioridade.length > 0
        ) {
          filters.prioridade = savedFilters.prioridade;
        }

        if (
          savedFilters.atribuido_para &&
          Array.isArray(savedFilters.atribuido_para) &&
          savedFilters.atribuido_para.length > 0
        ) {
          filters.atribuido_para = savedFilters.atribuido_para;
        }

        if (
          savedFilters.status_classificacao_fiscal &&
          Array.isArray(savedFilters.status_classificacao_fiscal) &&
          savedFilters.status_classificacao_fiscal.length > 0
        ) {
          filters.status = savedFilters.status_classificacao_fiscal;
        }

        if (savedFilters.triagem_diana_falha) {
          filters.triagem_diana_falha = savedFilters.triagem_diana_falha;
        }
      }
    }

    // Coletar campo de pesquisa principal
    var mainSearch = $("#main_search_input").val();
    if (mainSearch && mainSearch.trim() !== "") {
      filters.item_input = mainSearch.trim();
    }

    // Para métodos antigos, adicionar tag vazio para manter compatibilidade
    if (!this.useNewMethods) {
      filters.tag = "";
    }

    console.log("Filtros coletados para exportação:", filters);
    return filters;
  },

  prepareExportForm: function (filters, action) {
    var $form = $("#export_form");

    // Limpar campos existentes (exceto token)
    $form.find('input:not([name="download_token"])').remove();

    // Definir action
    $form.attr("action", action);

    // Adicionar filtros como campos hidden
    Object.keys(filters).forEach(function (key) {
      var value = filters[key];

      if (Array.isArray(value)) {
        // Para arrays, criar múltiplos inputs
        value.forEach(function (item) {
          if (item && item !== "" && item !== "-1") {
            $form.append(
              $("<input>").attr({
                type: "hidden",
                name: key + "[]",
                value: item,
              })
            );
          }
        });
      } else if (value && value !== "" && value !== "-1") {
        // Para valores únicos
        $form.append(
          $("<input>").attr({
            type: "hidden",
            name: key,
            value: value,
          })
        );
      }
    });
  },

  waitForExportCompletion: function (type, $button) {
    var self = this;
    var token = $('#export_form input[name="download_token"]').val();
    var statusEndpoint = "";

    if (type === "padrao") {
      statusEndpoint = this.useNewMethods
        ? this.base_url + "atribuir_grupo/ajax_xls_log_status_new"
        : this.base_url + "atribuir_grupo/ajax_xls_log_status";
    } else if (type === "multipaises") {
      statusEndpoint = this.useNewMethods
        ? this.base_url + "atribuir_grupo/ajax_xls_log_status_multipaises_new"
        : this.base_url + "atribuir_grupo/ajax_xls_log_status_multipaises";
    }

    // Mostrar overlay de loading
    this.showLoadingOverlay();

    // Aguardar um pouco antes de verificar status
    this.exportTimer = setTimeout(function () {
      $.ajax({
        url: statusEndpoint,
        method: "POST",
        data: { token: token },
        success: function (response) {
          self.finishExport($button);
        },
        error: function () {
          self.finishExport($button);
          console.warn(
            "Erro ao verificar status da exportação, mas o download pode ter sido iniciado"
          );
        },
      });
    }, 1500);
  },

  finishExport: function ($button) {
    // Remover estado de loading
    this.setLoadingState($button, false);

    // Esconder overlay
    this.hideLoadingOverlay();

    // Reset flag
    this.isExporting = false;

    // Limpar timer se existir
    if (this.exportTimer) {
      clearTimeout(this.exportTimer);
      this.exportTimer = null;
    }
  },

  setLoadingState: function ($button, loading) {
    if (loading) {
      $button.button("loading");
    } else {
      $button.button("reset");
    }
  },

  showLoadingOverlay: function () {
    $("#loading-overlay").show();
    $("#loading-message").text("Gerando arquivo para download...");
  },

  hideLoadingOverlay: function () {
    $("#loading-overlay").hide();
  },

  generateToken: function () {
    // Gerar um token simples para controle de download
    return Date.now() + "_" + Math.random().toString(36).substr(2, 9);
  },

  // Método público para coletar filtros (pode ser usado por outras funcionalidades)
  getActiveFilters: function () {
    return this.collectActiveFilters();
  },
};

// Inicialização automática quando o documento estiver pronto
$(document).ready(function () {
  IndexNewExport.init();
});
