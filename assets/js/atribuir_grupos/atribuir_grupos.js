atribuir_grupos = {
  base_url: "",
  itens_found: [],
  tag: "",
  selected_tags: [],
  group_request: null,
  hidden_groups_request: null,
  selected_caracteristica: null,

  init: function (base_url) {
    this.base_url = base_url;
  },
  validate: function (title, hideModal = true) {
    $("#ajax_validate").html(
      '<h5 class="alert alert-danger"><strong>Oops!</strong> ' + title + "</h5>"
    );

    if (hideModal) {
      $("#modal_motivo").modal("hide");
    }
  },
  save_send: function (postData) {
    // $('.red-text').each(function() {
    // 	// Encontra o select associado ao elemento de texto
    // 	var select = $(this).closest('.panel').find('select');

    // 	// Verifica se o elemento pai do select está hidden
    // 	var selectHidden = select.closest('.panel').is(':hidden');

    // 	// Verifica se o select foi preenchido e se o elemento não está hidden
    // 	if (!selectHidden && (select.val() === "")) {
    // 	  console.log('Select não preenchido e visível para:', $(this).text());
    // 	}
    //   });

    $.post(`${this.base_url}atribuir_grupo/salvar`, postData, () => {
      $("#modal_motivo").on("hidden.bs.modal", function () {
        var modal = $(this);

        var selects = modal.find("select.selectpicker");
        selects.each(function () {
          $(this).selectpicker("val", "").selectpicker("refresh");
        });

        modal.find('input[type="radio"]').prop("checked", false);
        modal.find("input, textarea").each(function () {
          var name = $(this).attr("name");
          if (name !== "raw_attr[]") {
            $(this).val("");
          }
        });
      });

      $("#modal_motivo").modal("hide");
      const button = document.getElementById("send_search_itens");
      button.click();
      swal("Sucesso", "Atribuido com Sucesso!", "success");
      //location.reload();
      $.ajax({
        url: "pr/perguntas/unsetUsuarioBloqueadorItens",
        method: "POST",
      });
    });
  },
  save: function (multimpaises) {
    var itens = [];
    var checked_itens = $('input[type="checkbox"][name="item[]"]:checked');
    var checked_grupo = $('input[type="radio"][name="grupo[]"]:checked');
    var grupo = $(checked_grupo).val();

    var idPredicaoOrdem = checked_grupo.attr("data-id-predicao-ordem");
    var fromPredicao = checked_grupo.attr("data-from") == "predicao";

    var btn = $("#save_association");

    var id_empresa = $("#id_empresa").val();

    //$(btn).button("loading");

    $(checked_itens).each(function () {
      var item = {
        part_number: $(this).val(),
        estabelecimento: $(this).attr("data-estabelecimento"),
      };

      itens.push(item);
    });

    var grupo = $(checked_grupo).val();
    var observacoes = $("#observacoes").val();
    var is_drawback = $(
      'input[type="checkbox"][name="is_drawback"]:checked'
    ).val();
    var observacoes_mestre = $("#observacoes_mestre").val();
    var marca = $("#marca").val();
    var funcao = $("#funcao").val();
    var inf_adicionais = $("#inf_adicionais").val();
    var aplicacao = $("#aplicacao").val();
    var material_constitutivo = $("#material_constitutivo").val();
    var subsidio = $("#subsidio").val();
    var caracteristica = $("#caracteristica").val();
    var memoria_classificacao = $("#memoria_classificacao").val();
    var descricao_proposta_completa = $("#descricao_proposta_completa").val();
    var descricao_mercado_local = $("#descricao_mercado_local").val();
    var descricao = $("#descricao").val();
    var evento = $("#evento").val();
    var lessin = $("#select-lessin").val();
    var nve = [];
    var li = "NÃO";
    var li_orgao_anuente = "";
    var li_destaque = "";
    var antidumping = "NÃO";
    var ex_ii = "";
    var ex_ipi = "";
    var classificacao_energetica = "";
    var predicao_ordem = fromPredicao
      ? Number($(`#${idPredicaoOrdem}`).val())
      : null;
    var has_predicao = fromPredicao;

    const raw_attrs = $('input[type="hidden"][name="raw_attr[]"]')
      .map(function () {
        return $(this).val();
      })
      .get();

    const allConditionedAttrs = new Set();

    raw_attrs.forEach((raw_attr) => {
      const attr = JSON.parse(raw_attr);

      if (
        attr.atributoCondicionante &&
        attr.condicionados &&
        attr.condicionados.length > 0
      ) {
        attr.condicionados.forEach((condicionado) => {
          allConditionedAttrs.add(condicionado.atributo.codigo);
        });
      }
    });

    const processedAttrs = new Set();

    const requiredNames = new Set();

    raw_attrs.forEach((raw_attr) => {
      const attr = JSON.parse(raw_attr);

      if (processedAttrs.has(attr.codigo)) {
        return;
      }

      if (allConditionedAttrs.has(attr.codigo)) {
        return;
      } else {
        if (
          attr.atributoCondicionante &&
          attr.condicionados &&
          attr.condicionados.length > 0
        ) {
          const condicionados = attr.condicionados;

          condicionados.forEach((condicionado) => {
            if (condicionado.condicao.valor == "true") {
              condicionado.condicao.valor = "1";
            }

            const condicao =
              parseInt(attr.dbdata.codigo) ===
              parseInt(condicionado.condicao.valor);

            if (condicao) {
              processedAttrs.add(condicionado.atributo.codigo);

              //Trata os fiflos do filhos
              if (
                condicionado.atributo.atributoCondicionante &&
                condicionado.atributo.condicionados &&
                condicionado.atributo.condicionados.length > 0
              ) {
                const condicionadosFilhos = condicionado.atributo.condicionados;

                condicionadosFilhos.forEach((condicionadoFilho) => {
                  if (condicionadoFilho.condicao.valor == "true") {
                    condicionadoFilho.condicao.valor = "1";
                  }

                  const condicaoFilho =
                    parseInt(condicionado.atributo.dbdata.codigo) ===
                    parseInt(condicionadoFilho.condicao.valor);

                  if (condicaoFilho) {
                    processedAttrs.add(condicionadoFilho.atributo.codigo);

                    if (
                      condicionadoFilho.obrigatorio &&
                      condicionadoFilho.atributo.dbdata.codigo === ""
                    ) {
                      requiredNames.add(
                        condicionadoFilho.atributo.nomeApresentacao
                      );
                    }
                  } else {
                    if (
                      condicionado.obrigatorio &&
                      condicionado.atributo.dbdata.codigo === ""
                    ) {
                      requiredNames.add(condicionado.atributo.nomeApresentacao);
                    }
                  }
                });
              } else {
                if (
                  condicionado.obrigatorio &&
                  condicionado.atributo.dbdata.codigo === ""
                ) {
                  requiredNames.add(condicionado.atributo.nomeApresentacao);
                }
              }
            } else {
              if (!attr.dbdata.codigo || attr.dbdata.codigo === "") {
                if (attr.obrigatorio) {
                  requiredNames.add(attr.nomeApresentacao);
                }
              }
            }
          });
        } else {
          if (!attr.dbdata.codigo || attr.dbdata.codigo === "") {
            if (attr.obrigatorio) {
              requiredNames.add(attr.nomeApresentacao);
            }
          }
        }
        processedAttrs.add(attr.codigo);
      }
    });

    const required_attrs = Array.from(requiredNames);

    $(".nve-selected").each(function (e) {
      nve.push({
        key: $(this).attr("name"),
        value: $(this).val(),
      });
    });

    var checkLi = $('input[type="radio"].li-checked:checked');
    if (checkLi.length == 1) {
      li_orgao_anuente = checkLi.attr("data-orgao-anuente");
      li_destaque = checkLi.attr("data-destaque");
      li = "SIM";
    }

    var checkAntidumping = $('input[type="radio"].antidumping-checked:checked');
    if (checkAntidumping.length == 1) {
      antidumping = "SIM";
    }

    var checkClassificacaoEnergetica = $(
      'input[type="radio"].classificacao-energetica-checked:checked'
    );

    if (checkClassificacaoEnergetica.length) {
      classificacao_energetica = checkClassificacaoEnergetica.attr(
        "data-classificacao-energetica"
      );
    }

    var checkIi = $('input[type="radio"].ex-ii-checked:checked');
    if (checkIi.length == 1) {
      ex_ii = checkIi.val();
    }

    var checkIpi = $('input[type="radio"].ex-ipi-checked:checked');
    if (checkIpi.length == 1) {
      ex_ipi = checkIpi.val();
    }

    var destaqueElement = $("input[name='destaque']:checked");

    if (destaqueElement) {
      var destaqueTd = destaqueElement.parent().parent();
      var suframaDestaque = destaqueElement.val();
      var suframaPpb = destaqueTd.find(".suframa-ppb").html();
      var suframaDescricao = destaqueTd.find(".suframa-descricao").html();
      var suframaProduto = destaqueTd.find(".suframa-produto").html();
      var suframaCodigo = destaqueTd.find(".suframa-codigo").html();
    }

    if (suframaPpb != "" && suframaPpb != undefined && suframaPpb != null) {
      suframaPpb.replace(/&nbsp;/, "").trim();
    }

    var erroArr = [];

    if (itens.length == 0) {
      erroArr.push("Selecione pelo menos um item para associar");
    }

    if (!grupo) {
      erroArr.push("Selecione um grupo tarifário para associar");
    }

    if (observacoes.length > 3000) {
      erroArr.push(
        "É necessário que a observação tenha no máximo 3000 caracteres."
      );
    }

    if (
      descricao_proposta_completa &&
      descricao_proposta_completa.length > 5000
    ) {
      erroArr.push(
        "É necessário que a Descrição proposta completa tenha no máximo 5000 caracteres."
      );
    }

    if (descricao_mercado_local && descricao_mercado_local.length > 5000) {
      erroArr.push(
        "É necessário que a Descrição proposta resumida tenha no máximo 5000 caracteres."
      );
    }

    if (!erroArr.length) {
      $("#validate_motivo").html(
        "" +
          '<h5 class="alert alert-info">' +
          "<strong>Atribuindo...</strong>" +
          " Estamos fazendo a atribuição dos itens ao grupo tarifário informado." +
          "</h5>"
      );

      var filtros = {
        atribuido_para: $('[name="atribuido_para"]').val(),
        tag: $('[name="tag"]').val(),
      };

      post_data = {
        itens: itens,
        grupo: grupo,
        observacoes: observacoes,
        marca: marca,
        funcao: funcao,
        inf_adicionais: inf_adicionais,
        aplicacao: aplicacao,
        material_constitutivo: material_constitutivo,
        filtros: filtros,
        subsidio: subsidio,
        caracteristica: caracteristica,
        memoria_classificacao: memoria_classificacao,
        descricao_proposta_completa: descricao_proposta_completa,
        descricao_mercado_local: descricao_mercado_local,
        descricao: descricao,
        evento: evento,
        lessin: lessin,
        suframa_destaque: suframaDestaque ? suframaDestaque : "",
        suframa_ppb: suframaPpb ? suframaPpb : "",
        suframa_descricao: suframaDescricao ? suframaDescricao : "",
        suframa_produto: suframaProduto ? suframaProduto : "",
        suframa_codigo: suframaCodigo ? suframaCodigo : "",
        num_ex_ii: ex_ii,
        num_ex_ipi: ex_ipi,
        nve: nve,
        li: li,
        li_orgao_anuente: li_orgao_anuente,
        li_destaque: li_destaque,
        antidumping: antidumping,
        concatenar_campos: $("#concatenar_campos").val(),
        classificacao_energetica: classificacao_energetica,
        predicao_ordem: predicao_ordem,
        has_predicao: has_predicao,
        raw_attrs: raw_attrs,
        observacoes_mestre: observacoes_mestre,
        preencher_desc_resumida: $("#preencher_desc_resumida").val(),
        is_drawback: is_drawback,
      };

      var elementoA = document.querySelector(
        'li[role  ="presentation"] a[href="#aba-atributos"]'
      );
      var result = true;
      if (elementoA) {
        var estiloComputado = window.getComputedStyle(elementoA);
        var displayAtual = estiloComputado.getPropertyValue("display");

        if (displayAtual === "none") {
          result = false;
        } else {
          result = true;
        }
      } else {
        result = true;
      }
      if (required_attrs.length > 0 && result) {
        var self = this;
        swal({
          title: "Atenção!",
          text:
            "Alguns atributos obrigatórios não estão preenchidos, deseja continuar? <br /> " +
            required_attrs.join(", "),
          type: "warning",
          confirmButtonText: "OK",
          cancelButtonText: "Cancelar",
          showConfirmButton: true,
          showCancelButton: true,
          allowOutsideClick: false,
        }).then(
          function () {
            self.save_send(post_data);
          },
          function (dismiss) {
            $(btn).button("reset");
          }
        );
      } else {
        // swal({
        // 	title: "Atenção!",
        // 	text: 'Deseja homologar os atributos? ',
        // 	type: "warning",
        // 	confirmButtonText: "OK",
        // 	cancelButtonText: "Cancelar",
        // 	showConfirmButton: true,
        // 	showCancelButton: true,
        // 	allowOutsideClick: false
        // }).then(function() {
        // 	// self.save_send(post_data);
        // 	alert('Homologar atributos');
        // }, function(dismiss) {
        // 	alert('Não Homologar atributos');
        // });

        this.save_send(post_data);
      }
      setTimeout(function () {
        if (itens.length == 1) {
          if (multimpaises == 1) {
            var part_number = itens[0].part_number;
            var estabelecimento = itens[0].estabelecimento;

            $.ajax({
              url: base_url + "atribuir_grupo/ajax_get_item",
              method: "POST",
              data: {
                part_number: part_number,
                estabelecimento: estabelecimento,
              },
              dataType: "json",
              success: function (dadositem) {
                if (dadositem.item.ncm_proposto != null) {
                  $("#ncmbrasil-modal").val(dadositem.item.ncm_proposto);
                  $("#btn-salvar-paises").prop("disabled", false);
                  $("#input-part-number-modal").val(part_number);
                  $("#estabelecimento-modal").val(estabelecimento);

                  $.ajax({
                    url:
                      base_url +
                      "cadastros/mestre_itens/ajax_get_dados_modal_paises",
                    method: "POST",
                    data: {
                      part_number: part_number,
                      estabelecimento: estabelecimento,
                      id_empresa: id_empresa,
                    },
                    dataType: "json",
                    success: function (dados) {
                      $("#descricao-modal").val(dados.entry.descricao);

                      $("#modal-multi-paises").modal("show");

                      if (dados && dados.empresa_pais) {
                        dados.empresa_pais.forEach((pais) => {
                          const slug = pais.slug;
                          if (pais.item_pais.length > 0) {
                            pais.item_pais.forEach((item) => {
                              // Atribuindo os valores aos campos usando jQuery
                              $("#codigo_classificacao_" + slug).val(
                                item.codigo_classificacao
                              );
                              $("#desc_curta_" + slug).val(
                                item.descricao_curta
                              );
                              $("#desc_completa_" + slug).val(
                                item.descricao_completa
                              );
                              $("#desc_li_" + slug).val(item.li);
                              $("#desc_adicional_" + slug).val(
                                item.informacao_adicional
                              );
                            });
                          } else {
                            $("#codigo_classificacao_" + slug).val("");
                            $("#desc_curta_" + slug).val("");
                            $("#desc_completa_" + slug).val("");
                            $("#desc_li_" + slug).val("");
                            $("#desc_adicional_" + slug).val("");
                          }
                          $("#codigo_classificacao_" + slug).removeAttr(
                            "disabled"
                          );
                          $("#desc_curta_" + slug).removeAttr("disabled");
                          $("#desc_completa_" + slug).removeAttr("disabled");
                          $("#desc_li_" + slug).removeAttr("disabled");
                          $("#desc_adicional_" + slug).removeAttr("disabled");
                        });
                      }
                    },
                    error: function () {
                      $("#message_user_transfer_owner_paises").html(
                        '<div class="alert alert-danger"><button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>Erro ao enviar os dados para o servidor.</div>'
                      );
                      $("#message_user_transfer_owner_paises").show();
                      $("#loading-img").hide();
                      $("#btn-salvar-paises").prop("disabled", false);
                      $("#btn-salvar-paises").html("Salvar");
                    },
                  });
                } else {
                  swal({
                    title: "Atenção!",
                    text: "Erro ao definir NCM, deseja continuar?",
                    type: "warning",
                    confirmButtonText: "OK",
                    cancelButtonText: "Cancelar",
                    showConfirmButton: true,
                    showCancelButton: true,
                    allowOutsideClick: false,
                  }).then(
                    function () {
                      self.save_send(post_data);
                    },
                    function (dismiss) {
                      $(btn).button("reset");
                    }
                  );
                }
              },
            });
          }
        }
      }, 2500);
    } else {
      var erros = "";
      for (let i = 0; i < erroArr.length; i++) {
        const item = erroArr[i];
        erros += "<p>" + item + "</p>";
      }
      swal("Atenção!", erros, "warning");
      $(btn).button("reset");
      return false;
    }
  },

  get_itens: function (clear_msg) {
    clear_msg = clear_msg == undefined ? true : clear_msg;

    if (clear_msg == true) {
      $("#ajax_validate").html("");
    }

    this.safe_submit(true);

    var btn = $("#send_search_itens").button("loading");

    this.itens_found = [];

    $("#response-ajax").css("display", "block");

    order_by = $("#order_item").attr("name");
    order_val = $("#order_item").val();

    var id_usuario = $("#atribuido_para").val();
    var id_usuario_logado = $("#form_transferir_owner  #id_usuario").val();
    var usuario_desbloqueador = $("#desblquear_itens").val();
    let evento = $("#eventoPacote").val();
    let prioridade = $("#prioridadeSelect").val();
    let status = $("#status").val();
    let owner = $("#owner").val();
    let sistemas_origens = $("#sistemaorigemSelect").val();
    let triagem_diana_falha = $("#triagem_diana_falha").is(":checked")
      ? "1"
      : "0";

    var post_data = {
      item_input: $('form#search_itens textarea[name="item_input"]').val(),
      tag: this.tag,
      order: { order: order_by, by: order_val },
      atribuido_para: id_usuario,
      evento: evento,
      prioridade: prioridade,
      status: status,
      owner: owner,
      sistemas_origens: sistemas_origens,
      triagem_diana_falha: triagem_diana_falha,
      filtered: "1", // Indica que o formulário foi submetido
    };

    var multi_estabelecimentos = $("#table-itens").attr(
      "data-multi-estabelecimentos"
    );

    const _this = this;

    $.post($("#search_itens").attr("action"), post_data, function (data) {
      $("#itens_holder").empty();

      if (data.itens == null) {
        $("#itens_holder").html(
          '<tr class="click-select"><td colspan="99" class="text-center">Nenhum item encontrado</td></tr>'
        );
      } else {
        for (i = 0; i < data.itens.length; i++) {
          var item = data.itens[i];

          _this.itens_found[item.part_number] = item;

          var desabilitado = "";

          if (
            item.usuario_bloqueador != null &&
            item.usuario_bloqueador != id_usuario_logado &&
            usuario_desbloqueador != 1
          ) {
            desabilitado = "disabled";
          }

          var row_item =
            "<tr class='click-select'>" +
            '<td><input type="checkbox" data-bloqueador="' +
            item.usuario_bloqueador +
            '" class="item_selected" ' +
            desabilitado +
            ' data-part-number="' +
            _this.encodeHTML(item.part_number) +
            '" data-tag="' +
            item.tag +
            '" data-descricao="' +
            // _this.quote(item.descricao) +
            '" data-ncm_item ="' +
            item.ncm +
            '" data-estabelecimento="' +
            item.estabelecimento +
            '" name="item[]" value="' +
            _this.encodeHTML(item.part_number) +
            '">';

          if (
            item.usuario_bloqueador != null &&
            item.usuario_bloqueador != id_usuario_logado
          ) {
            row_item +=
              '<i class="glyphicon glyphicon-lock" style="color:#FF0000" data-toggle="tooltip" data-placement="top" title="Bloqueado por ' +
              item.nome_usuario_bloqueador +
              '"></i>';
          } else if (item.usuario_bloqueador == id_usuario_logado) {
            row_item +=
              '<i class="glyphicon glyphicon-lock" style="color:#008000" data-toggle="tooltip" data-placement="top" title="Bloqueado por ' +
              item.nome_usuario_bloqueador +
              '"></i>';
          }
          if (item.sistema_origem == "MANUAL") {
            row_item +=
              '<span class="label label-success" data-toggle="tooltip" title="MANUAL">M</span>';
          }
          row_item +=
            "</td>" + '<td width="19%" style="word-break: break-word;">';
          if ($("#integracao_simplus").val() == true) {
            row_item +=
              'div class="d-flex">' +
              '<a href="javascript: void(0)" class="part_number_click" data-estabelecimento-simplus="' +
              item.estabelecimento +
              '" data-part-number="' +
              item.part_number +
              '">';
          } else {
            row_item +=
              '<div class="d-flex">' +
              '<a href="javascript: void(0)" class="part_number_inf" data-estabelecimento="' +
              item.estabelecimento +
              '" data-part-number="' +
              item.part_number +
              '">';
          }

          row_item += item.part_number + "<br/>";
          if (
            item.pn_primario_mpn != null &&
            item.pn_primario_mpn != "" &&
            item.hasPnPrimarioSecundario == "S"
          ) {
            row_item += item.pn_primario_mpn + "<br/>";
          }
          if (
            item.pn_secundario_ipn != null &&
            item.pn_secundario_ipn != null &&
            item.hasPnPrimarioSecundario == "S"
          ) {
            row_item += item.pn_secundario_ipn;
          }
          row_item += "</a>";

          // if (item.integracao_novo_material != '') {
          // 	row_item += "&nbsp;("+item.integracao_novo_material+")&nbsp;";
          // }

          if (item.item_ja_homologado == 1) {
            row_item +=
              "<span class='bg-success' data-toggle='tooltip' title='Item já homologado anteriormente' style='margin-left: 5px;'>";
            // row_item += '<i class="fa fa-check-circle text-success" aria-hidden="true"></i>';
            row_item +=
              '<img src="/assets/img/ok-icon.ico" alt="Ícone check verde para representar OK" width="14">';
            row_item += "</span>";
          }

          row_item += "</div>";

          if (item.has_pergunta > 0) {
            if (item.has_pergunta_pendente > 0) {
              row_item +=
                '<span class="label label-warning">' +
                item.has_pergunta_pendente +
                " pergunta(s) pendente(s)</span>";
            } else {
              row_item +=
                '<span class="label label-success">nenhuma pergunta pendente</span>';
            }
          }

          // Exibir a label de acordo com o status de triagem apenas se a empresa tiver a triagem estiver habilitada
          if (hasStatusTriagemDiana) {
            if (item.status_triagem_diana == "Pendente de triagem" && item.id_status == 6) {
              row_item +=
                '<br><span class="label label-warning">Pendente de triagem</span>';
            } else if (item.status_triagem_diana == "Triagem aprovada" && (item.id_status == 6 || item.id_status == 8)) {
              row_item +=
                '<br><span class="label label-success">Triagem aprovada</span>';
            } else if (item.status_triagem_diana == "Triagem reprovada" && item.id_status == 7) {
              // Cor da label para Triagem Reprovada: #B01A00
              row_item +=
                '<br><span class="label" style="background-color: #B01A00">Triagem reprovada</span>';
            } else if (item.status_triagem_diana == "Falha na triagem" && item.id_status == 8) {
              // Cor da label para Falha na Triagem: #CD1F73
              row_item +=
                '<br><span class="label" style="background-color: #CD1F73">Falha na triagem</span>';
            }
          }

          if (_this.tag == "" && item.tag) {
            row_item +=
              '<br><span class="label label-info">' + item.tag + "</span>";
          }

          if (item.dat_criacao !== null) {
            var dataCriada = moment(
              item.dat_criacao,
              "YYYY-MM-DD HH:mm:ss"
            ).format("DD/MM/YY");
            row_item += "<br><strong>DC: " + dataCriada + "</strong><br/>";
          }

          if (item.data_modificacao !== null) {
            var dataModificada = moment(
              item.data_modificacao,
              "YYYY-MM-DD HH:mm:ss"
            ).format("DD/MM/YY");
            row_item += "<strong>DM: " + dataModificada + "</strong>";
          }

          row_item += "</td>";

          if ($("#item_order_owner").length > 0) {
            var owner_codigo = item.owner_codigo ? item.owner_codigo : "";
            var owner_descricao = item.owner_descricao
              ? item.owner_descricao
              : "";
            var responsaveis_gestores_nomes = item.responsaveis_gestores_nomes
              ? item.responsaveis_gestores_nomes
              : "";

            var owner_display =
              owner_codigo +
              (owner_codigo && owner_descricao ? "-" : "") +
              owner_descricao +
              (owner_descricao && responsaveis_gestores_nomes ? "-" : "") +
              responsaveis_gestores_nomes;

            row_item +=
              "<td style='word-break: break-word;'>" + owner_display + "</td>";
          }

          if ($("#item_order_ncm").length > 0) {
            row_item += "<td>" + (item.ncm !== null ? item.ncm : "-") + "</td>";
          }

          if (multi_estabelecimentos == 1) {
            if (item.estabelecimento == null || item.estabelecimento == "") {
              row_item += '<td class="text-center">N/A</td>';
            } else {
              row_item +=
                '<td class="text-center">' + item.estabelecimento + "</td>";
            }
          }

          if (hasPeso) {
            row_item += "<td>" + item.peso + "</td>";
          }

          if (hasPrioridade) {
            if (
              item.empresa_prioridade == null ||
              item.empresa_prioridade == ""
            ) {
              row_item += '<td class="text-center"> </td>';
            } else {
              row_item += "<td>" + item.empresa_prioridade + "</td>";
            }
          }

          if (hasDescricaoGlobal) {
            row_item +=
              '<td style="word-break: break-word;">' +
              (item.descricao
                ? item.descricao
                : item.descricao_global + "<strong> (GLOBAL)</strong>") +
              "</td>";
          } else {
            row_item +=
              '<td style="word-break: break-word;">' +
              limitarTexto(item.descricao, 255) +
              "</td>" +
              "</tr>";
          }

          $("#itens_holder").append(row_item);
        }

        $(document).on("change", "#select-all", function (e) {
          _this.selected_item($(".item_selected").first());
        });

        $(document).on("click", ".item_selected:checked", function (e) {
          _this.selected_item(this);
          var id_usuario_logado = $(
            "#form_transferir_owner  #id_usuario"
          ).val();
          var bloqueador = $(this).data("bloqueador");

          if (
            bloqueador != "" &&
            bloqueador != null &&
            bloqueador != id_usuario_logado
          ) {
            $(this).addClass("travado");

            //Trava os demais botóes para permitir somente o desbloqueio
            $(".btn-success").attr("disabled", "disabled");
            $(".btn-primary").attr("disabled", "disabled");
            $("#desvincularGrupo").attr("disabled", "disabled");
          }
        });

        $(document).on("click", ".travado", function (e) {
          _this.selected_item(this);
          $(".btn-success").removeAttr("disabled");
          $(".btn-primary").removeAttr("disabled");
          $("#desvincularGrupo").removeAttr("disabled");
          $(this).removeClass("travado");
        });

        $(document).on("click", ".part_number_click", function (e) {
          $("#detalhes-simplus").modal();
          var post_action = {
            part_number: $(this).attr("data-part-number"),
            estabelecimento: $(this).attr("data-estabelecimento-simplus"),
          };

          $.post(
            "./atribuir_grupo/ajax_get_item_by_pn_company",
            post_action,
            function (data) {
              $("#detalhes-simplus").html(data);
            }
          );
        });
      }
      $("#loading-overlay").hide();
      _this.update_results();

      btn.button("reset");
      $("#response-ajax").css("display", "none");
      _this.recalc_sticky_kit();
    });

    return false;
  },

  get_grupos: function (group_by, caracteristica, new_request) {
    if (new_request == undefined) {
      new_request = false;
    }

    const _this = this;
    var tag = null;

    this.safe_submit(true);

    if (!$("#disable_filter_by_tag:checked").val()) {
      if (this.selected_tags.length > 0) {
        tag = this.selected_tags;
      } else {
        tag = this.tag;
      }
    }

    var order_by = $("#order_grupo").attr("name");
    var order_val = $("#order_grupo").val();
    let val_search_ncm = 0;

    $("#itens_holder")
      .find("td > input:checked")
      .each(function () {
        val_search_ncm = $("#disable_filter_by_tag:checked").val();
        const grupoInput = document.querySelector('input[name="grupo_input"]');

        if (
          val_search_ncm != 1 &&
          grupoInput.value.length == 0 &&
          $(this).attr("data-ncm_item") !== null &&
          $(this).attr("data-ncm_item") !== undefined &&
          $(this).attr("data-ncm_item") !== "null"
        ) {
          $('form#search_grupos_tarifarios input[name="grupo_input"]').val(
            $(this).attr("data-ncm_item")
          );
        }
      });

    var post_data = {
      grupo_input: $(
        'form#search_grupos_tarifarios input[name="grupo_input"]'
      ).val(),
      tag: tag,
      order: { order: order_by, by: order_val },
    };

    if ($("#filter_selected option:selected").val() == "id_grupo_tarifario") {
      group_by = "descricao";
      var group_panel = $(
        '.panel_carac_grupos table tbody[data-caracteristica="' +
          caracteristica +
          '"]'
      );
      var id_panel = group_panel?.id;
    }

    if (group_by == null && !new_request) {
      if (caracteristica != undefined) {
        post_data["caracteristica"] = caracteristica;
        var group_panel = $(
          '.panel_carac_grupos table tbody[data-caracteristica="' +
            caracteristica +
            '"]'
        );
        var id_panel = group_panel[0]?.id;
        this.selected_caracteristica = caracteristica;
      } else {
        post_data["caracteristica"] = this.selected_caracteristica;
        var group_panel = $(
          '.panel_carac_grupos table tbody[data-caracteristica="' +
            this.selected_caracteristica +
            '"]'
        );
        var id_panel = group_panel[0]?.id;
      }
    } else {
      post_data["group_by"] = "caracteristica";
      $("#accordion_grupos").empty();
    }

    if ($("#filter_selected option:selected").val() == "id_grupo_tarifario") {
      var id_panel = "accordion_grupos";
      post_data["group_by"] = $("#filter_selected option:selected").val();
    }

    if ($(group_panel).children().length == 0 || new_request) {
      var btn = $("#send_search_grupos_tarifarios").button("loading");
      $("#response-ajax").css("display", "block");

      this.group_request = $.post(
        $("#search_grupos_tarifarios").attr("action"),
        post_data,
        function (data) {
          var data = JSON.parse(data);

          if (data.grupos == null) {
            var accordion = $("#accordion_grupos");
            if (accordion.text() != "Nenhum grupo encontrado.") {
              accordion.append(
                '<h5 class="text-center">Nenhum grupo encontrado.</h5>'
              );
            }
          } else {
            if (group_by == "caracteristica") {
              $.each(data.grupos, function (i) {
                _this.create_group_structure(
                  data.grupos[i].caracteristica,
                  i,
                  data.grupos[i]
                );
              });
            } else {
              _this.fill_groups(data, id_panel);
            }
          }

          btn.button("reset");
          $("#response-ajax").css("display", "none");
          _this.recalc_sticky_kit();
        }
      );
    }

    return false;
  },

  show_ncm_detail: function (element) {
    id_grupo_tarifario = $(element).attr("data-id-grupo");
    ncm_recomendada = $(element).attr("data-ncm-recomendada");

    if ($("#info-grupo-" + id_grupo_tarifario).find("table").length > 0) {
      $("#info-grupo-" + id_grupo_tarifario + " table").remove();
      this.recalc_sticky_kit();
    } else {
      const _this = this;
      $.post(
        this.base_url + "atribuir_grupo/ajax_get_ncm_info",
        { ncm: ncm_recomendada },
        function (data) {
          $("#info-grupo-" + id_grupo_tarifario).append(data);
          _this.recalc_sticky_kit();
        }
      );
    }
  },

  show_obs_detail: function (element) {
    $(element).parent().find(".obs-content").toggle();
    this.recalc_sticky_kit();
  },

  hide_group: function (grupo) {
    grupo = grupo.replace("hide_group_", "");

    _this = this;

    var tag = $("#select_tag option:selected").val();

    var post_data = { id_grupo_tarifario: grupo, tag: tag };

    $.notify(
      {
        message:
          "Grupo oculto com sucesso para o pré-agrupamento <strong>" +
          (tag ? tag.toUpperCase() : "TODOS") +
          "</strong>.",
      },
      {
        type: "black",
        placement: {
          from: "bottom",
          align: "center",
        },
      }
    );

    $.post(
      this.base_url + "atribuir_grupo/ajax_hide_group",
      post_data,
      function (result) {
        _this.get_grupos("caracteristica");
        _this.refresh_hidden();
      }
    );
  },

  show_group: function (grupo, caracteristica) {
    grupo = grupo.replace("show_group_", "");

    _this = this;

    var tag = $("#select_tag option:selected").val();

    var post_data = { id_grupo_tarifario: grupo, tag: tag };

    $.post(
      this.base_url + "atribuir_grupo/ajax_show_group",
      post_data,
      function (result) {
        if (result) {
          if (_this.selected_caracteristica != null) {
            _this.get_grupos("caracteristica", null, true);
          } else {
            _this.get_grupos("caracteristica", null, true);
          }

          _this.refresh_hidden();
        }
      }
    );
  },

  refresh_hidden: function () {
    if (this.selected_tags.length > 0) {
      tag = this.selected_tags;
    } else {
      tag = this.tag;
    }

    if (this.hidden_groups_request) {
      this.hidden_groups_request.abort();
    }

    order_by = $("#order_grupo").attr("name");
    order_val = $("#order_grupo").val();

    var post_data = {
      grupo_input: $(
        'form#search_grupos_tarifarios input[name="grupo_input"]'
      ).val(),
      tag: tag,
      order: { order: order_by, by: order_val },
    };

    const _this = this;

    this.hidden_groups_request = $.post(
      this.base_url + "atribuir_grupo/ajax_get_hidden_groups",
      post_data,
      function (data) {
        var data = JSON.parse(data);

        if (data.hidden_grupos == null) {
          $("#grupos_ocultos_holder").html(
            '<tr><td colspan="3" class="text-center">Nenhum grupo encontrado</td></tr>'
          );
          $("#painel_grupos_ocultos").css("display", "none");
        } else {
          $("#painel_grupos_ocultos").css("display", "block");
          if (data.hidden_grupos != null) {
            $("#grupos_ocultos_holder").empty();

            for (i = 0; i < data.hidden_grupos.length; i++) {
              grupo = data.hidden_grupos[i];

              row_grupo =
                '<tr id="tr-grupo-' +
                grupo.id_grupo_tarifario +
                '">' +
                '<td style="width: 90%">' +
                grupo.descricao +
                '<div class="info-grupo-oculto" id="info-grupo-' +
                grupo.id_grupo_tarifario +
                '">' +
                '<strong>NCM Recomendada: </strong><a class="display-ncm-table" id="ncm_info" data-id-grupo="' +
                grupo.id_grupo_tarifario +
                '" data-ncm-recomendada="' +
                grupo.ncm_recomendada +
                '" data-toggle="ncm-recomendada-' +
                grupo.ncm_recomendada +
                '" href="javascript: void(0)">' +
                '<i class="glyphicon glyphicon-info-sign"></i> ' +
                grupo.ncm_recomendada +
                "</a></div>" +
                '<div class="obs-grupo-oculto" id="obs-grupo' +
                grupo.id_grupo_tarifario +
                '">' +
                "<strong>Observação: </strong>";

              if (!grupo.observacao) {
                row_grupo += "N/A";
              } else {
                row_grupo +=
                  '<a class="show-obs" href="javascript: void(0)">' +
                  '<i class="glyphicon glyphicon-info-sign"></i></a>' +
                  '<div style="display: none" class="obs-content">' +
                  grupo.observacao +
                  "</div>";
              }

              row_grupo +=
                "</div></td>" +
                '<td style="text-align: center;width: 10%;"><a class="show_group" href="javascript: void(0)" id="show_group_' +
                grupo.id_grupo_tarifario +
                '" data-toggle="tooltip" data-placement="top" title="Clique para reexibir o Grupo Tarifário na pesquisa" data-caracteristica="' +
                grupo.caracteristica +
                '"><i class="glyphicon glyphicon-eye-open"></i></a></td></tr>';

              $("#grupos_ocultos_holder").append(row_grupo);
            }
          }
        }

        _this.recalc_sticky_kit();
      }
    );
  },

  refresh_tags: function (id, show_todos, filter_options, selected) {
    if (selected == undefined) selected = null;

    id = id.replace(".", "\\.");

    $("#response-ajax").css("display", "block");

    var usuario_selecionado = $("#atribuido_para").val();

    if (filter_options == true) {
      var url =
        this.base_url +
        "atribuir_grupo/ajax_get_tags/filter/" +
        usuario_selecionado;
    } else {
      var url =
        this.base_url +
        "atribuir_grupo/ajax_get_tags/all/" +
        usuario_selecionado;
    }

    // Loading state
    $(id).attr("disabled", "disabled").html("<option>Carregando...</option>");

    const _this = this;

    $.get(url, function (data) {
      var data = JSON.parse(data);

      $(id).empty();

      if (show_todos === true) {
        $(id).append('<option selected value="">Todos</option>');
      } else {
        $(id).append(
          "<option disabled selected>Selecione o pré-agrupamento</option>"
        );
      }

      var cliente_total_tag =
        data["cliente"] != undefined
          ? data["cliente"].total_itens_pendentes
          : 0;
      var becomex_total_tag =
        data["becomex"] != undefined
          ? data["becomex"].total_itens_pendentes
          : 0;

      opt_selected = selected == "cliente" ? "selected" : null;
      $(id).append(
        '<option value="cliente" ' +
          opt_selected +
          ">CLIENTE – Dúvida no item (" +
          cliente_total_tag +
          " itens)</option>"
      );

      opt_selected = selected == "becomex" ? "selected" : null;
      $(id).append(
        '<option value="becomex" ' +
          opt_selected +
          ">BECOMEX – Grupo tarifário não encontrado (" +
          becomex_total_tag +
          " itens)</option>"
      );

      $.each(data, function (tag, item) {
        // TAG: Cliente e Becomex
        if (item.tag == "cliente" || item.tag == "becomex") {
          return true;
        }

        var perc = Math.round(item.perc * 100) / 100;

        opt_selected = selected == item.tag ? "selected" : null;

        if (filter_options == true) {
          if (item.tag != null && item.tag != "" && item.perc < 100) {
            $(id).append(
              '<option value="' +
                item.tag +
                '" ' +
                opt_selected +
                ">" +
                item.tag +
                " (" +
                perc +
                "% identificado) </option>"
            );
          }
        } else {
          if (item.tag != null && item.tag != "") {
            $(id).append(
              '<option value="' +
                item.tag +
                '" ' +
                opt_selected +
                ">" +
                item.tag +
                " (" +
                perc +
                "% identificado) </option>"
            );
          }
        }
      });

      $("#response-ajax").css("display", "none");

      $(id).attr("disabled", false);
      $(xls_log.submitbtn).attr("disabled", false);

      if (selected != null && selected != "") _this.selected_tag($(id));
      $("#select_tag").selectpicker("refresh");
    });
  },

  open_modal_change_tag: function () {
    $("#ajax_validate_modal").html("");
    $("#modal_table").empty();

    atribuir_grupos.refresh_tags("#new_tag", false, false);

    var itens = [];

    var checked_itens = $('input[type="checkbox"][name="item[]"]:checked');

    const _this = this;

    $(checked_itens).each(function () {
      var item = _this.itens_found[$(this).val()];

      tr =
        "<tr>" +
        '<td><input type="checkbox" id="item_selected" name="item-new-tag[]" value="' +
        _this.encodeHTML(item.part_number) +
        '" data-estabelecimento="' +
        item.estabelecimento +
        '" checked></td>' +
        '<td style="width: 15%">' +
        item.part_number +
        "</td>" +
        "<td>" +
        limitarTexto(item.descricao, 255) +
        "</td>" +
        "</tr>";

      $("#modal_table").append(tr);

      itens.push($(this).val());
    });
  },

  save_tag: function () {
    var itens = [];

    var total_itens = $('input[type="checkbox"][name="item-new-tag[]"]');
    var checked_itens = $(
      'input[type="checkbox"][name="item-new-tag[]"]:checked'
    );

    var motivo = $("#motivo").val();

    if ($(checked_itens).length == 0) {
      $("#ajax_validate_modal").html(
        '<div class="alert alert-danger"><strong>Oops!</strong> Selecione os itens.</div>'
      );
    } else if (!motivo) {
      $("#ajax_validate_modal").html(
        '<div class="alert alert-danger"><strong>Oops!</strong> A observação é uma informação obrigatória.</div>'
      );
    } else if (motivo.length < 10) {
      $("#ajax_validate_modal").html(
        '<h5 class="alert alert-danger"><strong>Oops!</strong> É necessário que a observação tenha no mínimo 10 caracteres.</h5>'
      );
    } else if (motivo.length > 3000) {
      $("#ajax_validate_modal").html(
        '<h5 class="alert alert-danger"><strong>Oops!</strong> É necessário que a observação tenha no máximo 3000 caracteres.</h5>'
      );
    } else if (
      $("#novo-agrupamento").is(":checked") &&
      $("#novo-agrupamento").prop("checked") &&
      $("#descricao-tag").val() == ""
    ) {
      $("#ajax_validate_modal").html(
        '<h5 class="alert alert-danger"><strong>Oops!</strong> Informe a descrição da nova TAG.</h5>'
      );
    } else {
      const _this = this;

      if (
        $("#novo-agrupamento").is(":checked") &&
        $("#novo-agrupamento").prop("checked")
      ) {
        new_tag = $("#descricao-tag").val();
      } else {
        new_tag = $("#new_tag").val();
      }

      var err_count = 0;

      if (new_tag == null) {
        $("#ajax_validate_modal").html(
          '<div class="alert alert-danger"><strong>Oops!</strong> Selecione o novo pré-agrupamento.</div>'
        );
        return;
      }

      $(checked_itens).each(function (i) {
        part_number = $(this).val();
        part_number = part_number.replace(".", "\\.");

        chk_box = $("body").find("[data-part-number='" + part_number + "']");
        $(chk_box).prop("checked", false);

        estabelecimento = $(this).attr("data-estabelecimento");

        $.post(
          _this.base_url + "atribuir_grupo/alterar_tag",
          {
            item: $(this).val(),
            estabelecimento: estabelecimento,
            tag: new_tag,
            motivo: motivo,
          },
          function (data) {
            if (data != "success") {
              err_count++;
            }
          }
        );
      });

      if (err_count == 0) {
        if (total_itens.length - checked_itens.length == 0) {
          $("#modal_change_tag").modal("hide");

          // this.get_itens(false);
          
          swal("Sucesso", "Pré-agrupamento alterado com sucesso.", "success");

          if (!this.tag) {
            this.refresh_tags("#select_tag", true, true);
          }
        } else {
          this.open_modal_change_tag();
          this.refresh_tags("#select_tag", true, true);
        }

        $("#ajax_validate").html(
          '<div class="alert alert-success"><strong>OK!</strong> Pré-agrupamento alterado com sucesso.</div>'
        );
      }
    }
  },

  change_order: function (id, order_field) {
    if ($(id).attr("name") == order_field && $(id).attr("value") == "asc") {
      $(id).attr("value", "desc");
    } else {
      $(id).attr("name", order_field);
      $(id).attr("value", "asc");
    }
  },

  select_all: function (select_from, element, event) {
    $(select_from)
      .find('input[name="' + element + '[]"]')
      .prop("checked", event.target.checked)
      .trigger("change");
  },

  quote: function (str) {
    return str.replace(/['"]+/g, "");
  },

  encodeHTML: function (s) {
    return s
      .split("&")
      .join("&amp;")
      .split("<")
      .join("&lt;")
      .split('"')
      .join("&quot;")
      .split("'")
      .join("&#39;");
  },

  selected_tag: function (element) {
    tag = $(element).val();
    this.tag = tag;
    this.selected_tags = [];

    this.get_itens();

    if (tag) {
      this.refresh_hidden();
      this.get_grupos("caracteristica");
    } else {
      this.clear_grupos();
    }
  },

  clear_tag: function () {
    this.tag = null;
  },

  clear_grupos: function () {
    $("#accordion_grupos").empty();
  },

  selected_group: function (element) {
    $(".info-grupo").css("display", "none");
    $("#info-grupo-" + $(element).val()).css("display", "block");
    $(".obs-grupo").css("display", "none");
    $("#obs-grupo-" + $(element).val()).css("display", "block");

    this.recalc_sticky_kit();
  },

  update_countdown: function (id_element, id_counter) {
    var remaining = 3000 - $("#" + id_element).val().length;
    $("#" + id_counter).text(remaining + " caracteres restantes.");
  },

  countCharacters: function (elementId, counterId) {
    const element = $("#" + elementId);
    const counter = $("#" + counterId);
    const count = element.val().length;

    if (count > 1) {
      counter.text(count + " caracteres");
    } else {
      counter.text(count + " caractere");
    }
  },

  update_results: function () {
    var info = $(".screen-info");
    var total_itens = $("#itens_holder").find("td > input").length;
    var total_selected = $("#itens_holder").find("td > input:checked").length;

    if (typeof total_itens != "undefined") {
      $(info).find(".total_itens").html(parseInt(total_itens));
    }

    if (typeof total_selected != "undefined") {
      $(info).find(".total_selected").html(parseInt(total_selected));
    }

    $(info).css("bottom", 0);

    if ($(document).height() <= $(window).height()) {
      $(info).css("bottom", "50px");
    }

    $(info).fadeIn("fast");
  },

  selected_item: function () {
    if (this.tag == "" || this.tag == null) {
      if (this.group_request) {
        this.group_request.abort();
      }

      var checked_itens = $('input[type="checkbox"][name="item[]"]:checked');
      var selected_tags = [];

      $(checked_itens).each(function (i) {
        tag = $(checked_itens[i]).attr("data-tag");
        if ($.inArray(tag, selected_tags) == -1) {
          selected_tags.push(tag);
        }
        tag = null;
      });

      this.selected_tags = selected_tags;

      this.get_grupos("caracteristica");
      this.refresh_hidden();
    }

    this.update_results();
  },

  user_transfer: function () {
    var btn = $("#form_transfer_submit").button("loading");

    $("#message_user_transfer").empty();

    var itens = [];

    var checked_itens = $('input[type="checkbox"][name="item[]"]:checked');

    $(checked_itens).each(function () {
      var item = {
        part_number: $(this).val(),
        estabelecimento: $(this).attr("data-estabelecimento"),
      };

      itens.push(item);
    });

    var tipo_responsavel = $(
      "#form_transferir_usuario #tipo_responsavel"
    ).val();
    var id_usuario = $("#form_transferir_usuario #id_usuario").val();
    var motivo = $("#form_transferir_usuario #motivo").val();

    var post_data = {
      itens: itens,
      id_usuario: id_usuario,
      motivo: motivo,
      tipo_responsavel: tipo_responsavel,
    };

    const _this = this;

    $.post(
      this.base_url + "atribuir_grupo/transferir_usuario",
      post_data,
      function (response) {
        var response = JSON.parse(response);

        if (response.error) {
          $("#message_user_transfer").append(response.error);
          btn.button("reset");
        } else if (response.success) {
          _this.refresh_users_filter();
          setTimeout(function () {
            $("#ajax_validate").empty();
            $("#ajax_validate").append(response.success);
            $("#transferencia-modal").modal("hide");
            btn.button("reset");
          }, 3000);
        }
      }
    );
  },

  refresh_users_filter: function () {
    $("#response-ajax").css("display", "block");

    var el = $("#atribuido_para");
    $(el).empty();

    $.get(
      this.base_url + "atribuir_grupo/ajax_get_users_filters",
      function (res) {
        $(el).append(res);
        $("#atribuido_para").selectpicker("refresh");
        $("#response-ajax").css("display", "none");
      }
    );
  },

  refresh_total: function () {
    $("#message_user_transfer").empty();
    var checked_itens = $('input[type="checkbox"][name="item[]"]:checked');
    $("#total_itens").text(checked_itens.length);
  },

  recalc_sticky_kit: function () {
    if (
      $("#column_list_right, #column_list_left").data("sticky_kit") === true
    ) {
      $(document.body).trigger("sticky_kit:recalc");
    } else {
      $("#column_list_right, #column_list_left").stick_in_parent();
      $("#column_list_right, #column_list_left")
        .on("sticky_kit:bottom", function (e) {
          $(this).parent().css("position", "static");
        })
        .on("sticky_kit:unbottom", function (e) {
          $(this).parent().css("position", "relative");
        });
    }
  },

  create_group_structure: function (caracteristica, idx, data) {
    const _this = this;
    var accordion = $("#accordion_grupos");

    if (data == undefined) {
      data = {
        memoria_classificacao: "",
        subsidio: "",
      };
    }

    var table =
      '<table class="table table-striped">' +
      "    <thead>" +
      '        <tr style="height: 51px;">' +
      '            <th style="width: 5%">#</th>' +
      '            <th style="width: 85%"><a href="javascript:void(0);" class="grupo_order_descricao">Descrição</a><a href="javascript:void(0);" class="grupo_order_ncm_recomendada pull-right">NCM Recomendada</a></th>' +
      "            <th></th>" +
      "        </tr>" +
      "    </thead>" +
      '    <tbody id="grupos_holder_' +
      idx +
      '" data-subsidio="' +
      data.subsidio +
      '" data-memoria-classificacao="' +
      data.memoria_classificacao +
      '" data-caracteristica="' +
      caracteristica +
      '"></tbody>' +
      "</table>";

    var panel =
      '<div class="panel panel-default">' +
      '    <div class="panel-heading" role="tab" id="heading-' +
      idx +
      '">' +
      '        <h4 class="panel-title">' +
      '             <a role="button" data-toggle="collapse" data-parent="#accordion_grupos" href="#collapse-' +
      idx +
      '" aria-expanded="true" aria-controls="collapse-' +
      idx +
      '">' +
      "               " +
      caracteristica +
      "             </a>" +
      "        </h4>" +
      "    </div>" +
      '    <div id="collapse-' +
      idx +
      '" data-caracteristica="' +
      caracteristica +
      '" class="panel_carac_grupos panel-collapse collapse" role="tabpanel" aria-labelledby="heading-' +
      idx +
      '">' +
      '         <div class="panel-body" id="panel_carac_' +
      idx +
      '"></div>' +
      "    </div>" +
      "</div>";

    $(accordion).append(panel);
    $("#panel_carac_" + idx).append(table);

    $("#collapse-" + idx).on(
      "shown.bs.collapse hidden.bs.collapse",
      function () {
        _this.recalc_sticky_kit();
      }
    );
  },

  fill_groups: function (data, table_body_id) {
    let tipo = $("#filter_selected option:selected").val();
    let total_uso = null;

    $("#" + table_body_id).empty();

    if (data.grupos == null) {
      $("#" + table_body_id).html(
        '<tr><td class = "grupo_tarifario_table" colspan="3" class="text-center">Nenhum grupo encontrado</td></tr>'
      );
    } else {
      for (i = 0; i < data.grupos.length; i++) {
        grupo = data.grupos[i];

        if (tipo == "id_grupo_tarifario") {
          total_uso =
            '<td class = "grupo_tarifario_table">' +
            grupo.total_grupo_tarifario +
            "</td>";
        }

        row_grupo =
          '<tr id="tr-grupo-' +
          grupo.id_grupo_tarifario +
          '" data-caracteristica="' +
          grupo.caracteristica +
          '" data-subsidio="' +
          grupo.subsidio +
          '" data-memoria-classificacao="' +
          grupo.memoria_classificacao +
          '">' +
          '<td class = "grupo_tarifario_table"><input class="form-check-input" type="radio" id="grupo_selected" name="grupo[]" value="' +
          grupo.id_grupo_tarifario +
          '"></td>' +
          "<td class = 'grupo_tarifario_table'>" +
          grupo.descricao +
          '<div class="info-grupo" id="info-grupo-' +
          grupo.id_grupo_tarifario +
          '" style="display: none;">' +
          "<strong>NCM Recomendada: </strong>";

        if (!grupo.ncm_recomendada) {
          row_grupo += "N/A";
        } else {
          row_grupo +=
            '<a class="display-ncm-table" id="ncm_info" data-id-grupo="' +
            grupo.id_grupo_tarifario +
            '" data-ncm-recomendada="' +
            grupo.ncm_recomendada +
            '" data-toggle="ncm-recomendada-' +
            grupo.ncm_recomendada +
            '" href="javascript: void(0)">' +
            '<i class="glyphicon glyphicon-info-sign"></i> ' +
            grupo.ncm_recomendada +
            "</a>";
        }

        row_grupo +=
          "</div>" +
          '<div class="obs-grupo" id="obs-grupo-' +
          grupo.id_grupo_tarifario +
          '" style="display: none;">' +
          "<strong>Observação: </strong>";

        if (!grupo.observacao) {
          row_grupo += "N/A";
        } else {
          row_grupo +=
            '<a class="show-obs" href="javascript: void(0)">' +
            '<i class="glyphicon glyphicon-info-sign"></i></a>' +
            '<div style="display: none" class="obs-content">' +
            grupo.observacao +
            "</div>";
        }

        row_grupo +=
          "</div></td>" +
          total_uso +
          '<td style="text-align: center;" class = "grupo_tarifario_table"><a href="javascript: void(0)" class="hide_group" id="hide_group_' +
          grupo.id_grupo_tarifario +
          '" data-toggle="tooltip" data-placement="top" title="Clique para ocultar temporariamente o Grupo Tarifário"><i class="glyphicon glyphicon-eye-close"></i></a></td></tr>';

        $("#" + table_body_id).append(row_grupo);
      }
    }
  },

  safe_submit: function (force) {
    var block = $("#btn-atribuir-block");

    if (
      !$(".item_selected").is(":checked") ||
      !$('input[name="grupo[]"]').is(":checked") ||
      force == true
    ) {
      $(block).find("button").prop("disabled", "disabled");
      $(block).find(".tooltip-wrapper").tooltip();
    } else {
      $(block).find("button").prop("disabled", false);
      $(block).find(".tooltip-wrapper").tooltip("destroy");
    }
  },
  loadDefault: function (element, url) {
    let values = $(".item_selected:checked")
      .map(function () {
        return $(this).val();
      })
      .get();

    if (values.length == 1) {
      let estabelecimento = $(".item_selected:checked").data("estabelecimento");
      var data;
      data = {
        part_number: values.shift(),
        estabelecimento: estabelecimento,
      };
    } else {
      data = values;
    }

    element.html('<div class="text-center">Carregando...</div>');
    var postData = {
      ncm: $("#suframa-produto-ncm").val(),
      idGrupoTarifario: $("#grupo_selected:checked").val(),
      dataItem: data,
    };

    $.post(url, postData, function (data) {
      element.html(data.response);
    });
  },
  loadNve: function () {
    var elemtAbaNve = $("#aba-nve");
    var url = this.base_url + "atribuir_grupo/ajax_get_nve";

    this.loadDefault(elemtAbaNve, url);
  },
  loadLi: function () {
    var elemtAbaLi = $("#aba-li");
    var url = this.base_url + "atribuir_grupo/ajax_get_li";

    this.loadDefault(elemtAbaLi, url);
  },
  loadExIi: function () {
    var elemtAbaExIi = $("#aba-ex-ii");
    var url = this.base_url + "atribuir_grupo/ajax_get_ex_ii";

    this.loadDefault(elemtAbaExIi, url);
  },
  loadExIpi: function () {
    var elemtAbaExIpi = $("#aba-ex-ipi");
    var url = this.base_url + "atribuir_grupo/ajax_get_ex_ipi";

    this.loadDefault(elemtAbaExIpi, url);
  },
  loadAntidumping: function () {
    var elemtAbaAntidumping = $("#aba-antidumping");
    var url = this.base_url + "atribuir_grupo/ajax_get_antidumping";

    this.loadDefault(elemtAbaAntidumping, url);
  },
  loadClassificacaoEnergetica: function () {
    var elemtAbaEnergetica = $("#aba-energetica");
    var url =
      this.base_url + "atribuir_grupo/ajax_get_classificacao_energetica";

    this.loadDefault(elemtAbaEnergetica, url);
  },
  loadAtributos: function () {
    var elementAtributos = $("#aba-atributos");
    var url = this.base_url + "atribuir_grupo/ajax_get_atributos";

    this.loadDefault(elementAtributos, url);

    window.setTimeout(function () {
      $("#save_association").prop("disabled", false);
      $(".loading").hide();
    }, 500);
  },
  loadPerguntasRespostas: function () {
    var elementPerguntasRespostas = $("#aba-perguntas-respostas");
    var url = this.base_url + "atribuir_grupo/ajax_get_perguntas_respostas";

    this.loadDefault(elementPerguntasRespostas, url);
  },
  loadRules: function (selectedTab = "#dados") {
    if (selectedTab == "#aba-nve") {
      atribuir_grupos.loadNve();
    }
    if (selectedTab == "#aba-li") {
      atribuir_grupos.loadLi();
    }
    if (selectedTab == "#aba-ex-ii") {
      atribuir_grupos.loadExIi();
    }
    if (selectedTab == "#aba-ex-ipi") {
      atribuir_grupos.loadExIpi();
    }
    if (selectedTab == "#aba-antidumping") {
      atribuir_grupos.loadAntidumping();
    }
    if (selectedTab == "#aba-energetica") {
      atribuir_grupos.loadClassificacaoEnergetica();
    }
    if (selectedTab == "#aba-atributos") {
      atribuir_grupos.loadAtributos();
    }
    if (selectedTab == "#aba-perguntas-respostas") {
      atribuir_grupos.loadPerguntasRespostas();
    }
  },
};

/* XLS Log */
xls_log = {
  form: "",
  submitbtn: "",
  timer: "",

  init: function () {
    this.form = $("form#generate_log");
    // this.submitbtn = $(this.form).find('[type="submit"]');

    this.submitbtn = $("#send_generate_log");

    $(this.submitbtn).on("click", function (e) {
      $("form#generate_log").attr("action", "/atribuir_grupo/log_xls");
      $("form#generate_log").submit();
    });

    $(this.form).on("submit", function (e) {
      xls_log.download();
    });
  },

  download: function (url) {
    $(xls_log.submitbtn).button("loading");
    $("#loading-overlay").show();
    var token = $(xls_log.form).find('[name="download_token"]').val();

    window.setTimeout(function () {
      $.ajax({
        url: "atribuir_grupo/ajax_xls_log_status",
        method: "post",
        data: { token: token },
        success: function (e) {
          xls_log.finish();
          $("#loading-overlay").hide();
        },
      });
    }, 1000);
  },

  finish: function () {
    $(this.submitbtn).button("reset");
  },
};

/* XLS Multipaises */
xls_multipaises = {
  form: "",
  submitbtn: "",
  timer: "",

  init: function () {
    this.form = $("form#generate_log");
    // this.submitbtn = $(this.form).find('[type="submit"]');

    this.submitbtn = $("#send_multipaises_log");

    $(this.submitbtn).on("click", function (e) {
      $("form#generate_log").attr("action", "/atribuir_grupo/xls_multipaises");
      $("form#generate_log").submit();
    });

    $(this.form).on("submit", function (e) {
      xls_multipaises.download();
    });
  },

  download: function (url) {
    $("#loading-overlay").show();
    $(xls_multipaises.submitbtn).button("loading");

    var token = $(xls_multipaises.form).find('[name="download_token"]').val();

    window.setTimeout(function () {
      $.ajax({
        url: "atribuir_grupo/ajax_xls_log_status_multipaises",
        method: "post",
        data: { token: token },
        success: function (e) {
          xls_multipaises.finish();
          $("#loading-overlay").hide();
        },
      });
    }, 1000);
  },

  finish: function () {
    $(this.submitbtn).button("reset");
  },
};

$(function () {
  $("body").tooltip({
    selector: '[data-toggle="tooltip"]',
  });

  $("#item_input").focus();
});

jQuery(document).ready(function () {
  var mudouNcm = "";
  xls_log.init();
  xls_multipaises.init();

  $("body").on("click", ".click-select", function (e) {
    if (e.target.nodeName != "INPUT") {
      $(this).find("input").click();
    }
  });

  $("body").on("click", "#btn_preencher_desc_resumida", function () {
    var descricaoStr = $("textarea#descricao").val();

    $("textarea#descricao_mercado_local").val(
      descricaoStr
        .normalize("NFD")
        .replace(/[\u0300-\u036f]/g, "")
        .toUpperCase()
    );

    $("#preencher_desc_resumida").val("1");
  });

  $("#accordion_grupos, #diana").on(
    "click",
    'input[type="radio"], .vertical-middle',
    function () {
      let element = $(this).hasClass("form-check-input")
        ? $(this).parent().parent()
        : $(this).parent();

      var ncmRecomendada = element
        .find(".display-ncm-table")
        .attr("data-ncm-recomendada");

      if (
        ncmRecomendada == "" ||
        !ncmRecomendada ||
        ncmRecomendada == undefined
      ) {
        ncmRecomendada = element.find(".ncm_info").attr("data-attr-ncm");
      }

      $("#suframa-produto-ncm").val(ncmRecomendada);
    }
  );

  $(
    ".vertical-middle .form-check-input, .vertical-middle, .form-check-input"
  ).on("click", function () {
    var ncmRecomendada = $(this)
      .parent()
      .parent()
      .find(".display-ncm-table")
      .attr("data-ncm-recomendada");

    if (
      ncmRecomendada == "" ||
      !ncmRecomendada ||
      ncmRecomendada == undefined
    ) {
      ncmRecomendada = $(this)
        .parent()
        .parent()
        .find(".ncm_info")
        .attr("data-attr-ncm");
    }

    $("#suframa-produto-ncm").val(ncmRecomendada);
  });

  $("body").on("click", ".item_selected", function () {
    $(".vertical-middle .form-check-input").prop("checked", false);
  });

  $("#modal_motivo").on("show.bs.modal", function (e) {
    var grupo = $('input[name="grupo[]"]:checked').length;
    var itens = $(".item_selected:checked").length;
    var itens_checked = [];

    var checked_itens = $(".item_selected:checked");
    $(checked_itens).each(function () {
      var item = {
        part_number: $(this).val(),
        ncm: $(this).attr("data-ncm_item"),
        estabelecimento: $(this).attr("data-estabelecimento"),
      };

      itens_checked.push(item);
    });

    // $.ajax({
    // 	url: base_url + "atribuir_grupo/get_part_number",
    // 	data: {
    // 		part_number: $('.item_selected:checked').attr("data-part-number"),
    // 		estabelecimento: $('.item_selected:checked').attr("data-estabelecimento")
    // 	},
    // 	async: false,
    // 	method: 'GET',
    // 	success: function (resp) {
    //         if (resp == '"EI"')
    //         {
    //              var elementoA = document.querySelector('li[role  ="presentation"] a[href="#aba-atributos"]');
    // 			 elementoA.style.display = 'block';
    // 			 var elementoRemover = document.getElementById('aba-atributos');
    // 			 if (elementoRemover) {
    // 				 elementoRemover.style.display = '';
    // 			 }

    //         } else if (resp != 'sem_ecomex'){

    // 			var elementoRemover = document.getElementById('aba-atributos');
    // 			if (elementoRemover) {
    // 				elementoRemover.style.display = 'none';
    // 			}

    //             var elementoA = document.querySelector('li[role  ="presentation"] a[href="#aba-atributos"]');
    // 		    elementoA.style.display = 'none';

    // 			var elementoA = document.querySelector('a[href="#dados"]');
    // 			if (elementoA) {
    // 				elementoA.click();
    // 			}

    //         }
    // 	}
    // })

    $.ajax({
      url: base_url + "cadastros/empresa/check_pn_divergente",
      data: {
        grupo: grupo,
        itens: itens_checked,
        part_number: $(".item_selected:checked").attr("data-part-number"),
        estabelecimento: $(".item_selected:checked").attr(
          "data-estabelecimento"
        ),
        ncm: $("#suframa-produto-ncm").val(),
      },
      async: false,
      method: "POST",
      success: function (item_checked) {
        var ncm_divergente = [];
        var item = [];
        for (i = 0; i < item_checked.length; i++) {
          if (item_checked[i].length > 0) {
            item_checked[i].forEach(function (nome, i) {
              item.ncm = nome.ncm;
              item.estabelecimento = nome.estabelecimento;
              ncm_divergente.push({
                ncm: item.ncm,
                estabelecimento: item.estabelecimento,
              });
            });
          }
        }
        if (ncm_divergente.length > 0) {
          message_alert(ncm_divergente);
        }
      },
    });

    if (!itens || !grupo) {
      swal(
        "Atenção!",
        "Nenhum " + (!itens ? "item" : "grupo tarifário") + " foi selecionado",
        "warning"
      );
      e.preventDefault();
    } else {
      //if (mudouNcm != $("#suframa-produto-ncm").val() || mudouNcm == "") {
      mudouNcm = $("#suframa-produto-ncm").val();

      atribuir_grupos.loadRules("#aba-nve");
      atribuir_grupos.loadRules("#aba-li");
      atribuir_grupos.loadRules("#aba-ex-ii");
      atribuir_grupos.loadRules("#aba-ex-ipi");
      atribuir_grupos.loadRules("#aba-antidumping");
      atribuir_grupos.loadRules("#aba-energetica");
      atribuir_grupos.loadRules("#aba-atributos");
      atribuir_grupos.loadRules("#aba-perguntas-respostas");
      //	}
      $.ajax({
        url: "pr/perguntas/setUsuarioBloqueadorItens",
        data: {
          itens: itens_checked,
        },
        method: "POST",
        success: function (dados) {},
      });
    }
  });
});

$("body").on("click", ".part_number_inf", function (e) {
  var self = $(this);
  $("#detalhes-part-number").modal();
  var post_action = {
    part_number: self.attr("data-part-number"),
    estabelecimento: self.attr("data-estabelecimento"),
  };

  $.post("./atribuir_grupo/ajax_get_item_by_pn", post_action, function (data) {
    $("#detalhes-part-number").html(data);
  });
});

function limitarTexto(texto, limite) {
  if (texto && texto.length > limite) {
    return texto.substring(0, limite) + "(...)";
  } else {
    return texto;
  }
}

function message_alert(data) {
  let ncm = pluck(data, "ncm");
  let estabelecimento = pluck(data, "estabelecimento");
  swal({
    title: "Atenção!",
    text:
      "Um NCM diferente  (" +
      ncm +
      ") foi definido para este mesmo part-number em outro estabelecimento(" +
      estabelecimento +
      "). Deseja continuar?  <br /> ",
    type: "warning",
    confirmButtonText: "OK",
    cancelButtonText: "Cancelar",
    showConfirmButton: true,
    showCancelButton: true,
    allowOutsideClick: false,
  }).then(
    function () {},
    function (dismiss) {
      $("#modal_motivo").modal("hide");
    }
  );
}

function pluck(array, key) {
  return array.map(function (obj) {
    return obj[key];
  });
}
