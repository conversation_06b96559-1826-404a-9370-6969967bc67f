// JS para exibir badges dos filtros selecionados e contador no ícone do modal
$(function () {
  // Função para extrair filtros selecionados do formulário
  function getFiltrosSelecionados() {
    let filtros = [];
    $("#filterForm")
      .find("input, select")
      .each(function () {
        let $el = $(this);
        let name = $el.attr("name");
        let type = $el.attr("type");
        let label = $el.closest(".form-group").find("label").text();
        let value = $el.val();
        if (type === "checkbox") {
          if ($el.is(":checked")) {
            filtros.push({ name: name, label: label, value: "Sim" });
          }
        } else if ($el.is("select")) {
          let selected = $el
            .find("option:selected")
            .map(function () {
              return $(this).text();
            })
            .get();
          if (
            selected.length &&
            selected[0] !== "" &&
            selected[0] !== "Selecione..."
          ) {
            filtros.push({
              name: name,
              label: label,
              value: selected.join(", "),
            });
          }
        } else if (type === "date") {
          if (value) {
            filtros.push({ name: name, label: label, value: value });
          }
        } else if (type === "text") {
          if (value) {
            filtros.push({ name: name, label: label, value: value });
          }
        }
      });
    return filtros;
  }

  // Renderiza os badges dos filtros selecionados
  function renderBadges() {
    let filtros = getFiltrosSelecionados();
    let $container = $("#active-filters");
    $container.empty();
    filtros.forEach(function (filtro) {
      const texto = filtro.label + ": " + filtro.value;
      const textoCurto =
        texto.length > 32 ? texto.substring(0, 29) + "..." : texto;
      $container.append(
        '<span class="badge badge-primary mr-1 d-flex align-items-center" style="max-width:220px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;">' +
          '<span class="badge-text" title="' +
          texto.replace(/"/g, "&quot;") +
          '" style="flex:1;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;">' +
          textoCurto +
          "</span>" +
          '<span class="badge badge-light badge-remove ml-1" data-name="' +
          filtro.name +
          '" style="cursor:pointer;">&times;</span>' +
          "</span>"
      );
    });
    // Atualiza contador no ícone do modal
    let $countBadge = $("#filter-count-badge");
    if (filtros.length > 0) {
      $countBadge.text(filtros.length).show();
    } else {
      $countBadge.hide();
    }
  }

  // Atualiza badges ao aplicar filtros (removido o evento de abertura do modal)
  $("#filterForm")
    .off("change.filter-badges")
    .on("change.filter-badges", "input, select", renderBadges);
  // Novo comportamento do botão Filtrar do modal
  $("#btn-filtrar-modal")
    .off("click.filter-badges")
    .on("click.filter-badges", function (e) {
      e.preventDefault();

      // Chamar o método de submissão dos filtros
      if (typeof FilterModal !== "undefined" && FilterModal.submitFilters) {
        FilterModal.submitFilters();
      } else {
        // Fallback: fechar modal e renderizar badges
        $("#filterModal").modal("hide");
        renderBadges();
      }
    });

  // Limpar todos os filtros
  $("#btn-clear-filters")
    .off("click.filter-badges")
    .on("click.filter-badges", function () {
      // Limpar o formulário
      $("#filterForm")[0].reset();
      $("#filterForm").find("select").selectpicker("deselectAll");
      $("#filterForm").find("select").selectpicker("refresh");
      $("#filterForm").find("input[type='date']").val("");
      $("#filterForm").find("input[type='text']").val("");
      $("#filterForm").find("input[type='checkbox']").prop("checked", false);

      // Limpar filtros salvos no backend (delegar para FilterModalIntegration)
      if (
        typeof FilterModalIntegration !== "undefined" &&
        FilterModalIntegration.clearSavedFilters
      ) {
        FilterModalIntegration.clearSavedFilters();
      }

      // Limpar cache de options do modal para garantir dados atualizados
      if (typeof FilterModal !== "undefined" && FilterModal.clearOptionsCache) {
        FilterModal.clearOptionsCache();
      }

      renderBadges();

      // Mostrar loading e recarregar página com reset_filters
      if ($("#loading-overlay").length) {
        $("#loading-overlay").show();
      }

      // Redirecionar para a mesma página com parâmetro reset_filters
      window.location.href = base_url + "atribuir_grupo?reset_filters=1";
    });

  // Remover filtro individual ao clicar no X do badge
  $("#active-filters")
    .off("click.filter-badges")
    .on("click.filter-badges", ".badge-remove", function (e) {
      e.preventDefault();
      const filterName = $(this).data("name");

      // 1. Limpar campo(s) correspondente(s) no formulário (se existir)
      const $form = $("#filterForm");
      if ($form.length) {
        // Tentar nome exato
        let $fields = $form.find('[name="' + filterName + '"]');
        // Tentar também com [] (arrays) caso não tenha encontrado
        if ($fields.length === 0) {
          $fields = $form.find('[name="' + filterName + '[]"]');
        }

        $fields.each(function () {
          const $el = $(this);
          if ($el.is("select")) {
            // Multi ou single
            $el.val([]); // zera multi ou single
            if ($el.hasClass("selectpicker")) $el.selectpicker("refresh");
          } else if ($el.is(":checkbox")) {
            $el.prop("checked", false);
          } else if ($el.is("input") || $el.is("textarea")) {
            $el.val("");
          }
        });
      }

      // 2. Limpar em FilterModalIntegration (estado salvo)
      if (
        typeof FilterModalIntegration !== "undefined" &&
        FilterModalIntegration.getSavedFilters &&
        FilterModalIntegration.updateWindowVariables
      ) {
        const current = FilterModalIntegration.getSavedFilters();
        if (Object.prototype.hasOwnProperty.call(current, filterName)) {
          // Definir valor vazio apropriado
          let clearedValue = "";
          // Arrays tratados como []
          if (Array.isArray(current[filterName])) clearedValue = [];
          if (filterName === "triagem_diana_falha") clearedValue = null;
          current[filterName] = clearedValue;
          // Atualizar variáveis globais (janela + objeto interno)
          FilterModalIntegration.updateWindowVariables({
            [filterName]: clearedValue,
          });
        }
      } else if (
        typeof FilterModalIntegration !== "undefined" &&
        FilterModalIntegration.getSavedFilters
      ) {
        // Fallback caso updateWindowVariables não exista
        const current = FilterModalIntegration.getSavedFilters();
        if (Object.prototype.hasOwnProperty.call(current, filterName)) {
          current[filterName] = Array.isArray(current[filterName]) ? [] : "";
          if (filterName === "triagem_diana_falha") current[filterName] = null;
        }
      }

      // 3. Remover somente o badge clicado
      const $outerBadge = $(this).closest(
        ".badge.badge-primary.d-flex.align-items-center"
      );
      $outerBadge.remove();

      // 4. Atualizar contador (sem reconstruir todos os badges)
      const remaining = $("#active-filters .badge-remove").length;
      const $countBadge = $("#filter-count-badge");
      if (remaining > 0) {
        $countBadge.text(remaining).show();
      } else {
        $countBadge.hide();
      }
    });

  // Função utilitária para validar se um filtro tem valor válido
  function isValidFilterValue(value) {
    if (Array.isArray(value)) {
      // Para arrays, verificar se há valores não vazios
      const nonEmptyValues = value.filter(
        (v) => v && v !== "" && v !== "-1" && v !== null && v !== undefined
      );
      return nonEmptyValues.length > 0;
    } else {
      // Para valores simples, verificar se não são vazios/nulos
      return (
        value &&
        value !== "" &&
        value !== null &&
        value !== false &&
        value !== "0" &&
        value !== undefined &&
        !Array.isArray(value) // Garantir que não é array vazio
      );
    }
  }

  // Função para renderizar badges baseado nos filtros salvos do backend
  function renderBadgesFromSavedFilters() {
    if (
      typeof FilterModalIntegration === "undefined" ||
      !FilterModalIntegration.getSavedFilters
    ) {
      return false;
    }

    const savedFilters = FilterModalIntegration.getSavedFilters();
    const $container = $("#active-filters");

    // Verificar se há filtros válidos antes de limpar o container
    let hasValidFilters = false;
    Object.keys(savedFilters).forEach(function (filterName) {
      const value = savedFilters[filterName];
      if (isValidFilterValue(value)) {
        hasValidFilters = true;
      }
    });

    // Se não há filtros válidos, não renderizar nada
    if (!hasValidFilters) {
      return false;
    }

    $container.empty();
    let totalFilters = 0;

    // Mapear nomes dos filtros para labels amigáveis
    const filterLabels = {
      pacotes_eventos: "Pacotes / Eventos",
      sistema_origem: "Sistema de origem",
      owner: "Owner",
      prioridade: "Prioridade",
      atribuido_para: "Atribuídos para",
      status_classificacao_fiscal: "Status de classificação fiscal",
      triagem_diana_falha: "Falha na triagem",
      novo_material: "Novo material",
      estabelecimento: "Estabelecimento",
      importado: "Importado",
      farol_sla: "Farol SLA",
      data_criacao_from: "Data de criação (de)",
      data_criacao_to: "Data de criação (até)",
      data_modificacao_from: "Data de modificação (de)",
      data_modificacao_to: "Data de modificação (até)",
      data_importado_from: "Data que tornou-se importado (de)",
      data_importado_to: "Data que tornou-se importado (até)",
    };

    // Processar cada filtro salvo
    Object.keys(savedFilters).forEach(function (filterName) {
      const value = savedFilters[filterName];
      const label = filterLabels[filterName];

      if (!label) return;

      // Usar a função de validação centralizada
      if (!isValidFilterValue(value)) {
        return;
      }

      let displayValue = "";

      if (Array.isArray(value)) {
        // Para arrays válidos, pegar apenas valores não vazios
        const nonEmptyValues = value.filter(
          (v) => v && v !== "" && v !== "-1" && v !== null && v !== undefined
        );
        displayValue = nonEmptyValues.join(", ");
      } else {
        // Para valores simples válidos
        if (filterName === "triagem_diana_falha") {
          displayValue = "Sim";
        } else {
          displayValue = value;
        }
      }

      // Se chegou até aqui, o filtro é válido e deve ser exibido
      const texto = label + ": " + displayValue;
      const textoCurto =
        texto.length > 32 ? texto.substring(0, 29) + "..." : texto;

      $container.append(
        '<span class="badge badge-primary mr-1 d-flex align-items-center" style="max-width:220px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;">' +
          '<span class="badge-text" title="' +
          texto.replace(/"/g, "&quot;") +
          '" style="flex:1;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;">' +
          textoCurto +
          "</span>" +
          '<span class="badge badge-light badge-remove ml-1" data-name="' +
          filterName +
          '" style="cursor:pointer;">&times;</span>' +
          "</span>"
      );
      totalFilters++;
    });

    // Atualizar contador no ícone do modal
    const $countBadge = $("#filter-count-badge");
    if (totalFilters > 0) {
      $countBadge.text(totalFilters).show();
    } else {
      $countBadge.hide();
    }

    return totalFilters > 0;
  }

  // Inicializa badges ao carregar página
  // Primeiro tenta com filtros salvos, depois com renderBadges normal se necessário
  setTimeout(function () {
    const hasRenderedFromSaved = renderBadgesFromSavedFilters();
    // Se não conseguiu renderizar com filtros salvos, usar método normal (mas só se realmente necessário)
    if (!hasRenderedFromSaved) {
      // Não renderizar badges vazias - deixar container vazio
      $("#active-filters").empty();
      $("#filter-count-badge").hide();
    }
  }, 200);
});
