# DIANA - Melhorias de Performance e Controle de Execução

## Resu<PERSON> das Melhorias Implementadas

Este documento descreve as melhorias implementadas na cron de análise DIANA para resolver problemas de performance e evitar execuções simultâneas.

### 1. **Controle de Execução Simultânea**

#### Tabela de Controle: `cron_diana_execucao`
- **Localização**: Migration `999_create_cron_diana_execucao.php`
- **Função**: Registra todas as execuções da cron com status e estatísticas
- **Campos principais**:
  - `status_execucao`: 'iniciada', 'finalizada', 'erro', 'interrompida'
  - `pid_processo`: Process ID para controle de execução
  - `data_inicio` / `data_fim`: Timestamps de controle
  - Estatísticas: total de empresas, itens, sucessos, erros, tempo

#### Model de Controle: `cron_diana_execucao_model.php`
- **Métodos principais**:
  - `verificar_execucao_em_andamento()`: Verifica se há execução rodando
  - `iniciar_execucao()`: Registra início de nova execução
  - `finalizar_execucao()`: Registra fim com estatísticas
  - `get_estatisticas_execucoes()`: Relatórios de performance

### 2. **Limitação de Performance**

#### Limite de Itens por Empresa: 3.000
- **Implementação**: Propriedade `$limite_itens_por_empresa = 3000`
- **Aplicação**: Nos métodos `get_itens_para_triagem_diana_pendente()` e `get_itens_para_triagem_diana_reprovada()`
- **Ordenação**: FIFO (First In, First Out) - itens mais antigos primeiro
- **Distribuição**: Se há itens pendentes e reprovados, o limite é distribuído entre eles

#### Melhorias nos Métodos de Busca
```php
// Antes
public function get_itens_para_triagem_diana_pendente($id_empresa)

// Depois  
public function get_itens_para_triagem_diana_pendente($id_empresa, $limit = 3000)
```

### 3. **Sistema de Logs e Monitoramento**

#### Novos Comandos de Monitoramento
```bash
# Verificar status das execuções
php index.php cli/cron_analise_diana status

# Limpar execuções antigas (>30 dias)
php index.php cli/cron_analise_diana cleanup
```

#### Logs Detalhados
- **Execução em andamento**: Detecta e previne execuções simultâneas
- **Estatísticas por empresa**: Mostra quantos itens foram processados por empresa
- **Limite atingido**: Avisa quando o limite de 3.000 itens é atingido
- **Performance**: Registra tempo de execução e taxa de sucesso

## Como Usar

### 1. **Executar Migration**
```bash
php index.php migrate
```

### 2. **Executar Cron Principal**
```bash
# Via CLI (produção)
php index.php cli/cron_analise_diana

# Via navegador (desenvolvimento)
http://seudominio.com/index.php/cli/cron_analise_diana
```

### 3. **Monitorar Execuções**
```bash
# Verificar status
php index.php cli/cron_analise_diana status

# Testar configuração
php index.php cli/cron_analise_diana test_config

# Testar empresa específica
php index.php cli/cron_analise_diana test_empresa 123
```

### 4. **Configurar Crontab**
```bash
# Executar a cada 30 minutos
*/30 * * * * php /var/www/gestaotarifaria/production/current/index.php cli/cron_analise_diana >> /var/log/diana_cron.log 2>&1

# Limpeza semanal (domingos às 2h)
0 2 * * 0 php /var/www/gestaotarifaria/production/current/index.php cli/cron_analise_diana cleanup >> /var/log/diana_cleanup.log 2>&1
```

## Benefícios das Melhorias

### ✅ **Performance**
- **Limite controlado**: Máximo 3.000 itens por empresa por execução
- **Processamento FIFO**: Itens mais antigos são processados primeiro
- **Distribuição inteligente**: Limite compartilhado entre itens pendentes e reprovados

### ✅ **Confiabilidade**
- **Prevenção de execuções simultâneas**: Sistema de lock baseado em banco de dados
- **Detecção de processos órfãos**: Verifica se processo ainda está rodando via PID
- **Timeout automático**: Execuções antigas (>2h) são marcadas como interrompidas
- **Tratamento de erros**: Captura e registra erros críticos

### ✅ **Monitoramento**
- **Histórico completo**: Todas as execuções são registradas
- **Estatísticas detalhadas**: Performance, taxa de sucesso, tempo médio
- **Logs estruturados**: Fácil identificação de problemas
- **Limpeza automática**: Remove registros antigos para manter performance

### ✅ **Manutenibilidade**
- **Comandos de diagnóstico**: `status`, `cleanup`, `test_config`
- **Logs em tempo real**: Interface visual para desenvolvimento
- **Compatibilidade**: Mantém toda funcionalidade existente
- **Padrão do projeto**: Segue convenções do CodeIgniter e do projeto

## Estrutura de Arquivos Modificados

```
application/
├── controllers/cli/
│   └── cron_analise_diana.php          # ✅ Atualizado
├── models/
│   ├── item_model.php                  # ✅ Atualizado  
│   └── cron_diana_execucao_model.php   # 🆕 Novo
├── migrations/
│   └── 999_create_cron_diana_execucao.php # 🆕 Novo
└── DIANA_PERFORMANCE_IMPROVEMENTS.md   # 🆕 Novo
```

## Próximos Passos Recomendados

1. **Executar migration** para criar tabela de controle
2. **Testar em ambiente de desenvolvimento** com `test_config` e `status`
3. **Monitorar primeiras execuções** para validar performance
4. **Ajustar limite** se necessário (propriedade `$limite_itens_por_empresa`)
5. **Configurar limpeza automática** no crontab
6. **Documentar para equipe** os novos comandos de monitoramento

## Troubleshooting

### Problema: "Execução em andamento"
```bash
# Verificar status
php index.php cli/cron_analise_diana status

# Se processo realmente parou, será marcado automaticamente como interrompido
```

### Problema: Performance ainda lenta
```bash
# Reduzir limite por empresa
# Editar: $limite_itens_por_empresa = 1500; // em vez de 3000
```

### Problema: Muitos registros na tabela
```bash
# Executar limpeza
php index.php cli/cron_analise_diana cleanup
```
