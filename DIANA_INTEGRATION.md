# Integração DIANA - Sistema de Análise de Triagem

Esta documentação descreve a implementação completa da integração com a API DIANA para análise automática de triagem de itens no sistema de Gestão Tarifária.

## Visão Geral

O sistema DIANA (Análise Inteligente de Triagem) é uma solução baseada em IA que analisa automaticamente itens importados para determinar se as informações fornecidas são suficientes para classificação fiscal. O sistema opera em dois passos principais:

### **Passo 1 - Análise Inicial**
- Analisa itens no status "Em análise" (id_status = 6)
- Verifica se descrição, função, aplicação e material constitutivo respondem às 3 perguntas fundamentais
- Se insuficiente, gera perguntas específicas para o cliente

### **Passo 2 - Reavaliação com Respostas**
- Analisa itens com perguntas respondidas (id_status = 8)
- Inclui as respostas do cliente na análise
- Determina aprovação final ou falha definitiva (evita loops infinitos)

### **Perguntas Fundamentais da DIANA**
1. **Qual a função do item (Para que ele serve?)**
2. **Qual a aplicação do item? (Onde ele é utilizado?)**
3. **Qual o material constitutivo do item?**

## Arquivos Implementados

### 1. Biblioteca de Integração - `Diana_api.php`

**Localização**: `application/libraries/Diana_api.php`

**Responsabilidade**: Gerencia toda a comunicação com a API DIANA

**Métodos Principais**:

- **`analisar_triagem($funcao, $aplicacao, $material_constitutivo, $descricao)`**
  - Envia dados básicos para análise (Passo 1)
  - Concatena: `descricao+funcao+aplicacao+material_constitutivo`
  - Permite campos vazios conforme especificação

- **`analisar_triagem_com_respostas($funcao, $aplicacao, $material_constitutivo, $descricao, $perguntas_respostas)`**
  - Envia dados incluindo perguntas e respostas (Passo 2)
  - Concatena: `descricao+funcao+aplicacao+material+pergunta1+resposta1+pergunta2+resposta2+...`
  - Usado para reavaliação de itens com respostas do cliente

- **`processar_resposta($resposta_api)`**
  - Processa retorno da API DIANA
  - Identifica se triagem foi aprovada ou reprovada
  - Extrai perguntas faltantes quando necessário

- **`is_configurada()`**
  - Verifica se URL da API está configurada corretamente
  - Valida formato da URL

**Características Técnicas**:
- Usa query parameter na URL (`?item_descr=...`) conforme API
- Logs detalhados para monitoramento e debug
- Tratamento robusto de erros HTTP e timeouts
- URL encoding automático para caracteres especiais

### 2. Métodos no Model - `item_model.php`

**Localização**: `application/models/item_model.php`

**Métodos Implementados**:

- **`get_itens_para_triagem_diana_pendente($id_empresa)`**
  - Busca itens para Passo 1 (análise inicial)
  - Critérios: status 6 + 'Pendente de triagem' + empresa com funcionalidade ativa
  - Retorna: part_number, estabelecimento, id_empresa, descricao, funcao, aplicacao, material_constitutivo

- **`get_itens_para_triagem_diana_reprovada($id_empresa)`**
  - Busca itens para Passo 2 (reavaliação com respostas)
  - Critérios: status 8 + 'Triagem reprovada' + empresa com funcionalidade ativa
  - Inclui perguntas e respostas DIANA associadas ao item
  - Evita produto cartesiano usando busca em duas etapas

- **`get_perguntas_respostas_diana($part_number, $estabelecimento, $id_empresa)`**
  - Busca perguntas e respostas específicas da DIANA para um item
  - Filtra apenas perguntas com origem = 'DIANA'
  - Retorna array de objetos com pergunta e resposta

- **`update_status_triagem_diana($part_number, $estabelecimento, $id_empresa, $status_triagem, $novo_id_status)`**
  - Atualiza status de triagem DIANA de um item
  - Opcionalmente altera id_status do item

- **`processar_resultado_diana($item, $resultado_analise)`**
  - Processa resultado do Passo 1
  - Se aprovado: mantém status atual + 'Triagem aprovada'
  - Se reprovado: muda para status 7 + 'Triagem reprovada' + insere perguntas

- **`processar_resultado_diana_com_respostas($item, $resultado_analise)`**
  - Processa resultado do Passo 2
  - Se aprovado: mantém status 8 + 'Triagem aprovada'
  - Se reprovado: mantém status 8 + 'Falha na triagem' (evita loop infinito)

- **`inserir_perguntas_diana($item, $perguntas)`**
  - Insere perguntas geradas pela DIANA na tabela ctr_pendencias_pergunta
  - Marca origem como 'DIANA' para identificação
  - Cria relacionamento com responsável

### 3. Cron de Execução - `cron_analise_diana.php`

**Localização**: `application/controllers/cli/cron_analise_diana.php`

**Responsabilidade**: Execução automática da análise DIANA via crontab

**Métodos**:

- **`index()`**
  - Método principal de execução
  - Processa ambos os passos (1 e 2) para cada empresa
  - Gera logs detalhados e estatísticas de execução

- **`test_config()`**
  - Testa configuração da API DIANA
  - Verifica conectividade e configurações
  - Lista itens encontrados para análise

- **`test_item($part_number, $id_empresa, $estabelecimento)`**
  - Testa processamento de um item específico
  - Útil para debug e validação

**Fluxo de Execução**:
1. Busca empresas com funcionalidade `status_triagem_diana` ativa
2. Para cada empresa:
   - **Passo 1**: Processa itens pendentes (status 6)
   - **Passo 2**: Processa itens com respostas (status 8 + triagem reprovada)
3. Gera estatísticas consolidadas

### 4. Configuração - `config.php`

**Localização**: `application/config/config.php`

**Configuração Adicionada**:
```php
/* Integração Status Triagem DIANA */
$config['diana_api_url'] = 'https://hml-triagem-gt.becomex.com.br/process-item-descr/';
```

## Configuração e Instalação

### 1. Configurar URL da API

Edite o arquivo `application/config/config.php` e adicione:

```php
/* Integração Status Triagem DIANA */
$config['diana_api_url'] = 'https://hml-triagem-gt.becomex.com.br/process-item-descr/';
```

**Importante**: A URL deve terminar com `/` e usar o endpoint correto da API DIANA.

### 2. Executar Migrations

Certifique-se de que a migration 259 foi executada para criar o campo `status_triagem_diana`:

```bash
php index.php migration latest
```

Esta migration adiciona o campo `status_triagem_diana` na tabela `item` com os valores:
- `'Pendente de triagem'` (padrão)
- `'Triagem aprovada'`
- `'Triagem reprovada'`
- `'Falha na triagem'`

### 3. Ativar Funcionalidade nas Empresas

Para cada empresa que deve usar o DIANA, adicione `status_triagem_diana` no campo `funcoes_adicionais`:

```sql
UPDATE empresa
SET funcoes_adicionais = CONCAT(IFNULL(funcoes_adicionais, ''), '|status_triagem_diana')
WHERE id_empresa = [ID_DA_EMPRESA];
```

**Verificação**: Use o método `get_empresas_with_status_triagem_diana()` do `empresa_model` para listar empresas ativas.

### 4. Configurar Logs (Opcional)

Para logs mais detalhados, ajuste o nível de log no `config.php`:

```php
$config['log_threshold'] = 3; // 0=Desabilitado, 1=Erro, 2=Debug, 3=Info, 4=Todos
```

### 5. Testar Configuração

Execute o teste de configuração:

```bash
php index.php cli/cron_analise_diana test_config
```

Saída esperada:
```
=== TESTE DE CONFIGURAÇÃO DIANA ===
✓ API DIANA está configurada
URL: https://hml-triagem-gt.becomex.com.br/process-item-descr/
Itens encontrados para análise: X
```

## Fluxo de Funcionamento Detalhado

### 1. Visão Geral do Processo

O sistema DIANA opera em **dois passos sequenciais** para garantir análise completa:

```mermaid
graph TD
    A[Item Criado] --> B[Status 6: Em análise]
    B --> C[PASSO 1: Análise Inicial]
    C --> D{Informações Suficientes?}
    D -->|Sim| E[Status 6 + Triagem aprovada]
    D -->|Não| F[Status 7 + Triagem reprovada + Perguntas]
    F --> G[Cliente Responde]
    G --> H[Status 8: Perguntas respondidas]
    H --> I[PASSO 2: Reavaliação]
    I --> J{Informações Suficientes?}
    J -->|Sim| K[Status 8 + Triagem aprovada]
    J -->|Não| L[Status 8 + Falha na triagem]
    E --> M[Pronto para Classificação]
    K --> M
    L --> N[Fim - Não processa mais]
```

### 2. Passo 1 - Análise Inicial

**Critérios de Elegibilidade**:

- ✅ Empresa tem funcionalidade `status_triagem_diana` ativa no campo `funcoes_adicionais`
- ✅ Item no status **6** (Em análise)
- ✅ Campo `status_triagem_diana` = `"Pendente de triagem"`
- ✅ Permite campos vazios (função, aplicação, material_constitutivo)

**Processo**:

1. **Concatenação de Dados**: `descricao+funcao+aplicacao+material_constitutivo`
2. **Envio para API**: `POST https://hml-triagem-gt.becomex.com.br/process-item-descr/?item_descr=dados_concatenados`
3. **Análise IA**: DIANA verifica se dados respondem às 3 perguntas fundamentais
4. **Processamento do Resultado**:
   - **Se APROVADO**: Mantém status 6 + `status_triagem_diana` = `"Triagem aprovada"`
   - **Se REPROVADO**: Altera para status 7 + `status_triagem_diana` = `"Triagem reprovada"` + insere perguntas específicas

### 3. Passo 2 - Reavaliação com Respostas

**Critérios de Elegibilidade**:

- ✅ Empresa tem funcionalidade `status_triagem_diana` ativa
- ✅ Item no status **8** (Perguntas respondidas)
- ✅ Campo `status_triagem_diana` = `"Triagem reprovada"`
- ✅ Possui perguntas DIANA com respostas do cliente

**Processo**:

1. **Concatenação Expandida**: `descricao+funcao+aplicacao+material+pergunta1+resposta1+pergunta2+resposta2+pergunta3+resposta3`
2. **Envio para API**: Mesma URL, mas com dados expandidos incluindo respostas
3. **Reavaliação IA**: DIANA analisa dados originais + respostas do cliente
4. **Processamento do Resultado**:
   - **Se APROVADO**: Mantém status 8 + `status_triagem_diana` = `"Triagem aprovada"`
   - **Se REPROVADO**: Mantém status 8 + `status_triagem_diana` = `"Falha na triagem"` (**evita loop infinito**)

### 4. Estrutura da Resposta da API

**Formato JSON Padrão**:

```json
{
  "item_descr": "CHAPA ALUMINIO ALCLAD 2024-O GRAO F+Componente estrutural+Construção aeronáutica+Alumínio liga 2024",
  "questions": [
    {
      "question": "Qual a função do item (Para que ele serve?)",
      "falta_informacao": true
    },
    {
      "question": "Qual a aplicação do item? (Onde ele é utilizado?)",
      "falta_informacao": false
    },
    {
      "question": "Qual o material constitutivo do item?",
      "falta_informacao": false
    }
  ],
  "elapsed_time": "1.073 seconds"
}
```

**Interpretação**:

- `falta_informacao: true` = Pergunta não foi respondida adequadamente
- `falta_informacao: false` = Pergunta foi respondida adequadamente
- Se **qualquer** pergunta tem `falta_informacao: true` → Triagem REPROVADA
- Se **todas** as perguntas têm `falta_informacao: false` → Triagem APROVADA

## Instalação e Execução da Cron

### 1. Configurar Crontab

**Recomendação**: Execute a cada 30 minutos para balancear performance e responsividade.

```bash
# Editar crontab
crontab -e

# Adicionar linha (ajustar caminho conforme ambiente)
*/30 * * * * /usr/bin/php /var/www/gestaotarifaria/production/current/index.php cli/cron_analise_diana >> /var/log/diana_cron.log 2>&1
```

**Opções de Frequência**:

- `*/15 * * * *` - A cada 15 minutos (alta responsividade)
- `*/30 * * * *` - A cada 30 minutos (recomendado)
- `0 */1 * * *` - A cada hora (baixa carga)

### 2. Execução Manual

```bash
# Execução completa (processa ambos os passos)
php index.php cli/cron_analise_diana

# Teste de configuração
php index.php cli/cron_analise_diana test_config

# Teste de item específico
php index.php cli/cron_analise_diana test_item PARTNUMBER 123 001
```

### 3. Monitoramento da Execução

**Verificar se cron está rodando**:

```bash
# Ver logs da cron
tail -f /var/log/diana_cron.log

# Ver últimas execuções
grep "ANÁLISE DIANA" /var/log/diana_cron.log | tail -10

# Verificar processos ativos
ps aux | grep cron_analise_diana
```

## Testes e Validação

### 1. Teste de Configuração

```bash
php index.php cli/cron_analise_diana test_config
```

**Saída Esperada**:

```text
=== TESTE DE CONFIGURAÇÃO DIANA ===
✓ API DIANA está configurada
URL: https://hml-triagem-gt.becomex.com.br/process-item-descr/
Encontradas 3 empresas com funcionalidade ativa
Itens encontrados para análise: 15
Exemplo de item:
  Part Number: ITEM123
  Empresa: 456
  Função: Componente estrutural...
  Aplicação: Construção aeronáutica...
  Material: Alumínio liga 2024...
```

### 2. Teste de Item Específico

```bash
php index.php cli/cron_analise_diana test_item ITEM123 456 001
```

**Saída Esperada**:

```text
=== TESTE DE ITEM ESPECÍFICO ===
Part Number: ITEM123
Empresa: 456
Estabelecimento: 001
✓ Item encontrado
Status atual: 6
Status triagem DIANA: Pendente de triagem
✓ API respondeu com sucesso
Resposta: {
  "item_descr": "...",
  "questions": [...],
  "elapsed_time": "1.234 seconds"
}
```

### 3. Teste de Execução Completa

Execute a cron manualmente e verifique os logs:

```bash
php index.php cli/cron_analise_diana 2>&1 | tee teste_execucao.log
```

## Sistema de Logs e Monitoramento

### 1. Logs do Sistema CodeIgniter

**Localização**: `application/logs/log-[data].php`

**Conteúdo**: Erros gerais, exceções e informações do sistema

**Configuração**: Ajustar `$config['log_threshold']` no `config.php`

```php
$config['log_threshold'] = 3; // 0=Off, 1=Error, 2=Debug, 3=Info, 4=All
```

### 2. Logs Específicos DIANA

**Localização**: `application/logs/diana-[data].log`

**Conteúdo**: Logs detalhados da integração DIANA

**Exemplo de Entrada**:

```text
[2024-01-15 10:30:02] [info] DIANA API: Enviando dados para análise - Context: {"payload_size":156,"item_descr_length":89}
[2024-01-15 10:30:03] [info] DIANA API: Resposta recebida com sucesso - Context: {"response_size":234,"elapsed_time":"1.234 seconds"}
[2024-01-15 10:30:04] [error] DIANA API: Erro na requisição HTTP: Connection timeout - Context: {"exception_type":"RequestException","status_code":408}
```

### 3. Logs da Cron

**Localização**: Configurável no crontab (ex: `/var/log/diana_cron.log`)

**Conteúdo**: Progresso da execução, estatísticas e resultados

**Exemplo de Execução Completa**:

```text
[2024-01-15 10:30:01] === INICIANDO ANÁLISE DIANA ===
[2024-01-15 10:30:01] Data/Hora: 2024-01-15 10:30:01
[2024-01-15 10:30:01] API DIANA configurada: https://hml-triagem-gt.becomex.com.br/process-item-descr/
[2024-01-15 10:30:02] Encontradas 3 empresas com funcionalidade ativa
[2024-01-15 10:30:02] Encontrados 25 itens para análise na empresa ABC Ltda (ID: 123)
[2024-01-15 10:30:02]   - Pendentes (passo 1): 15
[2024-01-15 10:30:02]   - Com respostas (passo 2): 10

# PASSO 1 - Análise Inicial
[2024-01-15 10:30:03] Processando: ITEM001 (Empresa: 123, Estabelecimento: 001)
[2024-01-15 10:30:04]   ✓ Triagem aprovada
[2024-01-15 10:30:05] Processando: ITEM002 (Empresa: 123, Estabelecimento: 001)
[2024-01-15 10:30:06]   ✓ Triagem reprovada
[2024-01-15 10:30:06]     Perguntas inseridas: 2

# PASSO 2 - Reavaliação com Respostas
[2024-01-15 10:30:07] Processando item com respostas: ITEM003 (Empresa: 123, Estabelecimento: 001)
[2024-01-15 10:30:08]   ✓ Triagem aprovada (com respostas)
[2024-01-15 10:30:09] Processando item com respostas: ITEM004 (Empresa: 123, Estabelecimento: 001)
[2024-01-15 10:30:10]   ✓ Triagem falha na triagem (com respostas)
[2024-01-15 10:30:10]     Status final: Falha na triagem (evita loop infinito)

[2024-01-15 10:30:15] === ANÁLISE DIANA FINALIZADA ===
[2024-01-15 10:30:15] Empresas processadas: 3
[2024-01-15 10:30:15] Itens processados: 25
[2024-01-15 10:30:15] Sucessos: 23
[2024-01-15 10:30:15] Erros: 2
[2024-01-15 10:30:15] Tempo total: 14.23 segundos
[2024-01-15 10:30:15] Taxa de sucesso: 92.00%
```

### 4. Métricas de Monitoramento

**KPIs Principais**:

- **Taxa de Sucesso**: `(sucessos / total_processados) * 100`
- **Tempo Médio por Item**: `tempo_total / itens_processados`
- **Distribuição por Empresa**: Quantos itens por empresa
- **Taxa de Aprovação Passo 1**: Itens aprovados na análise inicial
- **Taxa de Aprovação Passo 2**: Itens aprovados após respostas
- **Taxa de Falha Definitiva**: Itens que chegaram ao status "Falha na triagem"

**Comandos de Monitoramento**:

```bash
# Ver estatísticas das últimas execuções
grep "Taxa de sucesso" /var/log/diana_cron.log | tail -10

# Contar itens processados hoje
grep "$(date +%Y-%m-%d)" /var/log/diana_cron.log | grep "Itens processados" | awk '{sum+=$4} END {print "Total hoje:", sum}'

# Ver erros recentes
grep "✗" /var/log/diana_cron.log | tail -20

# Monitorar execução em tempo real
tail -f /var/log/diana_cron.log
```

## Troubleshooting e Resolução de Problemas

### 1. Problemas Comuns e Soluções

#### **Nenhum item encontrado para análise**

**Sintomas**:
```text
Nenhuma empresa encontrada com funcionalidade status_triagem_diana ativa
Nenhum item encontrado para análise DIANA na empresa ABC (ID: 123)
```

**Soluções**:
1. **Verificar funcionalidade nas empresas**:
   ```sql
   SELECT id_empresa, nome_fantasia, funcoes_adicionais
   FROM empresa
   WHERE funcoes_adicionais LIKE '%status_triagem_diana%';
   ```

2. **Ativar funcionalidade se necessário**:
   ```sql
   UPDATE empresa
   SET funcoes_adicionais = CONCAT(IFNULL(funcoes_adicionais, ''), '|status_triagem_diana')
   WHERE id_empresa = 123;
   ```

3. **Verificar itens elegíveis**:
   ```sql
   -- Passo 1
   SELECT COUNT(*) FROM item
   WHERE id_status = 6 AND status_triagem_diana = 'Pendente de triagem';

   -- Passo 2
   SELECT COUNT(*) FROM item
   WHERE id_status = 8 AND status_triagem_diana = 'Triagem reprovada';
   ```

#### **Erro 422 - Field required**

**Sintomas**:
```text
✗ Falha na chamada da API
DIANA API: Erro na requisição HTTP: 422 - {"detail":[{"type":"missing","loc":["query","item_descr"],"msg":"Field required"}]}
```

**Causa**: API não está recebendo o parâmetro `item_descr` corretamente

**Soluções**:
1. **Verificar URL da API**:
   ```php
   // config.php - deve terminar com /
   $config['diana_api_url'] = 'https://hml-triagem-gt.becomex.com.br/process-item-descr/';
   ```

2. **Verificar concatenação de dados**:
   ```bash
   php index.php cli/cron_analise_diana test_item ITEM123 456 001
   ```

#### **Timeout ou erro de conectividade**

**Sintomas**:
```text
DIANA API: Erro na requisição HTTP: Connection timeout
DIANA API: Erro na requisição HTTP: Could not resolve host
```

**Soluções**:
1. **Testar conectividade**:
   ```bash
   curl -X POST "https://hml-triagem-gt.becomex.com.br/process-item-descr/?item_descr=teste" -H "accept: application/json"
   ```

2. **Verificar firewall e proxy**
3. **Aumentar timeout se necessário** (Diana_api.php linha ~132)

#### **Falha ao processar resultado**

**Sintomas**:
```text
✗ Falha ao processar resultado
DIANA: Erro ao processar item ITEM123: Resposta da API inválida
```

**Soluções**:
1. **Verificar estrutura da resposta**:
   ```bash
   # Adicionar var_dump temporário na Diana_api.php
   var_dump($resposta_api); exit();
   ```

2. **Verificar permissões de banco**:
   ```sql
   SHOW GRANTS FOR CURRENT_USER;
   ```

3. **Verificar logs detalhados**:
   ```bash
   tail -f application/logs/diana-$(date +%Y-%m-%d).log
   ```

### 2. Comandos de Diagnóstico

```bash
# Teste completo de configuração
php index.php cli/cron_analise_diana test_config

# Teste de item específico com debug
php index.php cli/cron_analise_diana test_item PARTNUMBER ID_EMPRESA ESTABELECIMENTO

# Verificar logs em tempo real
tail -f /var/log/diana_cron.log

# Verificar logs específicos DIANA
tail -f application/logs/diana-$(date +%Y-%m-%d).log

# Verificar logs de erro do sistema
tail -f application/logs/log-$(date +%Y-%m-%d).php

# Testar conectividade com API
curl -X POST "https://hml-triagem-gt.becomex.com.br/process-item-descr/?item_descr=teste+funcao+aplicacao+material" -H "accept: application/json"

# Verificar se cron está configurada
crontab -l | grep diana

# Verificar processos em execução
ps aux | grep cron_analise_diana
```

### 3. Checklist de Validação

**Antes de colocar em produção**:

- [ ] ✅ URL da API configurada corretamente
- [ ] ✅ Migration 259 executada (campo status_triagem_diana)
- [ ] ✅ Funcionalidade ativada nas empresas necessárias
- [ ] ✅ Teste de configuração passou
- [ ] ✅ Teste de item específico funcionou
- [ ] ✅ Crontab configurada com frequência adequada
- [ ] ✅ Logs configurados e acessíveis
- [ ] ✅ Monitoramento implementado
- [ ] ✅ Equipe treinada para troubleshooting

**Monitoramento contínuo**:

- [ ] ✅ Taxa de sucesso > 90%
- [ ] ✅ Tempo médio por item < 5 segundos
- [ ] ✅ Sem erros de conectividade frequentes
- [ ] ✅ Logs sem exceções críticas
- [ ] ✅ Itens sendo processados regularmente

---

## Resumo da Implementação

### ✅ **Funcionalidades Implementadas**

1. **Análise Automática em 2 Passos**
   - Passo 1: Análise inicial de itens pendentes
   - Passo 2: Reavaliação com respostas do cliente

2. **Integração Completa com API DIANA**
   - Concatenação de dados com separador '+'
   - Suporte a campos vazios
   - Query parameter na URL conforme especificação

3. **Prevenção de Loop Infinito**
   - Status "Falha na triagem" para itens que não passam na segunda análise
   - DIANA para de processar itens com falha definitiva

4. **Sistema Robusto de Logs**
   - Logs específicos DIANA
   - Logs de execução da cron
   - Logs de erro do sistema

5. **Monitoramento e Métricas**
   - Taxa de sucesso
   - Tempo de execução
   - Estatísticas por empresa
   - Distribuição por tipo de resultado

### 🔧 **Arquitetura Técnica**

- **Biblioteca Diana_api**: Gerencia comunicação com API
- **Métodos no Item_model**: Busca e processamento de dados
- **Cron independente**: Execução automática via crontab
- **Sistema de configuração**: URL e parâmetros centralizados
- **Tratamento de erros**: Robusto e com logs detalhados

### 📊 **Fluxo de Estados**

```text
Item Criado → Status 6 (Em análise) → DIANA Passo 1
    ↓
    ├─ Aprovado → Status 6 + "Triagem aprovada" → Pronto para classificação
    └─ Reprovado → Status 7 + "Triagem reprovada" + Perguntas → Cliente responde
        ↓
        Status 8 (Perguntas respondidas) → DIANA Passo 2
        ↓
        ├─ Aprovado → Status 8 + "Triagem aprovada" → Pronto para classificação
        └─ Reprovado → Status 8 + "Falha na triagem" → Fim (evita loop)
```

### 🚀 **Próximos Passos Sugeridos**

1. **Monitoramento Avançado**
   - Dashboard com métricas em tempo real
   - Alertas para taxa de erro alta
   - Relatórios de performance

2. **Otimizações**
   - Cache de respostas para itens similares
   - Processamento em lote para melhor performance
   - Retry automático para falhas temporárias

3. **Melhorias na Interface**
   - Visualização do status DIANA na interface do usuário
   - Histórico de análises por item
   - Feedback visual do progresso da triagem

### 📞 **Suporte e Manutenção**

**Contatos Técnicos**:
- Equipe de Desenvolvimento: [email/slack]
- Administrador do Sistema: [email/telefone]
- Suporte DIANA API: [contato da Becomex]

**Documentação Relacionada**:
- Manual do Usuário do Sistema de Gestão Tarifária
- Documentação da API DIANA
- Guia de Troubleshooting do CodeIgniter

**Versionamento**:
- Versão da Documentação: 1.0
- Data da Última Atualização: Janeiro 2024
- Responsável: Equipe de Desenvolvimento

---

*Esta documentação foi criada seguindo as melhores práticas de documentação técnica e deve ser atualizada sempre que houver mudanças na implementação ou na API DIANA.*
