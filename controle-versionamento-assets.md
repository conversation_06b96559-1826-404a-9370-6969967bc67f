
# Controle de Versionamento de Assets: Deploy e Ambiente Local

## Objetivo

Evitar problemas de cache de arquivos estáticos (JS/CSS) após deploys, garantindo que o navegador sempre carregue a versão mais recente dos assets.

## Motivo das Alterações

- **Problema:** Ap<PERSON> deploys, usuários continuavam vendo arquivos antigos devido ao cache do navegador.
- **Solução:** Implementar versionamento automático dos assets, alterando o valor de `assets_version` no arquivo `config.php` a cada deploy ou alteração local.
- **Benefício:** Toda vez que o valor muda, a URL dos arquivos estáticos muda, forçando o navegador a baixar a versão nova.

---

## O que foi feito


### 1. Centralização do versionamento

O valor de versionamento dos assets foi centralizado na linha:

```php
$config['assets_version'] = 1753894205;
```

no arquivo `application/config/config.php`.
O backend utiliza esse valor (timestamp Unix) para "cache busting" nas URLs dos arquivos estáticos.


### 2. Atualização automática no deploy (deploy.rb)

Adicionada uma task no Capistrano para atualizar automaticamente o valor de `assets_version` com o timestamp Unix do momento do deploy.

Exemplo de task:

```ruby
namespace :deploy do
  desc 'Atualiza o assets_version para forçar cache busting'
  task :update_assets_version do
    on roles(:app) do
      within release_path do
        execute :bash, "-c 'sed -i \"s/\\($config\\[\\'assets_version\\'\\] = \\).*/\\1#{Time.now.to_i};/\" application/config/config.php'"
      end
    end
  end
  after :publishing, :update_assets_version
end
```

**Resultado:** A cada deploy, o valor é atualizado automaticamente no servidor remoto.

echo "assets_version atualizado para $TIMESTAMP em $CONFIG_FILE"

### 3. Script para ambiente local (`update_assets_version.sh`)

O script bash está versionado no repositório e serve para desenvolvedores atualizarem o valor localmente, sempre que necessário, usando timestamp Unix.

Exemplo de script:

```bash
#!/bin/bash
# Atualiza o assets_version do config.php para o timestamp atual

CONFIG_FILE="application/config/config.php"

if [ ! -f "$CONFIG_FILE" ]; then
  echo "Arquivo $CONFIG_FILE não encontrado!"
  exit 1
fi

TIMESTAMP=$(date +%s)
sed -i "s/\(['\"]assets_version['\"]\] = \).*/\1$TIMESTAMP;/" "$CONFIG_FILE"

echo "assets_version atualizado para $TIMESTAMP em $CONFIG_FILE"
```

**Como usar:**

1. O script já está versionado no repositório como `update_assets_version.sh` na raiz do projeto.
2. Dê permissão de execução (apenas uma vez):

   ```sh
   chmod +x update_assets_version.sh
   ```

3. Execute sempre que quiser forçar o cache busting local:

   ```sh
   ./update_assets_version.sh
   ```

O valor será atualizado e, ao recarregar a página, o navegador buscará os arquivos novos.

---

## Como utilizar

### No Deploy (Capistrano)

O versionamento é automático, não é necessário ação manual.
Após cada deploy, o valor de `assets_version` será atualizado.

### No Ambiente Local

Sempre que fizer alterações em arquivos estáticos e quiser garantir que o navegador carregue a nova versão, execute:

```sh
./update_assets_version.sh
```

---


## Observações

- O valor agora está em formato timestamp Unix (`date +%s`), padrão para automação e compatibilidade.
- O importante é que o valor mude sempre que houver alteração relevante nos assets.

---

Dúvidas ou sugestões, entre em contato com o time de desenvolvimento.
